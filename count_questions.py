#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import PyPDF2
import os
import re
from collections import defaultdict

def count_questions_in_pdf(pdf_path):
    """
    Đếm số câu hỏi trong một file PDF
    Tìm pattern "Question #X of Y" để xác định tổng số câu hỏi
    """
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            # Đọc trang đầu tiên để tìm tổng số câu hỏi
            first_page = pdf_reader.pages[0]
            text = first_page.extract_text()
            
            # Tìm pattern "Question #X of Y"
            pattern = r'Question #\d+ of (\d+)'
            matches = re.findall(pattern, text)
            
            if matches:
                total_questions = int(matches[0])
                return total_questions
            
            # Nếu không tìm thấy pattern trên, thử đếm trực tiếp
            total_text = ""
            for page in pdf_reader.pages:
                total_text += page.extract_text()
            
            # Đế<PERSON> số lần xuất hiện của "Question #"
            question_pattern = r'Question #\d+'
            question_matches = re.findall(question_pattern, total_text)
            
            return len(set(question_matches))  # Dùng set để tránh đếm trùng
            
    except Exception as e:
        print(f"Lỗi khi đọc file {pdf_path}: {e}")
        return 0

def is_question_file(filename):
    """
    Kiểm tra xem file có phải là file câu hỏi không
    (không chứa từ "answer" và có đuôi .pdf)
    """
    filename_lower = filename.lower()
    return (filename_lower.endswith('.pdf') and 
            'answer' not in filename_lower)

def scan_directory_for_questions(root_dir):
    """
    Quét toàn bộ thư mục để đếm câu hỏi trong các file PDF
    """
    results = defaultdict(dict)
    total_questions = 0
    
    print("Đang quét thư mục để tìm file câu hỏi...")
    print("="*60)
    
    for folder_name in os.listdir(root_dir):
        folder_path = os.path.join(root_dir, folder_name)
        
        if os.path.isdir(folder_path):
            print(f"\n📁 Thư mục: {folder_name}")
            folder_total = 0
            
            for filename in os.listdir(folder_path):
                if is_question_file(filename):
                    file_path = os.path.join(folder_path, filename)
                    question_count = count_questions_in_pdf(file_path)
                    
                    results[folder_name][filename] = question_count
                    folder_total += question_count
                    
                    print(f"  📄 {filename}: {question_count} câu hỏi")
            
            results[folder_name]['_total'] = folder_total
            total_questions += folder_total
            print(f"  📊 Tổng thư mục {folder_name}: {folder_total} câu hỏi")
    
    return results, total_questions

def generate_report(results, total_questions):
    """
    Tạo báo cáo tổng hợp
    """
    print("\n" + "="*60)
    print("📊 BÁO CÁO TỔNG HỢP SỐ LƯỢNG CÂU HỎI")
    print("="*60)
    
    # Sắp xếp theo tên thư mục
    sorted_folders = sorted(results.keys())
    
    for folder_name in sorted_folders:
        folder_data = results[folder_name]
        folder_total = folder_data.get('_total', 0)
        
        print(f"\n📁 {folder_name}: {folder_total} câu hỏi")
        
        # Hiển thị chi tiết từng file
        for filename, count in folder_data.items():
            if filename != '_total':
                print(f"   • {filename}: {count} câu hỏi")
    
    print(f"\n🎯 TỔNG CỘNG: {total_questions} câu hỏi")
    print("="*60)

def main():
    """
    Hàm chính
    """
    current_dir = "."
    
    print("🚀 THUẬT TOÁN ĐẾM SỐ LƯỢNG CÂU HỎI CFA")
    print("Chỉ đếm file PDF không chứa từ 'answer'")
    print("="*60)
    
    # Quét và đếm câu hỏi
    results, total_questions = scan_directory_for_questions(current_dir)
    
    # Tạo báo cáo
    generate_report(results, total_questions)
    
    # Lưu kết quả vào file
    with open("question_count_report.txt", "w", encoding="utf-8") as f:
        f.write("BÁO CÁO SỐ LƯỢNG CÂU HỎI CFA II 2025\n")
        f.write("="*50 + "\n\n")
        
        for folder_name in sorted(results.keys()):
            folder_data = results[folder_name]
            folder_total = folder_data.get('_total', 0)
            
            f.write(f"{folder_name}: {folder_total} câu hỏi\n")
            
            for filename, count in folder_data.items():
                if filename != '_total':
                    f.write(f"  - {filename}: {count} câu hỏi\n")
            f.write("\n")
        
        f.write(f"TỔNG CỘNG: {total_questions} câu hỏi\n")
    
    print(f"\n💾 Báo cáo đã được lưu vào file: question_count_report.txt")

if __name__ == "__main__":
    main()
