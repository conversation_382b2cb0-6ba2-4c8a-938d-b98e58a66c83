#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import PyPDF2
import os
import re
import json
from collections import defaultdict
from datetime import datetime

class CFAQuestionAnalyzer:
    """
    Lớp phân tích số lượng câu hỏi CFA với các tính năng nâng cao
    """
    
    def __init__(self, root_directory="."):
        self.root_directory = root_directory
        self.results = defaultdict(dict)
        self.statistics = {}
        
    def is_question_file(self, filename):
        """Kiểm tra file có phải là file câu hỏi không"""
        filename_lower = filename.lower()
        return (filename_lower.endswith('.pdf') and 
                'answer' not in filename_lower)
    
    def extract_question_count(self, pdf_path):
        """
        Trích xuất số lượng câu hỏi từ file PDF
        Sử dụng nhiều phương pháp để đảm bảo độ ch<PERSON>h xác
        """
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # Phương pháp 1: Tìm pattern "Question #X of Y" trong trang đầu
                if len(pdf_reader.pages) > 0:
                    first_page_text = pdf_reader.pages[0].extract_text()
                    
                    # Pattern chính xác nhất
                    pattern1 = r'Question #\d+ of (\d+)'
                    matches1 = re.findall(pattern1, first_page_text)
                    
                    if matches1:
                        return int(matches1[0])
                
                # Phương pháp 2: Đếm tất cả các câu hỏi duy nhất
                all_text = ""
                for page in pdf_reader.pages[:5]:  # Chỉ đọc 5 trang đầu để tối ưu
                    all_text += page.extract_text()
                
                # Tìm tất cả các số câu hỏi
                question_numbers = set()
                pattern2 = r'Question #(\d+)'
                matches2 = re.findall(pattern2, all_text)
                
                for match in matches2:
                    question_numbers.add(int(match))
                
                if question_numbers:
                    return max(question_numbers)
                
                return 0
                
        except Exception as e:
            print(f"⚠️  Lỗi khi đọc {pdf_path}: {e}")
            return 0
    
    def analyze_folder(self, folder_path, folder_name):
        """Phân tích một thư mục cụ thể"""
        print(f"\n📁 Đang phân tích: {folder_name}")
        
        folder_data = {}
        folder_total = 0
        file_count = 0
        
        for filename in os.listdir(folder_path):
            if self.is_question_file(filename):
                file_path = os.path.join(folder_path, filename)
                question_count = self.extract_question_count(file_path)
                
                folder_data[filename] = {
                    'questions': question_count,
                    'file_size': os.path.getsize(file_path)
                }
                
                folder_total += question_count
                file_count += 1
                
                print(f"  📄 {filename}: {question_count} câu hỏi")
        
        folder_data['_metadata'] = {
            'total_questions': folder_total,
            'file_count': file_count,
            'avg_questions_per_file': round(folder_total / file_count, 1) if file_count > 0 else 0
        }
        
        return folder_data
    
    def run_analysis(self):
        """Chạy phân tích toàn bộ"""
        print("🚀 THUẬT TOÁN PHÂN TÍCH CÂU HỎI CFA NÂNG CAO")
        print("="*60)
        
        total_questions = 0
        total_files = 0
        
        # Quét tất cả thư mục
        for item in os.listdir(self.root_directory):
            item_path = os.path.join(self.root_directory, item)
            
            if os.path.isdir(item_path) and not item.startswith('.'):
                folder_data = self.analyze_folder(item_path, item)
                self.results[item] = folder_data
                
                metadata = folder_data['_metadata']
                total_questions += metadata['total_questions']
                total_files += metadata['file_count']
        
        # Tính toán thống kê tổng quan
        self.statistics = {
            'total_questions': total_questions,
            'total_files': total_files,
            'total_folders': len(self.results),
            'avg_questions_per_file': round(total_questions / total_files, 1) if total_files > 0 else 0,
            'analysis_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        return self.results, self.statistics
    
    def generate_detailed_report(self):
        """Tạo báo cáo chi tiết"""
        print("\n" + "="*60)
        print("📊 BÁO CÁO CHI TIẾT")
        print("="*60)
        
        # Sắp xếp theo số lượng câu hỏi giảm dần
        sorted_folders = sorted(
            self.results.items(), 
            key=lambda x: x[1]['_metadata']['total_questions'], 
            reverse=True
        )
        
        for folder_name, folder_data in sorted_folders:
            metadata = folder_data['_metadata']
            print(f"\n📁 {folder_name}")
            print(f"   📊 Tổng: {metadata['total_questions']} câu hỏi")
            print(f"   📄 Số file: {metadata['file_count']}")
            print(f"   📈 Trung bình: {metadata['avg_questions_per_file']} câu hỏi/file")
            
            # Top 3 file có nhiều câu hỏi nhất
            files = [(k, v) for k, v in folder_data.items() if k != '_metadata']
            files.sort(key=lambda x: x[1]['questions'], reverse=True)
            
            print("   🏆 Top files:")
            for i, (filename, file_data) in enumerate(files[:3]):
                print(f"      {i+1}. {filename}: {file_data['questions']} câu hỏi")
        
        # Thống kê tổng quan
        stats = self.statistics
        print(f"\n🎯 THỐNG KÊ TỔNG QUAN:")
        print(f"   📚 Tổng số câu hỏi: {stats['total_questions']}")
        print(f"   📄 Tổng số file: {stats['total_files']}")
        print(f"   📁 Tổng số thư mục: {stats['total_folders']}")
        print(f"   📈 Trung bình câu hỏi/file: {stats['avg_questions_per_file']}")
        print(f"   🕐 Thời gian phân tích: {stats['analysis_date']}")
    
    def save_json_report(self, filename="cfa_question_analysis.json"):
        """Lưu báo cáo dưới dạng JSON"""
        report_data = {
            'statistics': self.statistics,
            'folder_analysis': self.results
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 Báo cáo JSON đã được lưu: {filename}")
    
    def create_summary_table(self):
        """Tạo bảng tóm tắt dạng CSV"""
        csv_content = "Thư mục,Số câu hỏi,Số file,Trung bình câu hỏi/file\n"
        
        for folder_name, folder_data in self.results.items():
            metadata = folder_data['_metadata']
            csv_content += f"{folder_name},{metadata['total_questions']},{metadata['file_count']},{metadata['avg_questions_per_file']}\n"
        
        with open("cfa_summary.csv", 'w', encoding='utf-8') as f:
            f.write(csv_content)
        
        print("📊 Bảng tóm tắt CSV đã được tạo: cfa_summary.csv")

def main():
    """Hàm chính"""
    analyzer = CFAQuestionAnalyzer()
    
    # Chạy phân tích
    results, statistics = analyzer.run_analysis()
    
    # Tạo các báo cáo
    analyzer.generate_detailed_report()
    analyzer.save_json_report()
    analyzer.create_summary_table()
    
    print("\n✅ Hoàn thành phân tích!")

if __name__ == "__main__":
    main()
