#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import PyPDF2
import re
import json

def analyze_question_file(pdf_path):
    """Phân tích cấu trúc file câu hỏi"""
    print(f"Analyzing question file: {pdf_path}")
    
    with open(pdf_path, 'rb') as file:
        pdf_reader = PyPDF2.PdfReader(file)
        
        # Đọc 5 trang đầu để hiểu cấu trúc
        for page_num in range(min(5, len(pdf_reader.pages))):
            page = pdf_reader.pages[page_num]
            text = page.extract_text()
            
            print(f"\n=== PAGE {page_num + 1} ===")
            
            # Tìm các pattern câu hỏi
            patterns = {
                'single_question': r'Question #(\d+) of (\d+) Question ID: (\d+)',
                'question_range': r'Question #(\d+) - (\d+) of (\d+) Question ID: (\d+)',
                'answer_choices': r'[A-E]\)',
                'question_content': r'Question #\d+.*?(?=Question #|\Z)',
            }
            
            for pattern_name, pattern in patterns.items():
                matches = re.findall(pattern, text, re.DOTALL)
                if matches:
                    print(f"{pattern_name}: {len(matches)} matches")
                    if pattern_name in ['single_question', 'question_range']:
                        for match in matches[:3]:  # Show first 3 matches
                            print(f"  {match}")
            
            # Hiển thị một phần text để hiểu cấu trúc
            print(f"\nSample text (first 800 chars):")
            print(text[:800])
            print("...")

def analyze_answer_file(pdf_path):
    """Phân tích cấu trúc file đáp án"""
    print(f"\nAnalyzing answer file: {pdf_path}")
    
    with open(pdf_path, 'rb') as file:
        pdf_reader = PyPDF2.PdfReader(file)
        
        # Đọc 3 trang đầu
        for page_num in range(min(3, len(pdf_reader.pages))):
            page = pdf_reader.pages[page_num]
            text = page.extract_text()
            
            print(f"\n=== ANSWER PAGE {page_num + 1} ===")
            
            # Tìm pattern đáp án
            answer_patterns = {
                'question_number': r'Question #(\d+)',
                'correct_answer': r'The correct answer is ([A-E])',
                'explanation': r'Explanation:',
            }
            
            for pattern_name, pattern in answer_patterns.items():
                matches = re.findall(pattern, text)
                if matches:
                    print(f"{pattern_name}: {len(matches)} matches")
                    if pattern_name == 'correct_answer':
                        print(f"  Answers found: {matches[:5]}")
            
            print(f"\nSample answer text (first 600 chars):")
            print(text[:600])
            print("...")

# Test với file mẫu
question_file = "1. Quantitative Methods/Reading 1 Multiple Regression.pdf"
answer_file = "1. Quantitative Methods/Reading 1 Multiple Regression - Answers.pdf"

analyze_question_file(question_file)
analyze_answer_file(answer_file)
