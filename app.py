#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, jsonify, request, render_template, send_from_directory
from flask_cors import CORS
import os
import json
from pdf_parser import CFAQuestionParser
import glob

app = Flask(__name__)
CORS(app)

# Global variables để cache dữ liệu
current_questions = []
current_answers = {}
current_file_info = {}

class CFAExamApp:
    def __init__(self):
        self.parser = CFAQuestionParser()
        self.available_files = self.get_available_files()
    
    def get_available_files(self):
        """Lấy danh sách các file PDF có sẵn"""
        files = []
        
        for folder in os.listdir('.'):
            if os.path.isdir(folder) and not folder.startswith('.'):
                folder_path = os.path.join('.', folder)
                
                # Tìm các file câu hỏi (không chứa "answer")
                question_files = []
                for file in os.listdir(folder_path):
                    if file.endswith('.pdf') and 'answer' not in file.lower():
                        # Tìm file đáp án tương ứng
                        base_name = file.replace('.pdf', '')
                        answer_patterns = [
                            f"{base_name} - Answers.pdf",
                            f"{base_name} - Answer.pdf",
                            f"{base_name} Answers.pdf",
                            f"{base_name} Answer.pdf"
                        ]
                        
                        answer_file = None
                        for pattern in answer_patterns:
                            answer_path = os.path.join(folder_path, pattern)
                            if os.path.exists(answer_path):
                                answer_file = pattern
                                break
                        
                        if answer_file:
                            question_files.append({
                                'name': file.replace('.pdf', ''),
                                'question_file': os.path.join(folder, file),
                                'answer_file': os.path.join(folder, answer_file),
                                'folder': folder
                            })
                
                if question_files:
                    files.extend(question_files)
        
        return files
    
    def load_questions(self, question_file, answer_file):
        """Load câu hỏi và đáp án từ file"""
        try:
            questions, answers = self.parser.parse_files(question_file, answer_file)
            return questions, answers
        except Exception as e:
            print(f"Error loading files: {e}")
            return [], {}

exam_app = CFAExamApp()

@app.route('/')
def index():
    """Trang chủ"""
    return render_template('index.html')

@app.route('/api/files')
def get_files():
    """API lấy danh sách file"""
    return jsonify(exam_app.available_files)

@app.route('/api/load_exam', methods=['POST'])
def load_exam():
    """API load bài thi"""
    global current_questions, current_answers, current_file_info
    
    data = request.json
    question_file = data.get('question_file')
    answer_file = data.get('answer_file')
    
    if not question_file or not answer_file:
        return jsonify({'error': 'Missing file paths'}), 400
    
    # Load questions và answers
    questions, answers = exam_app.load_questions(question_file, answer_file)
    
    if not questions:
        return jsonify({'error': 'Failed to load questions'}), 500
    
    current_questions = questions
    current_answers = answers
    current_file_info = {
        'question_file': question_file,
        'answer_file': answer_file,
        'total_questions': len(questions)
    }
    
    return jsonify({
        'success': True,
        'total_questions': len(questions),
        'file_info': current_file_info
    })

@app.route('/api/question/<int:question_num>')
def get_question(question_num):
    """API lấy câu hỏi theo số thứ tự"""
    if not current_questions:
        return jsonify({'error': 'No exam loaded'}), 400
    
    # Tìm câu hỏi theo số thứ tự
    question = None
    for q in current_questions:
        if q['question_number'] == question_num:
            question = q
            break
    
    if not question:
        return jsonify({'error': 'Question not found'}), 404
    
    # Nếu là câu hỏi nhóm, tìm tất cả câu hỏi trong nhóm
    if question['is_group_question']:
        group_questions = []
        for q in current_questions:
            if q['context'] == question['context'] and q['context']:
                group_questions.append(q)
        
        return jsonify({
            'type': 'group',
            'context': question['context'],
            'questions': group_questions,
            'total_questions': current_file_info['total_questions']
        })
    else:
        return jsonify({
            'type': 'single',
            'question': question,
            'total_questions': current_file_info['total_questions']
        })

@app.route('/api/answer/<int:question_num>')
def get_answer(question_num):
    """API lấy đáp án"""
    if not current_answers:
        return jsonify({'error': 'No answers loaded'}), 400
    
    answer = current_answers.get(question_num)
    if not answer:
        return jsonify({'error': 'Answer not found'}), 404
    
    return jsonify(answer)

@app.route('/api/submit_answer', methods=['POST'])
def submit_answer():
    """API submit đáp án"""
    data = request.json
    question_num = data.get('question_num')
    user_answer = data.get('answer')
    
    if not question_num or not user_answer:
        return jsonify({'error': 'Missing question number or answer'}), 400
    
    # Lấy đáp án đúng
    correct_answer_data = current_answers.get(question_num, {})
    correct_answer = correct_answer_data.get('correct_answer')
    explanation = correct_answer_data.get('explanation', '')
    
    is_correct = user_answer.upper() == correct_answer
    
    return jsonify({
        'correct': is_correct,
        'correct_answer': correct_answer,
        'explanation': explanation,
        'user_answer': user_answer
    })

@app.route('/api/exam_info')
def get_exam_info():
    """API lấy thông tin bài thi hiện tại"""
    if not current_questions:
        return jsonify({'error': 'No exam loaded'}), 400
    
    return jsonify(current_file_info)

if __name__ == '__main__':
    # Tạo thư mục templates nếu chưa có
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    # Tạo thư mục static nếu chưa có
    if not os.path.exists('static'):
        os.makedirs('static')
    
    print("🚀 Starting CFA Exam App...")
    print(f"📚 Found {len(exam_app.available_files)} available exam files")
    
    app.run(debug=True, host='0.0.0.0', port=5001)
