#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import PyPDF2
import os
import re

def analyze_pdf_structure(pdf_path):
    """Phân tích cấu trúc của một file PDF để hiểu format câu hỏi"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            print(f"Analyzing: {pdf_path}")
            print(f"Number of pages: {len(pdf_reader.pages)}")
            print("="*50)
            
            # Đọc 3 trang đầu để hiểu cấu trúc
            for page_num in range(min(3, len(pdf_reader.pages))):
                page = pdf_reader.pages[page_num]
                text = page.extract_text()
                
                print(f"\n--- PAGE {page_num + 1} ---")
                print(text[:1000])  # In 1000 ký tự đầu
                print("...")
                
                # Tìm pattern câu hỏi
                question_patterns = [
                    r'\b\d+\.\s',  # 1. 2. 3. ...
                    r'Question\s+\d+',  # Question 1, Question 2
                    r'\b[A-Z]\.\s',  # A. B. C. ...
                ]
                
                for pattern in question_patterns:
                    matches = re.findall(pattern, text)
                    if matches:
                        print(f"Found pattern '{pattern}': {len(matches)} matches")
                        print(f"Examples: {matches[:5]}")
                
    except Exception as e:
        print(f"Error analyzing {pdf_path}: {e}")

# Test với một file mẫu
sample_file = "1. Quantitative Methods/Reading 1 Multiple Regression.pdf"
if os.path.exists(sample_file):
    analyze_pdf_structure(sample_file)
else:
    print(f"File not found: {sample_file}")
