<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CFA Level II Question Bank</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        
        .exam-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .question-card {
            background: white;
            border-radius: 10px;
            border-left: 5px solid #667eea;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .context-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            border-left: 5px solid #28a745;
        }
        
        .choice-btn {
            width: 100%;
            text-align: left;
            margin-bottom: 10px;
            padding: 15px;
            border: 2px solid #e9ecef;
            background: white;
            transition: all 0.3s ease;
        }
        
        .choice-btn:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .choice-btn.selected {
            border-color: #667eea;
            background-color: #667eea;
            color: white;
        }
        
        .choice-btn.correct {
            border-color: #28a745;
            background-color: #28a745;
            color: white;
        }
        
        .choice-btn.incorrect {
            border-color: #dc3545;
            background-color: #dc3545;
            color: white;
        }
        
        .explanation-card {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 5px solid #28a745;
        }
        
        .timer {
            font-size: 1.2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .progress-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
        }
        
        .navigation-buttons {
            position: sticky;
            bottom: 20px;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 -5px 15px rgba(0,0,0,0.1);
            margin-top: 30px;
        }
        
        .file-selector {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
        }
        
        .spinner-border {
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-graduation-cap"></i> CFA Level II Question Bank</h1>
                    <p class="mb-0">Hệ thống luyện thi CFA Level II 2025</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="timer" id="timer">00:00:00</div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- File Selector -->
        <div id="file-selector" class="file-selector">
            <h3><i class="fas fa-folder-open"></i> Chọn bài thi</h3>
            <div class="row">
                <div class="col-md-8">
                    <select id="exam-select" class="form-select form-select-lg">
                        <option value="">Đang tải danh sách bài thi...</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <button id="start-exam" class="btn btn-primary btn-lg w-100" disabled>
                        <i class="fas fa-play"></i> Bắt đầu làm bài
                    </button>
                </div>
            </div>
        </div>

        <!-- Progress Info -->
        <div id="progress-info" class="progress-info" style="display: none;">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <h5><i class="fas fa-book"></i> <span id="exam-title">Đang tải...</span></h5>
                </div>
                <div class="col-md-3">
                    <h6><i class="fas fa-question-circle"></i> Câu hỏi: <span id="current-question">0</span>/<span id="total-questions">0</span></h6>
                </div>
                <div class="col-md-3">
                    <h6><i class="fas fa-check-circle"></i> Đã làm: <span id="completed-questions">0</span></h6>
                </div>
                <div class="col-md-3">
                    <div class="progress">
                        <div id="progress-bar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Question Content -->
        <div id="question-content" style="display: none;">
            <!-- Context (for group questions) -->
            <div id="context-section" class="context-card" style="display: none;">
                <h5><i class="fas fa-info-circle"></i> Thông tin chung</h5>
                <div id="context-text"></div>
            </div>

            <!-- Question -->
            <div id="question-section" class="question-card">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <h5><i class="fas fa-question"></i> Câu hỏi <span id="question-number">1</span></h5>
                    <span id="question-id" class="badge bg-secondary"></span>
                </div>
                <div id="question-text" class="mb-4"></div>
                
                <!-- Choices -->
                <div id="choices-section"></div>
                
                <!-- Submit Button -->
                <div class="text-center mt-4">
                    <button id="submit-answer" class="btn btn-success btn-lg" disabled>
                        <i class="fas fa-check"></i> Xác nhận đáp án
                    </button>
                </div>
                
                <!-- Explanation -->
                <div id="explanation-section" style="display: none;">
                    <div class="explanation-card">
                        <h6><i class="fas fa-lightbulb"></i> Giải thích</h6>
                        <div id="explanation-text"></div>
                        <div class="mt-3">
                            <strong>Đáp án đúng: <span id="correct-answer" class="text-success"></span></strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div id="navigation" class="navigation-buttons" style="display: none;">
            <div class="row">
                <div class="col-md-4">
                    <button id="prev-question" class="btn btn-outline-primary w-100">
                        <i class="fas fa-chevron-left"></i> Câu trước
                    </button>
                </div>
                <div class="col-md-4 text-center">
                    <button id="question-list" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-list"></i> Danh sách câu hỏi
                    </button>
                </div>
                <div class="col-md-4">
                    <button id="next-question" class="btn btn-outline-primary w-100">
                        Câu tiếp <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Loading -->
        <div id="loading" class="loading" style="display: none;">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Đang tải...</span>
            </div>
            <p class="mt-3">Đang tải dữ liệu...</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/app.js"></script>
</body>
</html>
