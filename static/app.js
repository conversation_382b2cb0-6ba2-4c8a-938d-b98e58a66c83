// CFA Exam App JavaScript

class CFAExamApp {
    constructor() {
        this.currentQuestion = 1;
        this.totalQuestions = 0;
        this.completedQuestions = new Set();
        this.userAnswers = {};
        this.startTime = null;
        this.timerInterval = null;
        this.currentExamData = null;
        
        this.init();
    }
    
    init() {
        this.loadAvailableFiles();
        this.setupEventListeners();
    }
    
    async loadAvailableFiles() {
        try {
            const response = await fetch('/api/files');
            const files = await response.json();
            
            const select = document.getElementById('exam-select');
            select.innerHTML = '<option value="">Chọn bài thi...</option>';
            
            files.forEach(file => {
                const option = document.createElement('option');
                option.value = JSON.stringify({
                    question_file: file.question_file,
                    answer_file: file.answer_file
                });
                option.textContent = `${file.folder} - ${file.name}`;
                select.appendChild(option);
            });
            
            document.getElementById('start-exam').disabled = false;
        } catch (error) {
            console.error('Error loading files:', error);
            alert('Lỗi khi tải danh sách bài thi');
        }
    }
    
    setupEventListeners() {
        document.getElementById('start-exam').addEventListener('click', () => this.startExam());
        document.getElementById('submit-answer').addEventListener('click', () => this.submitAnswer());
        document.getElementById('prev-question').addEventListener('click', () => this.previousQuestion());
        document.getElementById('next-question').addEventListener('click', () => this.nextQuestion());
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key >= '1' && e.key <= '5') {
                const choices = ['A', 'B', 'C', 'D', 'E'];
                const choice = choices[parseInt(e.key) - 1];
                this.selectChoice(choice);
            } else if (e.key === 'Enter') {
                this.submitAnswer();
            } else if (e.key === 'ArrowLeft') {
                this.previousQuestion();
            } else if (e.key === 'ArrowRight') {
                this.nextQuestion();
            }
        });
    }
    
    async startExam() {
        const select = document.getElementById('exam-select');
        const selectedValue = select.value;
        
        if (!selectedValue) {
            alert('Vui lòng chọn bài thi');
            return;
        }
        
        const fileData = JSON.parse(selectedValue);
        
        this.showLoading(true);
        
        try {
            const response = await fetch('/api/load_exam', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(fileData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.totalQuestions = result.total_questions;
                this.currentExamData = result.file_info;
                
                // Ẩn file selector, hiện exam interface
                document.getElementById('file-selector').style.display = 'none';
                document.getElementById('progress-info').style.display = 'block';
                document.getElementById('question-content').style.display = 'block';
                document.getElementById('navigation').style.display = 'block';
                
                // Update exam title
                const examTitle = select.options[select.selectedIndex].text;
                document.getElementById('exam-title').textContent = examTitle;
                document.getElementById('total-questions').textContent = this.totalQuestions;
                
                // Start timer
                this.startTimer();
                
                // Load first question
                await this.loadQuestion(1);
            } else {
                alert('Lỗi khi tải bài thi: ' + result.error);
            }
        } catch (error) {
            console.error('Error starting exam:', error);
            alert('Lỗi khi bắt đầu bài thi');
        } finally {
            this.showLoading(false);
        }
    }
    
    async loadQuestion(questionNum) {
        this.showLoading(true);
        
        try {
            const response = await fetch(`/api/question/${questionNum}`);
            const data = await response.json();
            
            this.currentQuestion = questionNum;
            this.updateProgress();
            
            if (data.type === 'group') {
                this.displayGroupQuestion(data);
            } else {
                this.displaySingleQuestion(data.question);
            }
            
            // Reset UI state
            this.resetQuestionUI();
            
        } catch (error) {
            console.error('Error loading question:', error);
            alert('Lỗi khi tải câu hỏi');
        } finally {
            this.showLoading(false);
        }
    }
    
    displaySingleQuestion(question) {
        // Hide context section
        document.getElementById('context-section').style.display = 'none';
        
        // Display question
        document.getElementById('question-number').textContent = question.question_number;
        document.getElementById('question-id').textContent = `ID: ${question.question_id}`;
        document.getElementById('question-text').textContent = question.question_text;
        
        // Display choices
        this.displayChoices(question.choices);
    }
    
    displayGroupQuestion(data) {
        // Show context
        if (data.context) {
            document.getElementById('context-section').style.display = 'block';
            document.getElementById('context-text').textContent = data.context;
        }
        
        // Find current question in group
        const currentQ = data.questions.find(q => q.question_number === this.currentQuestion);
        if (currentQ) {
            this.displaySingleQuestion(currentQ);
        }
    }
    
    displayChoices(choices) {
        const choicesSection = document.getElementById('choices-section');
        choicesSection.innerHTML = '';
        
        Object.entries(choices).forEach(([letter, text]) => {
            const button = document.createElement('button');
            button.className = 'btn choice-btn';
            button.innerHTML = `<strong>${letter})</strong> ${text}`;
            button.onclick = () => this.selectChoice(letter);
            button.dataset.choice = letter;
            
            choicesSection.appendChild(button);
        });
    }
    
    selectChoice(choice) {
        // Remove previous selection
        document.querySelectorAll('.choice-btn').forEach(btn => {
            btn.classList.remove('selected');
        });
        
        // Select new choice
        const selectedBtn = document.querySelector(`[data-choice="${choice}"]`);
        if (selectedBtn) {
            selectedBtn.classList.add('selected');
            document.getElementById('submit-answer').disabled = false;
        }
    }
    
    async submitAnswer() {
        const selectedChoice = document.querySelector('.choice-btn.selected');
        if (!selectedChoice) {
            alert('Vui lòng chọn đáp án');
            return;
        }
        
        const userAnswer = selectedChoice.dataset.choice;
        
        try {
            const response = await fetch('/api/submit_answer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    question_num: this.currentQuestion,
                    answer: userAnswer
                })
            });
            
            const result = await response.json();
            
            // Store user answer
            this.userAnswers[this.currentQuestion] = userAnswer;
            this.completedQuestions.add(this.currentQuestion);
            
            // Show result
            this.showAnswerResult(result);
            
            // Update progress
            this.updateProgress();
            
        } catch (error) {
            console.error('Error submitting answer:', error);
            alert('Lỗi khi gửi đáp án');
        }
    }
    
    showAnswerResult(result) {
        // Disable submit button
        document.getElementById('submit-answer').disabled = true;
        
        // Update choice buttons
        document.querySelectorAll('.choice-btn').forEach(btn => {
            const choice = btn.dataset.choice;
            
            if (choice === result.correct_answer) {
                btn.classList.add('correct');
            } else if (choice === result.user_answer && !result.correct) {
                btn.classList.add('incorrect');
            }
            
            btn.disabled = true;
        });
        
        // Show explanation
        if (result.explanation) {
            document.getElementById('explanation-section').style.display = 'block';
            document.getElementById('explanation-text').textContent = result.explanation;
            document.getElementById('correct-answer').textContent = result.correct_answer;
        }
    }
    
    resetQuestionUI() {
        // Reset choice buttons
        document.querySelectorAll('.choice-btn').forEach(btn => {
            btn.classList.remove('selected', 'correct', 'incorrect');
            btn.disabled = false;
        });
        
        // Reset submit button
        document.getElementById('submit-answer').disabled = true;
        
        // Hide explanation
        document.getElementById('explanation-section').style.display = 'none';
        
        // If question was already answered, show the result
        if (this.completedQuestions.has(this.currentQuestion)) {
            const userAnswer = this.userAnswers[this.currentQuestion];
            if (userAnswer) {
                this.selectChoice(userAnswer);
                this.submitAnswer();
            }
        }
    }
    
    previousQuestion() {
        if (this.currentQuestion > 1) {
            this.loadQuestion(this.currentQuestion - 1);
        }
    }
    
    nextQuestion() {
        if (this.currentQuestion < this.totalQuestions) {
            this.loadQuestion(this.currentQuestion + 1);
        }
    }
    
    updateProgress() {
        document.getElementById('current-question').textContent = this.currentQuestion;
        document.getElementById('completed-questions').textContent = this.completedQuestions.size;
        
        const progress = (this.completedQuestions.size / this.totalQuestions) * 100;
        document.getElementById('progress-bar').style.width = `${progress}%`;
        
        // Update navigation buttons
        document.getElementById('prev-question').disabled = this.currentQuestion === 1;
        document.getElementById('next-question').disabled = this.currentQuestion === this.totalQuestions;
    }
    
    startTimer() {
        this.startTime = new Date();
        this.timerInterval = setInterval(() => {
            const now = new Date();
            const elapsed = now - this.startTime;
            
            const hours = Math.floor(elapsed / 3600000);
            const minutes = Math.floor((elapsed % 3600000) / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            
            const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('timer').textContent = timeString;
        }, 1000);
    }
    
    showLoading(show) {
        document.getElementById('loading').style.display = show ? 'block' : 'none';
    }
}

// Initialize app when page loads
document.addEventListener('DOMContentLoaded', () => {
    new CFAExamApp();
});
