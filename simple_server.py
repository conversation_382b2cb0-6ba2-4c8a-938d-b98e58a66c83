#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import http.server
import socketserver
import json
import urllib.parse
import os
from pdf_parser import CFAQuestionParser

# Global variables
current_questions = []
current_answers = {}
current_file_info = {}
parser = CFAQuestionParser()

class CFARequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.serve_index()
        elif self.path.startswith('/api/files'):
            self.serve_files_api()
        elif self.path.startswith('/api/question/'):
            question_num = int(self.path.split('/')[-1])
            self.serve_question_api(question_num)
        elif self.path.startswith('/api/answer/'):
            question_num = int(self.path.split('/')[-1])
            self.serve_answer_api(question_num)
        elif self.path.startswith('/api/exam_info'):
            self.serve_exam_info_api()
        else:
            super().do_GET()
    
    def do_POST(self):
        if self.path == '/api/load_exam':
            self.handle_load_exam()
        elif self.path == '/api/submit_answer':
            self.handle_submit_answer()
        else:
            self.send_error(404)
    
    def serve_index(self):
        try:
            with open('templates/index.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, "Index file not found")
    
    def serve_files_api(self):
        files = self.get_available_files()
        self.send_json_response(files)
    
    def serve_question_api(self, question_num):
        global current_questions
        
        if not current_questions:
            self.send_json_response({'error': 'No exam loaded'}, 400)
            return
        
        # Tìm câu hỏi
        question = None
        for q in current_questions:
            if q['question_number'] == question_num:
                question = q
                break
        
        if not question:
            self.send_json_response({'error': 'Question not found'}, 404)
            return
        
        # Kiểm tra xem có phải câu hỏi nhóm không
        if question['is_group_question']:
            group_questions = []
            for q in current_questions:
                if q['context'] == question['context'] and q['context']:
                    group_questions.append(q)
            
            response = {
                'type': 'group',
                'context': question['context'],
                'questions': group_questions,
                'total_questions': current_file_info['total_questions']
            }
        else:
            response = {
                'type': 'single',
                'question': question,
                'total_questions': current_file_info['total_questions']
            }
        
        self.send_json_response(response)
    
    def serve_answer_api(self, question_num):
        global current_answers
        
        if not current_answers:
            self.send_json_response({'error': 'No answers loaded'}, 400)
            return
        
        answer = current_answers.get(question_num)
        if not answer:
            self.send_json_response({'error': 'Answer not found'}, 404)
            return
        
        self.send_json_response(answer)
    
    def serve_exam_info_api(self):
        global current_file_info
        
        if not current_file_info:
            self.send_json_response({'error': 'No exam loaded'}, 400)
            return
        
        self.send_json_response(current_file_info)
    
    def handle_load_exam(self):
        global current_questions, current_answers, current_file_info
        
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))
        
        question_file = data.get('question_file')
        answer_file = data.get('answer_file')
        
        if not question_file or not answer_file:
            self.send_json_response({'error': 'Missing file paths'}, 400)
            return
        
        try:
            questions, answers = parser.parse_files(question_file, answer_file)
            
            if not questions:
                self.send_json_response({'error': 'Failed to load questions'}, 500)
                return
            
            current_questions = questions
            current_answers = answers
            current_file_info = {
                'question_file': question_file,
                'answer_file': answer_file,
                'total_questions': len(questions)
            }
            
            self.send_json_response({
                'success': True,
                'total_questions': len(questions),
                'file_info': current_file_info
            })
            
        except Exception as e:
            print(f"Error loading files: {e}")
            self.send_json_response({'error': str(e)}, 500)
    
    def handle_submit_answer(self):
        global current_answers
        
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))
        
        question_num = data.get('question_num')
        user_answer = data.get('answer')
        
        if not question_num or not user_answer:
            self.send_json_response({'error': 'Missing question number or answer'}, 400)
            return
        
        # Lấy đáp án đúng
        correct_answer_data = current_answers.get(question_num, {})
        correct_answer = correct_answer_data.get('correct_answer')
        explanation = correct_answer_data.get('explanation', '')
        
        is_correct = user_answer.upper() == correct_answer
        
        self.send_json_response({
            'correct': is_correct,
            'correct_answer': correct_answer,
            'explanation': explanation,
            'user_answer': user_answer
        })
    
    def get_available_files(self):
        """Lấy danh sách các file PDF có sẵn"""
        files = []
        
        for folder in os.listdir('.'):
            if os.path.isdir(folder) and not folder.startswith('.'):
                folder_path = os.path.join('.', folder)
                
                # Tìm các file câu hỏi (không chứa "answer")
                for file in os.listdir(folder_path):
                    if file.endswith('.pdf') and 'answer' not in file.lower():
                        # Tìm file đáp án tương ứng
                        base_name = file.replace('.pdf', '')
                        answer_patterns = [
                            f"{base_name} - Answers.pdf",
                            f"{base_name} - Answer.pdf",
                            f"{base_name} Answers.pdf",
                            f"{base_name} Answer.pdf"
                        ]
                        
                        answer_file = None
                        for pattern in answer_patterns:
                            answer_path = os.path.join(folder_path, pattern)
                            if os.path.exists(answer_path):
                                answer_file = pattern
                                break
                        
                        if answer_file:
                            files.append({
                                'name': file.replace('.pdf', ''),
                                'question_file': os.path.join(folder, file),
                                'answer_file': os.path.join(folder, answer_file),
                                'folder': folder
                            })
        
        return files
    
    def send_json_response(self, data, status_code=200):
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

def main():
    PORT = 8080
    
    # Tạo thư mục cần thiết
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    print("🚀 Starting CFA Exam Server...")
    
    # Đếm số file có sẵn
    handler = CFARequestHandler
    files = handler(None, None, None).get_available_files()
    print(f"📚 Found {len(files)} available exam files")
    
    with socketserver.TCPServer(("", PORT), CFARequestHandler) as httpd:
        print(f"🌐 Server running at http://localhost:{PORT}")
        print("📖 Open your browser and go to the URL above")
        httpd.serve_forever()

if __name__ == "__main__":
    main()
