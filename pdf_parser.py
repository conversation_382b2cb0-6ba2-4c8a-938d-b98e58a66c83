#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import PyPDF2
import re
import json
from typing import Dict, List, Tuple, Optional

class CFAQuestionParser:
    """Parser để extract câu hỏi và đáp án từ file PDF CFA"""
    
    def __init__(self):
        self.questions = []
        self.answers = {}
        
    def parse_question_file(self, pdf_path: str) -> List[Dict]:
        """Parse file câu hỏi và trả về danh sách câu hỏi"""
        questions = []
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            full_text = ""
            
            # Đọc toàn bộ file
            for page in pdf_reader.pages:
                full_text += page.extract_text() + "\n"
        
        # Tách các câu hỏi
        questions = self._extract_questions(full_text)
        return questions
    
    def parse_answer_file(self, pdf_path: str) -> Dict:
        """Parse file đáp án và trả về dictionary đáp án"""
        answers = {}
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            full_text = ""
            
            for page in pdf_reader.pages:
                full_text += page.extract_text() + "\n"
        
        answers = self._extract_answers(full_text)
        return answers
    
    def _extract_questions(self, text: str) -> List[Dict]:
        """Extract câu hỏi từ text"""
        questions = []
        
        # Pattern cho câu hỏi đơn lẻ
        single_pattern = r'Question #(\d+) of (\d+) Question ID: (\d+)\s*(.*?)(?=Question #|\Z)'
        
        # Pattern cho câu hỏi theo nhóm (có context chung)
        range_pattern = r'Question #(\d+) - (\d+) of (\d+) Question ID: (\d+)\s*(.*?)(?=Question #|\Z)'
        
        # Tìm context chung (text trước câu hỏi nhóm)
        context_pattern = r'(.*?)(?=Question #\d+ - \d+ of \d+)'
        
        # Tách text thành các phần
        parts = re.split(r'(?=Question #\d+)', text)
        
        current_context = ""
        
        for part in parts:
            if not part.strip():
                continue
                
            # Kiểm tra xem có phải là câu hỏi nhóm không
            range_match = re.search(range_pattern, part, re.DOTALL)
            if range_match:
                start_q, end_q, total_q, q_id, content = range_match.groups()
                
                # Tìm context trước câu hỏi nhóm
                context_match = re.search(r'^(.*?)Question #\d+ - \d+', part, re.DOTALL)
                if context_match:
                    current_context = context_match.group(1).strip()
                
                # Extract từng câu hỏi trong nhóm
                group_questions = self._extract_group_questions(
                    content, int(start_q), int(end_q), int(total_q), current_context
                )
                questions.extend(group_questions)
                
            else:
                # Câu hỏi đơn lẻ
                single_match = re.search(single_pattern, part, re.DOTALL)
                if single_match:
                    q_num, total_q, q_id, content = single_match.groups()
                    
                    question_data = self._parse_single_question(
                        content, int(q_num), int(total_q), q_id
                    )
                    if question_data:
                        questions.append(question_data)
                        
                # Reset context cho câu hỏi đơn lẻ
                current_context = ""
        
        return questions
    
    def _extract_group_questions(self, content: str, start_q: int, end_q: int, 
                                total_q: int, context: str) -> List[Dict]:
        """Extract câu hỏi trong một nhóm có context chung"""
        questions = []
        
        # Tách các câu hỏi con trong nhóm
        sub_questions = re.split(r'Question #(\d+) - \d+ of \d+ Question ID: (\d+)', content)
        
        for i in range(1, len(sub_questions), 3):
            if i + 1 < len(sub_questions):
                q_num = int(sub_questions[i])
                q_id = sub_questions[i + 1]
                q_content = sub_questions[i + 2] if i + 2 < len(sub_questions) else ""
                
                question_data = self._parse_single_question(
                    q_content, q_num, total_q, q_id, context
                )
                if question_data:
                    questions.append(question_data)
        
        return questions
    
    def _parse_single_question(self, content: str, q_num: int, total_q: int, 
                              q_id: str, context: str = "") -> Optional[Dict]:
        """Parse một câu hỏi đơn lẻ"""
        
        # Tách câu hỏi và các lựa chọn
        lines = content.strip().split('\n')
        question_text = ""
        choices = {}
        
        current_choice = None
        choice_text = ""
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Kiểm tra xem có phải là lựa chọn không (A), B), C), ...)
            choice_match = re.match(r'^([A-E])\)(.*)', line)
            if choice_match:
                # Lưu lựa chọn trước đó
                if current_choice:
                    choices[current_choice] = choice_text.strip()
                
                current_choice = choice_match.group(1)
                choice_text = choice_match.group(2)
            elif current_choice:
                # Tiếp tục text của lựa chọn hiện tại
                choice_text += " " + line
            else:
                # Text của câu hỏi
                question_text += " " + line
        
        # Lưu lựa chọn cuối cùng
        if current_choice:
            choices[current_choice] = choice_text.strip()
        
        if not question_text.strip() or not choices:
            return None
            
        return {
            'question_number': q_num,
            'total_questions': total_q,
            'question_id': q_id,
            'context': context,
            'question_text': question_text.strip(),
            'choices': choices,
            'is_group_question': bool(context)
        }
    
    def _extract_answers(self, text: str) -> Dict:
        """Extract đáp án từ file answer"""
        answers = {}

        # Làm sạch text
        text = re.sub(r'\s+', ' ', text)

        # Tách theo câu hỏi - pattern cải tiến
        question_pattern = r'Question #(\d+)(?:\s+of\s+\d+)?(?:\s+Question\s+ID:\s+\d+)?'
        question_blocks = re.split(question_pattern, text)

        for i in range(1, len(question_blocks), 2):
            if i + 1 < len(question_blocks):
                q_num = int(question_blocks[i])
                content = question_blocks[i + 1]

                # Tìm đáp án đúng với nhiều pattern khác nhau
                correct_answer = None
                explanation = ""

                # Các pattern tìm đáp án
                answer_patterns = [
                    r'The correct answer is ([A-E])\.',
                    r'The correct answer is ([A-E])\s',
                    r'Correct answer:\s*([A-E])',
                    r'Answer:\s*([A-E])',
                    r'([A-E])\s*is correct',
                    r'Choice\s*([A-E])\s*is correct'
                ]

                for pattern in answer_patterns:
                    match = re.search(pattern, content, re.IGNORECASE)
                    if match:
                        correct_answer = match.group(1).upper()
                        break

                # Nếu không tìm thấy pattern trên, thử tìm trong explanation
                if not correct_answer:
                    # Tìm explanation trước
                    exp_match = re.search(r'Explanation[:\s]*(.*?)(?=Question #|\Z)',
                                        content, re.DOTALL | re.IGNORECASE)
                    if exp_match:
                        exp_text = exp_match.group(1)
                        # Tìm đáp án trong explanation
                        for pattern in answer_patterns:
                            match = re.search(pattern, exp_text, re.IGNORECASE)
                            if match:
                                correct_answer = match.group(1).upper()
                                break

                # Tìm explanation
                explanation_patterns = [
                    r'Explanation[:\s]*(.*?)(?=Question #|\Z)',
                    r'Explanation[:\s]*(.*?)(?=\(Module|\Z)',
                ]

                for pattern in explanation_patterns:
                    explanation_match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
                    if explanation_match:
                        explanation = explanation_match.group(1).strip()
                        # Làm sạch explanation
                        explanation = re.sub(r'\s+', ' ', explanation)
                        break

                if correct_answer:
                    answers[q_num] = {
                        'correct_answer': correct_answer,
                        'explanation': explanation
                    }

        return answers
    
    def parse_files(self, question_file: str, answer_file: str) -> Tuple[List[Dict], Dict]:
        """Parse cả file câu hỏi và đáp án"""
        questions = self.parse_question_file(question_file)
        answers = self.parse_answer_file(answer_file)
        
        return questions, answers
    
    def save_to_json(self, questions: List[Dict], answers: Dict, output_file: str):
        """Lưu dữ liệu ra file JSON"""
        data = {
            'questions': questions,
            'answers': answers,
            'total_questions': len(questions)
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

# Test parser
if __name__ == "__main__":
    parser = CFAQuestionParser()
    
    question_file = "1. Quantitative Methods/Reading 1 Multiple Regression.pdf"
    answer_file = "1. Quantitative Methods/Reading 1 Multiple Regression - Answers.pdf"
    
    print("Parsing files...")
    questions, answers = parser.parse_files(question_file, answer_file)
    
    print(f"Extracted {len(questions)} questions")
    print(f"Extracted {len(answers)} answers")
    
    # Lưu ra file JSON để test
    parser.save_to_json(questions, answers, "test_questions.json")
    
    # Hiển thị một vài câu hỏi mẫu
    for i, q in enumerate(questions[:3]):
        print(f"\n--- Question {q['question_number']} ---")
        print(f"Context: {q['context'][:100]}..." if q['context'] else "No context")
        print(f"Question: {q['question_text'][:100]}...")
        print(f"Choices: {list(q['choices'].keys())}")
        if q['question_number'] in answers:
            print(f"Answer: {answers[q['question_number']]['correct_answer']}")
