{"questions": [{"question_number": 1, "total_questions": 144, "question_id": "1586011", "context": "", "question_text": "An analyst is estimating whether company sales is related to three economic variables. The regression exhibits conditional heteroskedasticity, serial correlation, and multicollinearity. The analyst uses <PERSON> and <PERSON>ey-<PERSON> corrected standard errors. Which of the following is most accurate?", "choices": {"A": "The regression will still exhibit heteroskedasticity and multicollinearity, but the serial correlation problem will be solved.", "B": "The regression will still exhibit multicollinearity, but the heteroskedasticity and serial correlation problems will be solved.", "C": "The regression will still exhibit serial correlation and multicollinearity, but the heteroskedasticity problem will be solved."}, "is_group_question": false}, {"question_number": 2, "total_questions": 144, "question_id": "1471868", "context": "", "question_text": "Consider the following estimated regression equation, with the standard errors of the slope coefficients as noted: Salesi = 10.0 + 1.25 R&Di + 1.0 ADVi – 2.0 COMPi + 8.0 CAPi where the standard error for the estimated coefficient on R&D is 0.45, the standard error for the estimated coefficient on ADV is 2.2 , the standard error for the estimated coefficient on COMP is 0.63, and the standard error for the estimated coefficient on CAP is 2.5. The equation was estimated over 40 companies. Using a 5% level of significance, which of the estimated coefficients are significantly different from zero?", "choices": {"A": "ADV and CAP only.", "B": "R&D, ADV, COMP, and CAP.", "C": "R&D, COMP, and CAP only."}, "is_group_question": false}, {"question_number": 3, "total_questions": 144, "question_id": "1479906", "context": "", "question_text": "Which of the following is least likely a method used to detect heteroskedasticity?", "choices": {"A": "Scatter plot.", "B": "Breusch-Pagan test.", "C": "Breusch-Godfrey test."}, "is_group_question": false}, {"question_number": 4, "total_questions": 144, "question_id": "1479913", "context": "", "question_text": "One of the main assumptions of a multiple regression model is that the variance of the residuals is constant across all observations in the sample. A violation of the assumption is most likely to be described as:", "choices": {"A": "unstable remnant deviation.", "B": "positive serial correlation.", "C": "heteroskedasticity."}, "is_group_question": false}, {"question_number": 5, "total_questions": 144, "question_id": "1479918", "context": "", "question_text": "During the course of a multiple regression analysis, an analyst has observed several items that she believes may render incorrect conclusions. For example, the coefficient standard errors are too small, although the estimated coefficients are accurate. She believes that these small standard error terms will result in the computed t-statistics being too big, resulting in too many Type I errors. The analyst has most likely observed which of the following assumption violations in her regression analysis?", "choices": {"A": "Positive serial correlation.", "B": "Homoskedasticity.", "C": "Multicollinearity. In preparing an analysis of HB Inc., <PERSON> is asked to look at the company's sales in relation to broad based economic indicators. <PERSON><PERSON><PERSON>'s analysis indicates that HB's monthly sales are related to changes in housing starts (H) and changes in the mortgage interest rate (M). The analysis covers the past ten years for these variables. The regression equation is: S = 1.76 + 0.23H - 0.08M Number of observations:123 Unadjusted R2: 0.77 F statistic: 9.80 <PERSON><PERSON><PERSON> statistic 0.50 p-value of Housing Starts0.017 p=value of Mortgage Rates0.033 Variable Descriptions S = HB Sales (in thousands) H = housing starts (in thousands) M = mortgage interest rate (in percent) November 20x6 Actual Data HB's monthly sales: $55,000 Housing starts: 150,000 Mortgage interest rate (%): 7.5"}, "is_group_question": false}, {"question_number": 12, "total_questions": 144, "question_id": "1586006", "context": "", "question_text": "Consider the following estimated regression equation: AUTOt = 10.0 + 1.25 PIt + 1.0 TEENt – 2.0 INSt The equation was estimated over 40 companies. The predicted value of AUTO if PI is 4, TEEN is 0.30, and INS = 0.6 is closest to:", "choices": {"A": "14.90.", "B": "14.10.", "C": "17.50. <PERSON><PERSON>, CFA, is investigating the application of the Fama-French three-factor model (Model 1) for the Indian stock market for the period 2001–2011 (120 months). Using the dependent variable as annualized return (%), the results of the analysis are shown in  Indian Equities— Fama-French Model Indian Equities—Fama-French Model FactorCoefficientP-ValueVIF Intercept1.22<0.001 SMB 0.23<0.0013 HML 0.340.0033 Rm-Rf0.88<0.0012 R-squared 0.36 SSE 38.00 BG (lag 1) 2.11 BG (lag 2) 1.67 Partial F-Table (5% Level of Significance) Degrees of Freedom DenominatorDegrees of Freedom Numerator 1 2 3 112 3.93 3.08 2.69 113 3.93 3.08 2.68 114 3.92 3.08 2.68 115 3.92 3.08 2.68 116 3.92 3.07 2.68 117 3.92 3.07 2.68 Partial Chi-Square Table (5% Level of Significance) Degrees of FreedomCritical Value 1 3.84 2 5.99 3 7.81 4 9.49 5 11.07 6 12.59"}, "is_group_question": false}, {"question_number": 17, "total_questions": 144, "question_id": "1479903", "context": "", "question_text": "When constructing a regression model to predict portfolio returns, an analyst runs a regression for the past five year period. After examining the results, she determines that an increase in interest rates two years ago had a significant impact on portfolio results for the time of the increase until the present. By performing a regression over two separate time periods, the analyst would be attempting to prevent which type of misspecification?", "choices": {"A": "Incorrectly pooling data.", "B": "Inappropriate variable scaling.", "C": "Inappropriate variable form. Autumn <PERSON><PERSON><PERSON> is attempting to forecast sales for Brookfield Farms based on a multiple regression model. <PERSON><PERSON><PERSON> has constructed the following model: sales = b0 + (b1 × CPI) + (b2 × IP) + (b3 × GDP) + εt Where: sales = $ change in sales (in 000's) CPI = change in the consumer price index IP = change in industrial production (millions) GDP = change in GDP (millions) All changes in variables are in percentage terms. <PERSON><PERSON><PERSON> uses monthly data from the previous 180 months of sales data and for the independent variables. The model estimates (with coefficient standard errors in parentheses) are: SALES =10.2+ (4.6 × CPI)+ (5.2 × IP)+ (11.7 × GDP) p-value0.0010.17 0.11 0.09 The sum of squared errors is 140.3 and the total sum of squares is 368.7. <PERSON><PERSON><PERSON> is concerned that one or more of the assumptions underlying multiple regression has been violated in her analysis. In a conversation with <PERSON>, CFA, a colleague who is considered by many in the firm to be a quant specialist, <PERSON><PERSON><PERSON> says, \"It is my understanding that there are five assumptions of a multiple regression model:\" Assumption 1:There is a linear relationship between the dependent and independent variables. Assumption 2:The independent variables are not random, and there is zero correlation between any two of the independent variables. Assumption 3:The residual term is normally distributed with an expected value of zero. Assumption 4:The residuals are serially correlated. Assumption 5:The variance of the residuals is constant. <PERSON><PERSON><PERSON> agrees with <PERSON>'s assessment of the assumptions of multiple regression. Voiku tests and fails to reject each of the following four null hypotheses at the 99% confidence interval: Hypothesis 1:The coefficient on GDP is negative. Hypothesis 2:The intercept term is equal to –4. Hypothesis 3:A 2.6% increase in the CPI will result in an increase in sales of more than 12.0%. Hypothesis 4:A 1% increase in industrial production will result in a 1% decrease in sales. Figure 1: Partial F-Table critical values for right-hand tail area equal to 0.05 df1 = 1 df1 = 3 df1 = 5 df2 = 170 3.90 2.66 2.27 df2 = 176 3.89 2.66 2.27 df2 = 180 3.89 2.65 2.26"}, "is_group_question": false}, {"question_number": 21, "total_questions": 144, "question_id": "1586002", "context": "", "question_text": "Which of the following statements regarding the R2 is least accurate?", "choices": {"A": "The R2 of a regression will be greater than or equal to the adjusted-R2 for the same regression.", "B": "R2 is the coe\u0000cient of determination of the regression.", "C": "The R2 is the ratio of the unexplained variation to the explained variation of the dependent variable. In preparing an analysis of Treefell Company, <PERSON> is asked to look at the company's sales in relation to broad-based economic indicators. <PERSON><PERSON>'s analysis indicates that Treefell's monthly sales are related to changes in housing starts (H) and changes in the mortgage interest rate (M). The analysis covers the past 10 years for these variables. The regression equation is: S = 1.76 + 0.23H – 0.08M Number of observations:123 Unadjusted R2: 0.77 F-statistic: 9.80 Durbin-Watson statistic:0.50 p-value of Housing Starts:0.017 t-stat of Mortgage Rates:–2.6 Variable Descriptions S = Treefell Sales (in thousands) H = housing starts (in thousands) M = mortgage interest rate (in percent) November 20X6 Actual Data Treefell's monthly sales:$55,000 Housing starts: 150,000 Mortgage interest rate (%):7.5 Partial Chi-Square Table (5% Level of Significance) Degrees of FreedomCritical Value 1 3.84 2 5.99 3 7.81 4 9.49 5 11.07 6 12.59"}, "is_group_question": false}, {"question_number": 39, "total_questions": 144, "question_id": "1479867", "context": "", "question_text": "<PERSON>, CFA, wants to check for seasonality in monthly stock returns (i.e., the January effect) after controlling for market cap and systematic risk. The type of model that Fye would most appropriately select is:", "choices": {"A": "Neither multiple regression nor logistic regression.", "B": "Multiple regression model.", "C": "logistic regression model."}, "is_group_question": false}, {"question_number": 40, "total_questions": 144, "question_id": "1630876", "context": "", "question_text": "One choice a researcher can use to test for nonstationarity is to use a:", "choices": {"A": "Breusch-Pagan test, which uses a modi\u0000ed t-statistic.", "B": "<PERSON><PERSON><PERSON><PERSON> test, which uses a modi\u0000ed t-statistic.", "C": "<PERSON>ey-<PERSON> test, which uses a modi\u0000ed χ2 statistic. <PERSON>, CFA, has determined that commercial electric generator sales in the Midwest U.S. for Self-Start Company is a function of several factors in each area: the cost of heating oil, the temperature, snowfall, and housing starts. Using data for the most currently available year, she runs a cross-sectional regression where she regresses the deviation of sales from the historical average in each area on the deviation of each explanatory variable from the historical average of that variable for that location. She feels this is the most appropriate method since each geographic area will have different average values for the inputs, and the model can explain how current conditions explain how generator sales are higher or lower from the historical average in each area. In summary, she regresses current sales for each area minus its respective historical average on the following variables for each area. The difference between the retail price of heating oil and its historical average. The mean number of degrees the temperature is below normal in Chicago. The amount of snowfall above the average. The percentage of housing starts above the average. <PERSON> used a sample of 26 observations obtained from 26 metropolitan areas in the Midwest U.S. The results are in the tables below. The dependent variable is in sales of generators in millions of dollars. Coefficient Estimates Table Variable Estimated Coefficient<PERSON><PERSON><PERSON><PERSON>r of the Coefficient Intercept 5.00 1.850 $ Heating Oil 2.00 0.827 Low Temperature 3.00 1.200 Snowfall 10.00 4.833 Housing Starts 5.00 2.333 Analysis of Variance Table (ANOVA) Source Degrees of Freedom Sum of SquaresMean Square Regression 4 335.20 83.80 Error 21 606.40 28.88 Total 25 941.60 Table of the F-Distribution Critical values for right-hand tail area equal to 0.05 Numerator: df1 and Denominator: df2 df1 df2 1 2 41020 1161.45199.50224.58241.88248.01 218.51319.00019.24719.39619.446 47.70866.94436.38825.96445.8025 104.96464.10283.47802.97822.7740 204.35123.49282.86612.34792.1242 One of her goals is to forecast the sales of the Chicago metropolitan area next year. For that area and for the upcoming year, Williams obtains the following projections: heating oil prices will be $0.10 above average, the temperature in Chicago will be 5 degrees below normal, snowfall will be 3 inches above average, and housing starts will be 3% below average. In addition to making forecasts and testing the significance of the estimated coefficients, she plans to perform diagnostic tests to verify the validity of the model's results."}, "is_group_question": false}, {"question_number": 46, "total_questions": 144, "question_id": "1479901", "context": "", "question_text": "A multiple regression model has included independent variables that are not linearly related to the dependent variable. The model is most likely misspecified due to:", "choices": {"A": "incorrect data pooling.", "B": "incorrect variable form.", "C": "incorrect variable scaling."}, "is_group_question": false}, {"question_number": 47, "total_questions": 144, "question_id": "1471870", "context": "", "question_text": "Consider the following regression equation: Salesi = 10.0 + 1.25 R&Di + 1.0 ADVi – 2.0 COMPi + 8.0 CAPi where Sales is dollar sales in millions, R&D is research and development expenditures in millions, ADV is dollar amount spent on advertising in millions, COMP is the number of competitors in the industry, and CAP is the capital expenditures for the period in millions of dollars. Which of the following is NOT a correct interpretation of this regression information?", "choices": {"A": "If R&D and advertising expenditures are $1 million each, there are 5 competitors, and capital expenditures are $2 million, expected Sales are $8.25 million.", "B": "If a company spends $1 million more on capital expenditures (holding everything else constant), Sales are expected to increase by $8.0 million.", "C": "One more competitor will mean $2 million less in Sales (holding everything else constant)."}, "is_group_question": false}, {"question_number": 48, "total_questions": 144, "question_id": "1479883", "context": "", "question_text": "One possible problem that could jeopardize the validity of the employment growth rate model is multicollinearity. Which of the following would most likely suggest the existence of multicollinearity?", "choices": {"A": "The variance of the observations has increased over time.", "B": "The <PERSON><PERSON><PERSON><PERSON> statistic is signi\u0000cant.", "C": "The F-statistic suggests that the overall regression is signi\u0000cant, however the regression coe\u0000cients are not individually signi\u0000cant."}, "is_group_question": false}, {"question_number": 49, "total_questions": 144, "question_id": "1586005", "context": "", "question_text": "Consider the following estimated regression equation: Salesi = 10.0 + 1.25 R&Di + 1.0 ADVi − 2.0 COMPi + 8.0 CAPi Sales are in millions of dollars. An analyst is given the following predictions on the independent variables: R&D = 5, ADV = 4, COMP = 10, and CAP = 40. The predicted level of sales is closest to:", "choices": {"A": "$310.25 million.", "B": "$300.25 million.", "C": "$320.25 million."}, "is_group_question": false}, {"question_number": 50, "total_questions": 144, "question_id": "1479902", "context": "", "question_text": "When pooling the samples over multiple economic environments in a multiple regression model, which of the following errors is most likely to occur?", "choices": {"A": "Model misspeci\u0000cation.", "B": "Heteroskedasticity.", "C": "Multicollinearity."}, "is_group_question": false}, {"question_number": 51, "total_questions": 144, "question_id": "1472067", "context": "", "question_text": "An analyst runs a regression of portfolio returns on three independent variables. These independent variables are price-to-sales (P/S), price-to-cash flow (P/CF), and price-to-book (P/B). The analyst discovers that the p-values for each independent variable are relatively high. However, the F-test has a very small p-value. The analyst is puzzled and tries to figure out how the F-test can be statistically significant when the individual independent variables are not significant. What violation of regression analysis has occurred?", "choices": {"A": "multicollinearity.", "B": "conditional heteroskedasticity.", "C": "serial correlation."}, "is_group_question": false}, {"question_number": 52, "total_questions": 144, "question_id": "1472012", "context": "", "question_text": "Which of the following statements regarding heteroskedasticity is least accurate?", "choices": {"A": "Heteroskedasticity may occur in cross-sectional or time-series analyses.", "B": "Heteroskedasticity results in an estimated variance that is too small and, therefore, a\u0000ects statistical inference.", "C": "The assumption of linear regression is that the residuals are heteroskedastic."}, "is_group_question": false}, {"question_number": 53, "total_questions": 144, "question_id": "1586003", "context": "", "question_text": "<PERSON> estimated a regression that produced the following analysis of variance (ANOVA) table: Source Sum of squares Degrees of freedom Mean square Regression 20 1 20 Error 80 40 2 Total 100 41 The values of R2 and the F-statistic for joint test of significance of all the slope coefficients are:", "choices": {"A": "R2 = 0.25 and F = 0.909.", "B": "R2 = 0.20 and F = 10.", "C": "R2 = 0.25 and F = 10. A real estate agent wants to develop a model to predict the selling price of a home. The agent believes that the most important variables in determining the price of a house are its size (in square feet) and the number of bedrooms. Accordingly, he takes a random sample of 32 homes that has recently been sold. The results of the regression are: Coefficient Standard Error t-statistics Intercept 66,500 59,292 1.12 House Size 74.30 21.11 3.52 Number of Bedrooms 10306 3230 3.19 R2 = 0.56; F = 40.73 Selected F- table values for significance level of 0.05: 1 2 28 4.20 3.34 29 4.18 3.33 30 4.17 3.32 32 4.15 3.29 (Degrees of freedom for the numerator in columns; Degrees of freedom for the denominator in rows) Additional information regarding this multiple regression: 1. Variance of error is not constant across the 32 observations. 2. The two variables (size of the house and the number of bedrooms) are highly correlated. 3. The error variance is not correlated with the size of the house nor with the number of bedrooms."}, "is_group_question": false}, {"question_number": 57, "total_questions": 144, "question_id": "1471872", "context": "", "question_text": "<PERSON>, CFA, is undertaking an analysis of the bicycle industry. He hypothesizes that bicycle sales (SALES) are a function of three factors: the population under 20 (POP), the level of disposable income (INCOME), and the number of dollars spent on advertising (ADV). All data are measured in millions of units. <PERSON> gathers data for the last 20 years and estimates the following equation (standard errors in parentheses): SALES= α+ 0.004 POP+ 1.031 INCOME + 2.002 ADV (0.005) (0.337) (2.312) The critical t-statistic for a 95% confidence level is 2.120. Which of the independent variables is statistically different from zero at the 95% confidence level?", "choices": {"A": "INCOME and ADV.", "B": "ADV only.", "C": "INCOME only."}, "is_group_question": false}, {"question_number": 58, "total_questions": 144, "question_id": "1489310", "context": "", "question_text": "<PERSON>, CFA, is evaluating a regression analysis recently published in a trade journal that hypothesizes that the annual performance of the S&P 500 stock index can be explained by movements in the Federal Funds rate and the U.S. Producer Price Index (PPI). Which of the following statements regarding his analysis is most accurate?", "choices": {"A": "If the p-value of a variable is less than the signi\u0000cance level, the null hypothesis can be rejected.", "B": "If the t-value of a variable is less than the signi\u0000cance level, the null hypothesis should be rejected.", "C": "If the p-value of a variable is less than the signi\u0000cance level, the null hypothesis cannot be rejected."}, "is_group_question": false}, {"question_number": 59, "total_questions": 144, "question_id": "1472026", "context": "", "question_text": "Which of the following statements regarding serial correlation that might be encountered in regression analysis is least accurate?", "choices": {"A": "Serial correlation occurs least often with time series data.", "B": "Serial correlation does not a\u0000ect consistency of regression coe\u0000cients.", "C": "Positive serial correlation and heteroskedasticity can both lead to Type I errors."}, "is_group_question": false}, {"question_number": 60, "total_questions": 144, "question_id": "1479923", "context": "", "question_text": "Assume that in a particular multiple regression model, it is determined that the error terms are uncorrelated with each other. Which of the following statements is most accurate?", "choices": {"A": "Serial correlation may be present in this multiple regression model, and can be con\u0000rmed only through a <PERSON><PERSON><PERSON><PERSON><PERSON> test.", "B": "This model is in accordance with the basic assumptions of multiple regression analysis because the errors are not serially correlated.", "C": "Unconditional heteroskedasticity present in this model should not pose a problem, but can be corrected by using robust standard errors."}, "is_group_question": false}, {"question_number": 61, "total_questions": 144, "question_id": "1471980", "context": "", "question_text": "An analyst runs a regression of monthly value-stock returns on five independent variables over 48 months. The total sum of squares is 430, and the sum of squared errors is 170. Test the null hypothesis at the 2.5% and 5% significance level that all five of the independent variables are equal to zero.", "choices": {"A": "Rejected at 5% signi\u0000cance only.", "B": "Rejected at 2.5% signi\u0000cance and 5% signi\u0000cance.", "C": "Not rejected at 2.5% or 5.0% signi\u0000cance."}, "is_group_question": false}, {"question_number": 62, "total_questions": 144, "question_id": "1479874", "context": "", "question_text": "<PERSON> estimated a regression that produced the following analysis of variance (ANOVA) table: Source Sum of squaresDegrees of freedom Mean square Regression 100 1 100.0 Error 300 40 7.5 Total 400 41 The values of R2 and the F-statistic to test the null hypothesis that slope coefficients on all variables are equal to zero are:", "choices": {"A": "R2 = 0.20 and F = 13.333.", "B": "R2 = 0.25 and F = 0.930.", "C": "R2 = 0.25 and F = 13.333. <PERSON>, CFA, works for ABC Capital, a large money management company based in New York. <PERSON> has several years of experience as a financial analyst, but is currently working in the marketing department developing materials to be used by ABC's sales team for both existing and prospective clients. ABC Capital's client base consists primarily of large net worth individuals and Fortune 500 companies. ABC invests its clients' money in both publicly traded mutual funds as well as its own investment funds that are managed in-house. Five years ago, roughly half of its assets under management were invested in the publicly traded mutual funds, with the remaining half in the funds managed by ABC's investment team. Currently, approximately 75% of ABC's assets under management are invested in publicly traded funds, with the remaining 25% being distributed among ABC's private funds. The managing partners at ABC would like to shift more of its client's assets away from publicly traded funds into ABC's proprietary funds, ultimately returning to a 50/50 split of assets between publicly traded funds and ABC funds. There are three key reasons for this shift in the firm's asset base. First, ABC's in- house funds have outperformed other funds consistently for the past five years. Second, ABC can offer its clients a reduced fee structure on funds managed in-house relative to other publicly traded funds. Lastly, ABC has recently hired a top fund manager away from a competing investment company and would like to increase his assets under management. ABC Capital's upper management requested that current clients be surveyed in order to determine the cause of the shift of assets away from ABC funds. Results of the survey indicated that clients feel there is a lack of information regarding ABC's funds. Clients would like to see extensive information about ABC's past performance, as well as a sensitivity analysis showing how the funds will perform in varying market scenarios. <PERSON> is part of a team that has been charged by upper management to create a marketing program to present to both current and potential clients of ABC. He needs to be able to demonstrate a history of strong performance for the ABC funds, and, while not promising any measure of future performance, project possible return scenarios. He decides to conduct a regression analysis on all of ABC's in-house funds. He is going to use 12 independent economic variables in order to predict each particular fund's return. <PERSON> is very aware of the many factors that could minimize the effectiveness of his regression model, and if any are present, he knows he must determine if any corrective actions are necessary. Mason is using a sample size of 121 monthly returns."}, "is_group_question": false}, {"question_number": 66, "total_questions": 144, "question_id": "1472073", "context": "", "question_text": "A fund has changed managers twice during the past 10 years. An analyst wishes to measure whether either of the changes in managers has had an impact on performance. R is the return on the fund, and M is the return on a market index. Which of the following regression equations can appropriately measure the desired impacts?", "choices": {"A": "The desired impact cannot be measured.", "B": "R = a + bM + c1D1 + c2D2 + ε, where D1 = 1 if the return is from the \u0000rst manager, and D2 = 1 if the return is from the third manager.", "C": "R = a + bM + c1D1 + c2D2 + c3D3 + ε, where D1 = 1 if the return is from the \u0000rst manager, and D2 = 1 if the return is from the second manager, and D3 = 1 is the return is from the third manager."}, "is_group_question": false}, {"question_number": 67, "total_questions": 144, "question_id": "1471869", "context": "", "question_text": "Consider the following regression equation: Salesi = 20.5 + 1.5 R&Di + 2.5 ADVi – 3.0 COMPi where Sales is dollar sales in millions, R&D is research and development expenditures in millions, ADV is dollar amount spent on advertising in millions, and COMP is the number of competitors in the industry. Which of the following is NOT a correct interpretation of this regression information?", "choices": {"A": "If R&D and advertising expenditures are $1 million each and there are 5 competitors, expected sales are $9.5 million.", "B": "One more competitor will mean $3 million less in sales (holding everything else constant).", "C": "If a company spends $1 more on R&D (holding everything else constant), sales are expected to increase by $1.5 million."}, "is_group_question": false}, {"question_number": 68, "total_questions": 144, "question_id": "1479922", "context": "", "question_text": "Which of the following is a potential remedy for multicollinearity?", "choices": {"A": "Add dummy variables to the regression.", "B": "Take \u0000rst di\u0000erences of the dependent variable.", "C": "Omit one or more of the collinear variables."}, "is_group_question": false}, {"question_number": 69, "total_questions": 144, "question_id": "1586007", "context": "", "question_text": "Which of the following conditions will least likely affect the statistical inference about regression parameters by itself?", "choices": {"A": "Multicollinearity.", "B": "Unconditional heteroskedasticity.", "C": "Model misspeci\u0000cation. <PERSON>, an enrolled candidate for the CFA Level II examination, has decided to perform a calendar test to examine whether there is any abnormal return associated with investments and disinvestments made in blue-chip stocks on particular days of the week. As a proxy for blue-chips, he has decided to use the S&P 500 Index. The analysis will involve the use of dummy variables and is based on the past 780 trading days. Here are selected findings of his study: RSS 0.0039 SSE 0.9534 SST 0.9573 R-squared0.004 SEE 0.035 <PERSON>, CFA, a friend of <PERSON>, overhears that he is interested in regression analysis and warns him that whenever heteroskedasticity is present in multiple regression, it could undermine the regression results. She mentions that one easy way to spot conditional heteroskedasticity is through a scatter plot, but she adds that there is a more formal test. Unfortunately, she can't quite remember its name. <PERSON> believes that heteroskedasticity can be rectified using White-corrected standard errors. Her son <PERSON> who has also taken part in the discussion, hears this comment and argues that White corrections would typically reduce the number of Type I errors in financial data."}, "is_group_question": false}, {"question_number": 81, "total_questions": 144, "question_id": "1471947", "context": "", "question_text": "An analyst regresses the return of a S&P 500 index fund against the S&P 500, and also regresses the return of an active manager against the S&P 500. The analyst uses the last five years of data in both regressions. Without making any other assumptions, which of the following is most accurate? The index fund:", "choices": {"A": "regression should have higher sum of squares regression as a ratio to the total sum of squares.", "B": "should have a lower coe\u0000cient of determination.", "C": "should have a higher coe\u0000cient on the independent variable."}, "is_group_question": false}, {"question_number": 82, "total_questions": 144, "question_id": "1479949", "context": "", "question_text": "Suppose the analyst wants to add a dummy variable for whether a person has a business college degree and an engineering degree. What is the CORRECT representation if a person has both degrees? Business Degree Dummy VariableEngineering Degree Dummy Variable", "choices": {"A": "0 1", "B": "0 0", "C": "1 1"}, "is_group_question": false}, {"question_number": 83, "total_questions": 144, "question_id": "1471946", "context": "", "question_text": "Which of the following statements regarding the R2 is least accurate?", "choices": {"A": "The adjusted-R2 not appropriate to use in simple regression.", "B": "It is possible for the adjusted-R2 to decline as more variables are added to the multiple regression.", "C": "The adjusted-R2 is greater than the R2 in multiple regression. <PERSON>, CFA, is the chief financial officer for Mega Flowers, one of the largest producers of flowers and bedding plants in the Western United States. Mega Flowers grows its plants in three large nursery facilities located in California. Its products are sold in its company-owned retail nurseries as well as in large, home and garden \"super centers\". For its retail stores, Mega Flowers has designed and implemented marketing plans each season that are aimed at its consumers in order to generate additional sales for certain high-margin products. To fully implement the marketing plan, additional contract salespeople are seasonally employed. For the past several years, these marketing plans seemed to be successful, providing a significant boost in sales to those specific products highlighted by the marketing efforts. However, for the past year, revenues have been flat, even though marketing expenditures increased slightly. <PERSON> is concerned that the expensive seasonal marketing campaigns are simply no longer generating the desired returns, and should either be significantly modified or eliminated altogether. He proposes that the company hire additional, permanent salespeople to focus on selling Mega Flowers' high-margin products all year long. The chief operating officer, <PERSON>, disagrees with <PERSON>. He believes that although last year's results were disappointing, the marketing campaign has demonstrated impressive results for the past five years, and should be continued. His belief is that the prior years' performance can be used as a gauge for future results, and that a simple increase in the sales force will not bring about the desired results. <PERSON> gathers information regarding quarterly sales revenue and marketing expenditures for the past five years. Based upon historical data, <PERSON> derives the following regression equation for Mega Flowers (stated in millions of dollars): Expected Sales= 12.6 + 1.6 (Marketing Expenditures)+ 1.2 (# of Salespeople) Brent shows the equation to <PERSON> and tells him, \"This equation shows that a $1 million increase in marketing expenditures will increase the independent variable by $1 .6 million, all other factors being equal.\" Johnson replies , \"It also appears that sales will equal $12.6 million if all independent variables are equal to zero.\" Brent makes the following statements about model evaluation: Statement 1: The BIC metric usually imposes a higher penalty for overfitting than AIC. Statement 2: AIC is used if the goal is to have a better forecast, while BIC is used if the goal is a better goodness of fit."}, "is_group_question": false}, {"question_number": 88, "total_questions": 144, "question_id": "1479921", "context": "", "question_text": "<PERSON>, CFA, is analyzing the result of a regression analysis comparing the performance of gold stocks versus a broad equity market index. <PERSON> believes that first lag serial correlation may be present and, in order to prove his theory, should use which of the following methods to detect its presence?", "choices": {"A": "The Breusch-Pagan test.", "B": "The Du<PERSON><PERSON><PERSON><PERSON> statistic.", "C": "The Hansen method. <PERSON>, CFA, has regressed 30 years of data to forecast future sales for National Motor Company based on the percent change in gross domestic product (GDP) and the change in retail price of a U.S. gallon of fuel. The results are presented below. Predictor CoefficientStan<PERSON><PERSON>r of the Coefficient Intercept 78 13.710 Δ GDP 30.22 12.120 Δ $ Fuel −412.39 183.981 Analysis of Variance Table (ANOVA) Source Degrees of Freedom Sum of Squares Regression 291.30 Error 27 132.12 Total 29 423.42"}, "is_group_question": false}, {"question_number": 92, "total_questions": 144, "question_id": "1471882", "context": "", "question_text": "When interpreting the results of a multiple regression analysis, which of the following terms represents the value of the dependent variable when the independent variables are all equal to zero?", "choices": {"A": "<PERSON>lop<PERSON> coe\u0000cient.", "B": "p-value.", "C": "Intercept term."}, "is_group_question": false}, {"question_number": 93, "total_questions": 144, "question_id": "1479908", "context": "", "question_text": "An analyst is trying to determine whether fund return performance is persistent. The analyst divides funds into three groups based on whether their return performance was in the top third (group 1), middle third (group 2), or bottom third (group 3) during the previous year. The manager then creates the following equation: R = a + b1D1 + b2D2 + b3D3 + ε, where R is return premium on the fund (the return minus the return on the S&P 500 benchmark) and Di is equal to 1 if the fund is in group i. Assuming no other information, this equation will suffer from:", "choices": {"A": "serial correlation.", "B": "heteroskedasticity.", "C": "multicollinearity."}, "is_group_question": false}, {"question_number": 94, "total_questions": 144, "question_id": "1471867", "context": "", "question_text": "Consider the following estimated regression equation, with calculated t-statistics of the estimates as indicated: AUTOt = 10.0 + 1.25 PIt + 1.0 TEENt – 2.0 INSt with a PI calculated t-statistic of 0.45, a TEEN calculated t-statistic of 2.2, and an INS calculated t-statistic of 0.63. The equation was estimated over 40 companies. Using a 5% level of significance, which of the independent variables significantly different from zero?", "choices": {"A": "TEEN only.", "B": "PI and INS only.", "C": "PI only."}, "is_group_question": false}, {"question_number": 95, "total_questions": 144, "question_id": "1472077", "context": "", "question_text": "Consider the following model of earnings (EPS) regressed against dummy variables for the quarters: EPSt = α + β1Q1t + β2Q2t + β3Q3t where: EPSt is a quarterly observation of earnings per share Q1t takes on a value of 1 if period t is the second quarter, 0 otherwise Q2t takes on a value of 1 if period t is the third quarter, 0 otherwise Q3t takes on a value of 1 if period t is the fourth quarter, 0 otherwise Which of the following statements regarding this model is most accurate? The:", "choices": {"A": "signi\u0000cance of the coe\u0000cients cannot be interpreted in the case of dummy variables.", "B": "EPS for the \u0000rst quarter is represented by the residual.", "C": "coe\u0000cient on each dummy tells us about the di\u0000erence in earnings per share between the respective quarter and the one left out (\u0000rst quarter in this case). <PERSON>, CFA, is currently enrolled as a part-time graduate student at State University. One of his recent assignments for his course on Quantitative Analysis is to perform a regression analysis utilizing the concepts covered during the semester. He must interpret the results of the regression as well as the test statistics. <PERSON><PERSON><PERSON> is confident in his ability to calculate the statistics because the class is allowed to use statistical software. However, he realizes that the interpretation of the statistics will be the true test of his knowledge of regression analysis. His professor has given to the students a list of questions that must be answered by the results of the analysis. <PERSON><PERSON><PERSON> has estimated a regression equation in which 160 quarterly returns on the S&P 500 are explained by three macroeconomic variables: employment growth (EMP) as measured by nonfarm payrolls, gross domestic product (GDP) growth, and private investment (INV). The results of the regression analysis are as follows: Coefficient Estimates Parameter CoefficientStandard Error of Coefficient Intercept 9.50 3.40 EMP -4.50 1.25 GDP 4.20 0.76 INV -0.30 0.16 Other Data: Regression sum of squares (RSS) = 126.00 Sum of squared errors (SSE) = 267.00 BG-stat: Lag 1: 3.15; Lag 2: 3.22 Degree of Freedom DenominatorDegree of Freedom Numerator 1 2 3 153 3.90 3.06 2.66 154 3.90 3.05 2.66 155 3.90 3.05 2.66 156 3.90 3.05 2.66 157 3.90 3.05 2.66 158 3.90 3.05 2.66"}, "is_group_question": false}, {"question_number": 100, "total_questions": 144, "question_id": "1479914", "context": "", "question_text": "Which of the following questions is least likely answered by using a qualitative dependent variable?", "choices": {"A": "Based on the following company-speci\u0000c \u0000nancial ratios, will company ABC enter bankruptcy?", "B": "Based on the following subsidiary and competition variables, will company XYZ divest itself of a subsidiary?", "C": "Based on the following executive-speci\u0000c and company-speci\u0000c variables, how many shares will be acquired through the exercise of executive stock options? <PERSON><PERSON>, CFA, is analyzing the returns of a fund that his company offers. He tests the fund's sensitivity to a small capitalization index and a large capitalization index, as well as to whether the January effect plays a role in the fund's performance. He uses two years of monthly returns data, and runs a regression of the fund's return on the indexes and a January-effect qualitative variable. The \"January\" variable is 1 for the month of January and zero for all other months. The results of the regression are shown in the tables below. Regression Statistics Multiple R 0.817088 R2 0.667632 Adjusted R2 0.617777 Standard Error 1.655891 Observations 24 ANOVA df SS MS Regression 3 110.1568 36.71895 Residual 20 54.8395 2.741975 Total 23 164.9963 Coefficients Standard Error t-Statistic Intercept -0.23821 0.388717 -0.61282 January 2.560552 1.232634 2.077301 Small Cap Index 0.231349 0.123007 1.880778 Large Cap Index 0.951515 0.254528 3.738359 Exhibit 1: Partial F-Table (5% Level of Significance) Degree of Freedom DenominatorDegree of Freedom Numerator 1 2 3 18 4.41 3.55 3.16 19 4.38 3.52 3.13 20 4.35 3.49 3.10 21 4.32 3.47 3.07 22 4.30 3.44 3.05 23 4.28 3.42 3.03 Gloucester plans to test for serial correlation and conditional and unconditional heteroskedasticity."}, "is_group_question": false}, {"question_number": 107, "total_questions": 144, "question_id": "1479878", "context": "", "question_text": "Consider the following analysis of variance table: Source Sum of Squares Df Mean Square Regression 20 1 20 Error 80 20 4 Total 100 21 The F-statistic for a test of joint significance of all the slope coefficients is closest to:", "choices": {"A": "0.2.", "B": "0.05.", "C": "5. <PERSON> is a security analyst who is using regression analysis to determine how well two factors explain returns for common stocks. The independent variables are the natural logarithm of the number of analysts following the companies, Ln(no. of analysts), and the natural logarithm of the market value of the companies, Ln(market value). The regression output generated from a statistical program is given in the following tables. Each p-value corresponds to a two-tail test. <PERSON> plans to use the result in the analysis of two investments. WLK Corp. has twelve analysts following it and a market capitalization of $2.33 billion. NGR Corp. has two analysts following it and a market capitalization of $47 million. Table 1: Regression Output Variable CoefficientStandard Error of the Coefficientt-statisticp-value Intercept 0.043 0.01159 3.71 < 0.001 Ln(No. of Analysts) −0.027 0.00466 −5.80 < 0.001 Ln(Market Value) 0.006 0.00271 2.21 0.028 Table 2: ANOVA Degrees of Freedom Sum of Squares Mean Square Regression 2 0.103 0.051 Residual 194 0.559 0.003 Total 196 0.662"}, "is_group_question": false}, {"question_number": 112, "total_questions": 144, "question_id": "1471871", "context": "", "question_text": "<PERSON>, CFA, is undertaking an analysis of the bicycle industry. He hypothesizes that bicycle sales (SALES) are a function of three factors: the population under 20 (POP), the level of disposable income (INCOME), and the number of dollars spent on advertising (ADV). All data are measured in millions of units. <PERSON> gathers data for the last 20 years. Which of the follow regression equations correctly represents <PERSON>'s hypothesis?", "choices": {"A": "SALES = α  x β1 POP x β2 INCOME x β3 ADV x ε.", "B": "SALES = α  + β1 POP + β2 INCOME + β3 ADV + ε.", "C": "INCOME = α  + β1 POP + β2 SALES + β3 ADV + ε."}, "is_group_question": false}, {"question_number": 113, "total_questions": 144, "question_id": "1479934", "context": "", "question_text": "A regression with three independent variables have VIF values of 3, 4, and 2 for the first, second, and third independent variables, respectively. Which of the following conclusions is most appropriate?", "choices": {"A": "Total VIF of 9 indicates a serious multicollinearity problem.", "B": "Only variable two has a problem with multicollinearity.", "C": "Multicollinearity does not seem to be a problem with the model. <PERSON>, CFA, is an analyst in the research department for Smith Brothers in New York. She follows several industries, as well as the top companies in each industry. She provides research materials for both the equity traders for Smith Brothers as well as their retail customers. She routinely performs regression analysis on those companies that she follows to identify any emerging trends that could affect investment decisions. Due to recent layoffs at the company, there has been some consolidation in the research department. Two research analysts have been laid off, and their workload will now be distributed among the remaining four analysts. In addition to her current workload, <PERSON> will now be responsible for providing research on the airline industry. Pinnacle Airlines, a leader in the industry, represents a large holding in Smith Brothers' portfolio. Looking back over past research on Pinnacle, <PERSON> recognizes that the company historically has been a strong performer in what is considered to be a very competitive industry. The stock price over the last 52-week period has outperformed that of other industry leaders, although Pinnacle's net income has remained flat. <PERSON> wonders if the stock price of Pinnacle has become overvalued relative to its peer group in the market, and wants to determine if the timing is right for Smith Brothers to decrease its position in Pinnacle. <PERSON> decides to run a regression analysis, using the monthly returns of Pinnacle stock as the dependent variable and monthly returns of the airlines industry as the independent variable. Analysis of Variance Table (ANOVA) Sourcedf (Degrees of Freedom)SS (Sum of Squares)Mean Square (SS/df) Regression 1 3,257 (RSS) 3,257 (MSR) Error 8 298 (SSE) 37.25 (MSE) Total 9 3,555 (SS Total)"}, "is_group_question": false}, {"question_number": 118, "total_questions": 144, "question_id": "1472074", "context": "", "question_text": "The management of a large restaurant chain believes that revenue growth is dependent upon the month of the year. Using a standard 12 month calendar, how many dummy variables must be used in a regression model that will test whether revenue growth differs by month?", "choices": {"A": "11.", "B": "13.", "C": "12."}, "is_group_question": false}, {"question_number": 119, "total_questions": 144, "question_id": "1472011", "context": "", "question_text": "Consider the following graph of residuals and the regression line from a time-series regression: These residuals exhibit the regression problem of:", "choices": {"A": "heteroskedasticity.", "B": "autocorrelation.", "C": "homoskedasticity. <PERSON><PERSON>, CFA, is looking at the retail property sector for her manager. She is undertaking a top down review as she feels this is the best way to analyze the industry segment. To predict U.S. property starts (housing), she has used regression analysis. <PERSON> included the following variables in her analysis: Average nominal interest rates during each year (as a decimal) Annual GDP per capita in $'000 Given these variables the following output was generated from 30 years of data: Exhibit 1 – Results from Regressing Housing Starts (in Millions) on Interest Rates and GDP Per Capita CoefficientStandard ErrorT-statistic Intercept 0.42 3.1 Interest rate −1.0 −2.0 GDP per capita 0.03 0.7 ANOVA df SS MSS F Regression 2 3.896 1.948 21.644 Residual 27 2.431 0.090 Total 29 6.327 Observations 30 Durbin-Watson 1.22 Exhibit 2 - Critical Values for F-Distribution at 5% Level of Significance Degrees of Freedom for the DenominatorDegrees of Freedom (df) for the Numerator 1 2 3 26 4.23 3.37 2.98 27 4.21 3.35 2.96 28 4.20 3.34 2.95 29 4.18 3.33 2.93 30 4.17 3.32 2.92 31 4.16 3.31 2.91 32 4.15 3.30 2.90 The following variable estimates have been made for 20X7: GDP per capita = $46,700 Interest rate = 7%"}, "is_group_question": false}, {"question_number": 126, "total_questions": 144, "question_id": "1471873", "context": "", "question_text": "<PERSON>, CFA, is undertaking an analysis of the bicycle industry. He hypothesizes that bicycle sales (SALES) are a function of three factors: the population under 20 (POP), the level of disposable income (INCOME), and the number of dollars spent on advertising (ADV). All data are measured in millions of units. <PERSON> gathers data for the last 20 years and estimates the following equation (standard errors in parentheses): SALES= 0.000+ 0.004 POP+ 1.031 INCOME + 2.002 ADV (0.113)(0.005) (0.337) (2.312) For next year, <PERSON> estimates the following parameters: (1) the population under 20 will be 120 million, (2) disposable income will be $300,000,000, and (3) advertising expenditures will be $100,000,000. Based on these estimates and the regression equation, what are predicted sales for the industry for next year?", "choices": {"A": "$557,143,000.", "B": "$509,980,000.", "C": "$656,991,000."}, "is_group_question": false}, {"question_number": 127, "total_questions": 144, "question_id": "1479919", "context": "", "question_text": "Which of the following is least likely a method of detecting serial correlations?", "choices": {"A": "A scatter plot of the residuals over time.", "B": "The Breusch-Pagan test.", "C": "The Breusch-Godfrey test."}, "is_group_question": false}, {"question_number": 128, "total_questions": 144, "question_id": "1479959", "context": "", "question_text": "A high-yield bond analyst is trying to develop an equation using financial ratios to estimate the probability of a company defaulting on its bonds. A technique that can be used to develop this equation is:", "choices": {"A": "logistic regression model.", "B": "dummy variable regression.", "C": "multiple linear regression adjusting for heteroskedasticity. Using a recent analysis of salaries (in $1,000) of financial analysts, Timbadia runs a regression of salaries on education, experience, and gender. (Gender equals one for men and zero for women.) The regression results from a sample of 230 financial analysts are presented below, with t-statistics in parenthesis. Salary= 34.98+ 1.2 Education+ 0.5 Experience+ 6.3 Gender (29.11) (8.93) (2.98) (1.58) Timbadia also runs a multiple regression to gain a better understanding of the relationship between lumber sales, housing starts, and commercial construction. The regression uses a large data set of lumber sales as the dependent variable with housing starts and commercial construction as the independent variables. The results of the regression are: Coefficient Standard Error t-statistics Intercept 5.337 1.71 3.14 Housing starts 0.76 0.09 8.44 Commercial construction 1.25 0.33 3.78 Finally, Timbadia runs a regression between the returns on a stock and its industry index with the following results: Coefficient Standard Error Intercept 2.1 2.01 Industry index 1.9 0.31 Standard error of estimate = 15.1 Correlation coefficient = 0.849"}, "is_group_question": false}, {"question_number": 132, "total_questions": 144, "question_id": "1471928", "context": "", "question_text": "Which of the following statements least accurately describes one of the fundamental multiple regression assumptions?", "choices": {"A": "The independent variables are not random.", "B": "The error term is normally distributed.", "C": "The variance of the error terms is not constant (i.e., the errors are heteroskedastic)."}, "is_group_question": false}, {"question_number": 133, "total_questions": 144, "question_id": "1472007", "context": "", "question_text": "An analyst is trying to estimate the beta for a fund. The analyst estimates a regression equation in which the fund returns are the dependent variable and the Wilshire 5000 is the independent variable, using monthly data over the past five years. The analyst finds that the correlation between the square of the residuals of the regression and the Wilshire 5000 is 0.2. Which of the following is most accurate, assuming a 0.05 level of significance? There is:", "choices": {"A": "no evidence that there is conditional heteroskedasticity or serial correlation in the regression equation.", "B": "evidence of serial correlation but not conditional heteroskedasticity in the regression equation.", "C": "evidence of conditional heteroskedasticity but not serial correlation in the regression equation."}, "is_group_question": false}, {"question_number": 134, "total_questions": 144, "question_id": "1471927", "context": "", "question_text": "One of the underlying assumptions of a multiple regression is that the variance of the residuals is constant for various levels of the independent variables. This quality is referred to as:", "choices": {"A": "a linear relationship.", "B": "homoskedasticity.", "C": "a normal distribution."}, "is_group_question": false}, {"question_number": 135, "total_questions": 144, "question_id": "1472075", "context": "", "question_text": "<PERSON> is an analyst with the retail industry. She is modeling a company's sales over time and has noticed a quarterly seasonal pattern. If she includes dummy variables to represent the seasonality component of the sales she must use:", "choices": {"A": "one dummy variables.", "B": "four dummy variables.", "C": "three dummy variables."}, "is_group_question": false}, {"question_number": 136, "total_questions": 144, "question_id": "1471881", "context": "", "question_text": "Which of the following statements most accurately interprets the following regression results at the given significance level? Variable p-value Intercept 0.0201 X1 0.0284 X2 0.0310 X3 0.0143", "choices": {"A": "The variable X2 is statistically signi\u0000cantly di\u0000erent from zero at the 3% signi\u0000cance level.", "B": "The variables X1 and X2 are statistically signi\u0000cantly di\u0000erent from zero at the 2% signi\u0000cance level.", "C": "The variable X3 is statistically signi\u0000cantly di\u0000erent from zero at the 2% signi\u0000cance level. <PERSON>, an analyst with Great Lakes Investments, has created a comprehensive report on the pharmaceutical industry at the request of his boss. The Great Lakes portfolio currently has a significant exposure to the pharmaceuticals industry through its large equity position in the top two pharmaceutical manufacturers. His boss requested that <PERSON> determine a way to accurately forecast pharmaceutical sales in order for Great Lakes to identify further investment opportunities in the industry as well as to minimize their exposure to downturns in the market. <PERSON> realized that there are many factors that could possibly have an impact on sales, and he must identify a method that can quantify their effect. <PERSON> used a multiple regression analysis with five independent variables to predict industry sales. His goal is to not only identify relationships that are statistically significant, but economically significant as well. The assumptions of his model are fairly standard: a linear relationship exists between the dependent and independent variables, the independent variables are not random, and the expected value of the error term is zero. <PERSON> is confident with the results presented in his report. He has already done some hypothesis testing for statistical significance, including calculating a t-statistic and conducting a two-tailed test where the null hypothesis is that the regression coefficient is equal to zero versus the alternative that it is not. He feels that he has done a thorough job on the report and is ready to answer any questions posed by his boss. However, <PERSON>'s boss, <PERSON>, is concerned that in his analysis, <PERSON> has ignored several potential problems with the regression model that may affect his conclusions. He knows that when any of the basic assumptions of a regression model are violated, any results drawn for the model are questionable. He asks <PERSON> to go back and carefully examine the effects of heteroskedasticity, multicollinearity, and serial correlation on his model. In specific, he wants <PERSON> to make suggestions regarding how to detect these errors and to correct problems that he encounters."}, "is_group_question": false}, {"question_number": 141, "total_questions": 144, "question_id": "1472009", "context": "", "question_text": "Which of the following statements regarding heteroskedasticity is least accurate?", "choices": {"A": "Conditional heteroskedasticity can be detected using the Breusch-Pagan chi-square statistic.", "B": "When not related to independent variables, heteroskedasticity does not pose any major problems with the regression.", "C": "Heteroskedasticity only occurs in cross-sectional regressions."}, "is_group_question": false}, {"question_number": 142, "total_questions": 144, "question_id": "1472066", "context": "", "question_text": "When two or more of the independent variables in a multiple regression are correlated with each other, the condition is called:", "choices": {"A": "multicollinearity.", "B": "conditional heteroskedasticity.", "C": "serial correlation."}, "is_group_question": false}, {"question_number": 143, "total_questions": 144, "question_id": "1479904", "context": "", "question_text": "Which of the following is least likely to result in misspecification of a regression model?", "choices": {"A": "Omission of an important independent variable.", "B": "Inappropriate variable form.", "C": "Transforming a variable."}, "is_group_question": false}, {"question_number": 144, "total_questions": 144, "question_id": "1471891", "context": "", "question_text": "Which of the following statements regarding the results of a regression analysis is least accurate? The:", "choices": {"A": "slope coe\u0000cient in a multiple regression is the change in the dependent variable for a one-unit change in the independent variable, holding all other variables constant.", "B": "slope coe\u0000cient in a multiple regression is the value of the dependent variable for a given value of the independent variable.", "C": "slope coe\u0000cients in the multiple regression are referred to as partial betas."}, "is_group_question": false}], "answers": {"73": {"correct_answer": "E", "explanation": "<PERSON> is correct. White-corrected standard errors are also known as robust standard errors. <PERSON> is correct because for financial data, generally, White-corrected errors are higher than the biased errors leading to lower computed t-statistics and, therefore, less frequent rejection of the null hypothesis (remember incorrectly rejecting a true null is Type I error). (Module 1.3, LOS 1.h)"}, "80": {"correct_answer": "E", "explanation": "<PERSON> is correct. White-corrected standard errors are also known as robust standard errors. <PERSON> is correct because White-corrected errors are higher than the biased errors leading to lower computed t-statistics and therefore less frequent rejection of the Null Hypothesis (remember incorrectly rejecting a true Null is Type I error). (Module 1.3, LOS 1.h)"}}, "total_questions": 54}