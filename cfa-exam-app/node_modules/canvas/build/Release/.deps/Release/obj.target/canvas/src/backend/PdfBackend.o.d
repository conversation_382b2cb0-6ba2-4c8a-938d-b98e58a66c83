cmd_Release/obj.target/canvas/src/backend/PdfBackend.o := c++ -o Release/obj.target/canvas/src/backend/PdfBackend.o ../src/backend/PdfBackend.cc '-DNODE_GYP_MODULE_NAME=canvas' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DHAVE_JPEG' '-DHAVE_GIF' '-DHAVE_RSVG' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/23.5.0/src -I/Users/<USER>/Library/Caches/node-gyp/23.5.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/23.5.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/23.5.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/23.5.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/23.5.0/deps/v8/include -I../../nan -I/opt/homebrew/Cellar/cairo/1.18.2/include/cairo -I/opt/homebrew/Cellar/fontconfig/2.16.0/include -I/opt/homebrew/opt/freetype/include/freetype2 -I/opt/homebrew/opt/libpng/include/libpng16 -I/opt/homebrew/Cellar/libxext/1.3.6/include -I/opt/homebrew/Cellar/xorgproto/2024.1/include -I/opt/homebrew/Cellar/libxrender/0.9.12/include -I/opt/homebrew/Cellar/libx11/1.8.10/include -I/opt/homebrew/Cellar/libxcb/1.17.0/include -I/opt/homebrew/Cellar/libxau/1.0.12/include -I/opt/homebrew/Cellar/libxdmcp/1.1.5/include -I/opt/homebrew/Cellar/pixman/0.44.2/include/pixman-1 -I/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0 -I/opt/homebrew/Cellar/glib/2.82.4/include -I/opt/homebrew/Cellar/fribidi/1.0.16/include/fribidi -I/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz -I/opt/homebrew/Cellar/graphite2/1.3.14/include -I/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0 -I/opt/homebrew/Cellar/glib/2.82.4/lib/glib-2.0/include -I/opt/homebrew/opt/gettext/include -I/opt/homebrew/Cellar/pcre2/10.44/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk/usr/include/ffi -I/opt/homebrew/opt/jpeg-turbo/include -I/opt/homebrew/include -I/opt/homebrew/Cellar/librsvg/2.58.4/include/librsvg-2.0 -I/opt/homebrew/Cellar/gdk-pixbuf/2.42.12/include/gdk-pixbuf-2.0 -I/opt/homebrew/opt/libtiff/include -I/opt/homebrew/opt/zstd/include -I/opt/homebrew/Cellar/xz/5.6.4/include  -O3 -gdwarf-2 -fno-strict-aliasing -flto -mmacosx-version-min=11.0 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++20 -stdlib=libc++ -fno-rtti -MMD -MF ./Release/.deps/Release/obj.target/canvas/src/backend/PdfBackend.o.d.raw   -c
Release/obj.target/canvas/src/backend/PdfBackend.o: \
  ../src/backend/PdfBackend.cc ../src/backend/PdfBackend.h \
  ../src/backend/Backend.h \
  /opt/homebrew/Cellar/cairo/1.18.2/include/cairo/cairo.h \
  /opt/homebrew/Cellar/cairo/1.18.2/include/cairo/cairo-version.h \
  /opt/homebrew/Cellar/cairo/1.18.2/include/cairo/cairo-features.h \
  /opt/homebrew/Cellar/cairo/1.18.2/include/cairo/cairo-deprecated.h \
  ../src/backend/../dll_visibility.h ../../nan/nan.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/node_version.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/uv.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/uv/errno.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/uv/version.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/uv/unix.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/uv/threadpool.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/uv/darwin.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/node.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/cppgc/common.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8config.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-array-buffer.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-local-handle.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-handle-base.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-internal.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-object.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-maybe.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-persistent-handle.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-weak-callback-info.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-primitive.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-data.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-value.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-sandbox.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-traced-handle.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-container.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-context.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-snapshot.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-isolate.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-callbacks.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-promise.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-debug.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-script.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-memory-span.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-message.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-embedder-heap.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-exception.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-function-callback.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-microtask.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-statistics.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-unwinder.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-embedder-state-scope.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-date.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-extension.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-external.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-function.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-template.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-initialization.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-platform.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-source-location.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-json.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-locker.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-microtask-queue.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-primitive-object.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-proxy.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-regexp.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-typed-array.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-value-serializer.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-version.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-wasm.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/node_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/node_buffer.h \
  /Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/node_object_wrap.h \
  ../../nan/nan_callbacks.h ../../nan/nan_callbacks_12_inl.h \
  ../../nan/nan_maybe_43_inl.h ../../nan/nan_converters.h \
  ../../nan/nan_converters_43_inl.h ../../nan/nan_new.h \
  ../../nan/nan_implementation_12_inl.h \
  ../../nan/nan_persistent_12_inl.h ../../nan/nan_weak.h \
  ../../nan/nan_object_wrap.h ../../nan/nan_private.h \
  ../../nan/nan_typedarray_contents.h ../../nan/nan_json.h \
  ../../nan/nan_scriptorigin.h ../src/backend/../closure.h \
  ../src/backend/../Canvas.h ../src/backend/../backend/Backend.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pangocairo.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-attributes.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-font.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-coverage.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib-object.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gbinding.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/galloca.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gtypes.h \
  /opt/homebrew/Cellar/glib/2.82.4/lib/glib-2.0/include/glibconfig.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gmacros.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gversionmacros.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/glib-visibility.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/garray.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gasyncqueue.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gthread.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gatomic.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/glib-typeof.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gerror.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gquark.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gutils.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gbacktrace.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gbase64.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gbitlock.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gbookmarkfile.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gdatetime.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gtimezone.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gbytes.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gcharset.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gchecksum.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gconvert.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gdataset.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gdate.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gdir.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/genviron.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gfileutils.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/ggettext.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/ghash.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/glist.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gmem.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gnode.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/ghmac.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/ghook.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/ghostutils.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/giochannel.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gmain.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gpoll.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gslist.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gstring.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gunicode.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gstrfuncs.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gkeyfile.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gmappedfile.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gmarkup.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gmessages.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gvariant.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gvarianttype.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/goption.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gpathbuf.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gpattern.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gprimes.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gqsort.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gqueue.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/grand.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/grcbox.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/grefcount.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/grefstring.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gregex.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gscanner.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gsequence.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gshell.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gslice.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gspawn.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gstringchunk.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gstrvbuilder.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gtestutils.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gthreadpool.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gtimer.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gtrashstack.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gtree.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/guri.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/guuid.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gversion.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/deprecated/gallocator.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/deprecated/gcache.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/deprecated/gcompletion.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/deprecated/gmain.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/deprecated/grel.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/deprecated/gthread.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/glib-autocleanups.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gobject.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gtype.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gobject-visibility.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gvalue.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gparam.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gclosure.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gsignal.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gmarshal.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gboxed.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/glib-types.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gbindinggroup.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/genums.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/glib-enumtypes.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gparamspecs.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gsignalgroup.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gsourceclosure.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gtypemodule.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gtypeplugin.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gvaluearray.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gvaluetypes.h \
  /opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gobject-autocleanups.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-version-macros.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-features.h \
  /opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb.h \
  /opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-blob.h \
  /opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-common.h \
  /opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-buffer.h \
  /opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-unicode.h \
  /opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-font.h \
  /opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-face.h \
  /opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-map.h \
  /opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-set.h \
  /opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-draw.h \
  /opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-paint.h \
  /opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-deprecated.h \
  /opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-shape.h \
  /opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-shape-plan.h \
  /opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-style.h \
  /opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-version.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-types.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-gravity.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-matrix.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-script.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-language.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-bidi-type.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-direction.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-color.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-break.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-item.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-context.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-fontmap.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-fontset.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-engine.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-glyph.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-enum-types.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-fontset-simple.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-glyph-item.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-layout.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-tabs.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-markup.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-renderer.h \
  /opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-utils.h \
  /opt/homebrew/opt/jpeg-turbo/include/jpeglib.h \
  /opt/homebrew/opt/jpeg-turbo/include/jconfig.h \
  /opt/homebrew/opt/jpeg-turbo/include/jmorecfg.h \
  /opt/homebrew/opt/libpng/include/libpng16/png.h \
  /opt/homebrew/opt/libpng/include/libpng16/pnglibconf.h \
  /opt/homebrew/opt/libpng/include/libpng16/pngconf.h \
  /opt/homebrew/Cellar/cairo/1.18.2/include/cairo/cairo-pdf.h
../src/backend/PdfBackend.cc:
../src/backend/PdfBackend.h:
../src/backend/Backend.h:
/opt/homebrew/Cellar/cairo/1.18.2/include/cairo/cairo.h:
/opt/homebrew/Cellar/cairo/1.18.2/include/cairo/cairo-version.h:
/opt/homebrew/Cellar/cairo/1.18.2/include/cairo/cairo-features.h:
/opt/homebrew/Cellar/cairo/1.18.2/include/cairo/cairo-deprecated.h:
../src/backend/../dll_visibility.h:
../../nan/nan.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/node_version.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/uv.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/uv/errno.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/uv/version.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/uv/unix.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/uv/threadpool.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/uv/darwin.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/node.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/cppgc/common.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8config.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-array-buffer.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-local-handle.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-handle-base.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-internal.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-object.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-maybe.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-persistent-handle.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-weak-callback-info.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-primitive.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-data.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-value.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-sandbox.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-traced-handle.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-container.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-context.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-snapshot.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-isolate.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-callbacks.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-promise.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-debug.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-script.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-memory-span.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-message.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-embedder-heap.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-exception.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-function-callback.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-microtask.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-statistics.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-unwinder.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-embedder-state-scope.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-date.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-extension.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-external.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-function.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-template.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-initialization.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-platform.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-source-location.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-json.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-locker.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-microtask-queue.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-primitive-object.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-proxy.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-regexp.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-typed-array.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-value-serializer.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-version.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/v8-wasm.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/node_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/node_buffer.h:
/Users/<USER>/Library/Caches/node-gyp/23.5.0/include/node/node_object_wrap.h:
../../nan/nan_callbacks.h:
../../nan/nan_callbacks_12_inl.h:
../../nan/nan_maybe_43_inl.h:
../../nan/nan_converters.h:
../../nan/nan_converters_43_inl.h:
../../nan/nan_new.h:
../../nan/nan_implementation_12_inl.h:
../../nan/nan_persistent_12_inl.h:
../../nan/nan_weak.h:
../../nan/nan_object_wrap.h:
../../nan/nan_private.h:
../../nan/nan_typedarray_contents.h:
../../nan/nan_json.h:
../../nan/nan_scriptorigin.h:
../src/backend/../closure.h:
../src/backend/../Canvas.h:
../src/backend/../backend/Backend.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pangocairo.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-attributes.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-font.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-coverage.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib-object.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gbinding.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/galloca.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gtypes.h:
/opt/homebrew/Cellar/glib/2.82.4/lib/glib-2.0/include/glibconfig.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gmacros.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gversionmacros.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/glib-visibility.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/garray.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gasyncqueue.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gthread.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gatomic.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/glib-typeof.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gerror.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gquark.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gutils.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gbacktrace.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gbase64.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gbitlock.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gbookmarkfile.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gdatetime.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gtimezone.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gbytes.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gcharset.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gchecksum.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gconvert.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gdataset.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gdate.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gdir.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/genviron.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gfileutils.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/ggettext.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/ghash.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/glist.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gmem.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gnode.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/ghmac.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/ghook.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/ghostutils.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/giochannel.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gmain.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gpoll.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gslist.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gstring.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gunicode.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gstrfuncs.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gkeyfile.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gmappedfile.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gmarkup.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gmessages.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gvariant.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gvarianttype.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/goption.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gpathbuf.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gpattern.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gprimes.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gqsort.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gqueue.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/grand.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/grcbox.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/grefcount.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/grefstring.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gregex.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gscanner.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gsequence.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gshell.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gslice.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gspawn.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gstringchunk.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gstrvbuilder.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gtestutils.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gthreadpool.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gtimer.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gtrashstack.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gtree.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/guri.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/guuid.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/gversion.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/deprecated/gallocator.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/deprecated/gcache.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/deprecated/gcompletion.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/deprecated/gmain.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/deprecated/grel.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/deprecated/gthread.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/glib/glib-autocleanups.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gobject.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gtype.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gobject-visibility.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gvalue.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gparam.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gclosure.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gsignal.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gmarshal.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gboxed.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/glib-types.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gbindinggroup.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/genums.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/glib-enumtypes.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gparamspecs.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gsignalgroup.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gsourceclosure.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gtypemodule.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gtypeplugin.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gvaluearray.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gvaluetypes.h:
/opt/homebrew/Cellar/glib/2.82.4/include/glib-2.0/gobject/gobject-autocleanups.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-version-macros.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-features.h:
/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb.h:
/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-blob.h:
/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-common.h:
/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-buffer.h:
/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-unicode.h:
/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-font.h:
/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-face.h:
/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-map.h:
/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-set.h:
/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-draw.h:
/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-paint.h:
/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-deprecated.h:
/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-shape.h:
/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-shape-plan.h:
/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-style.h:
/opt/homebrew/Cellar/harfbuzz/10.2.0/include/harfbuzz/hb-version.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-types.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-gravity.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-matrix.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-script.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-language.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-bidi-type.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-direction.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-color.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-break.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-item.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-context.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-fontmap.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-fontset.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-engine.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-glyph.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-enum-types.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-fontset-simple.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-glyph-item.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-layout.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-tabs.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-markup.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-renderer.h:
/opt/homebrew/Cellar/pango/1.56.1/include/pango-1.0/pango/pango-utils.h:
/opt/homebrew/opt/jpeg-turbo/include/jpeglib.h:
/opt/homebrew/opt/jpeg-turbo/include/jconfig.h:
/opt/homebrew/opt/jpeg-turbo/include/jmorecfg.h:
/opt/homebrew/opt/libpng/include/libpng16/png.h:
/opt/homebrew/opt/libpng/include/libpng16/pnglibconf.h:
/opt/homebrew/opt/libpng/include/libpng16/pngconf.h:
/opt/homebrew/Cellar/cairo/1.18.2/include/cairo/cairo-pdf.h:
