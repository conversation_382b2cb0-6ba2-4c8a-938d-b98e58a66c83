cmd_Release/canvas.node := c++ -bundle -undefined dynamic_lookup -Wl,-search_paths_first -mmacosx-version-min=11.0 -arch arm64 -L./Release -stdlib=libc++  -o Release/canvas.node Release/obj.target/canvas/src/backend/Backend.o Release/obj.target/canvas/src/backend/ImageBackend.o Release/obj.target/canvas/src/backend/PdfBackend.o Release/obj.target/canvas/src/backend/SvgBackend.o Release/obj.target/canvas/src/bmp/BMPParser.o Release/obj.target/canvas/src/Backends.o Release/obj.target/canvas/src/Canvas.o Release/obj.target/canvas/src/CanvasGradient.o Release/obj.target/canvas/src/CanvasPattern.o Release/obj.target/canvas/src/CanvasRenderingContext2d.o Release/obj.target/canvas/src/closure.o Release/obj.target/canvas/src/color.o Release/obj.target/canvas/src/Image.o Release/obj.target/canvas/src/ImageData.o Release/obj.target/canvas/src/init.o Release/obj.target/canvas/src/register_font.o -L/opt/homebrew/Cellar/pixman/0.44.2/lib -lpixman-1 -L/opt/homebrew/Cellar/cairo/1.18.2/lib -lcairo -L/opt/homebrew/opt/libpng/lib -lpng16 -L/opt/homebrew/Cellar/pango/1.56.1/lib -lpangocairo-1.0 -lpango-1.0 -L/opt/homebrew/Cellar/harfbuzz/10.2.0/lib -lharfbuzz -L/opt/homebrew/Cellar/glib/2.82.4/lib -lgobject-2.0 -lglib-2.0 -L/opt/homebrew/opt/gettext/lib -lintl -L/opt/homebrew/opt/freetype/lib -lfreetype -L/opt/homebrew/opt/jpeg-turbo/lib -ljpeg -L/opt/homebrew/lib -lgif -L/opt/homebrew/Cellar/librsvg/2.58.4/lib -lrsvg-2 -lm -L/opt/homebrew/Cellar/gdk-pixbuf/2.42.12/lib -lgdk_pixbuf-2.0 -lgio-2.0
