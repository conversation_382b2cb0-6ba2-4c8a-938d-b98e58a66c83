{"name": "uncontrollable", "version": "7.2.1", "description": "Wrap a controlled react component, to allow specific prop/handler pairs to be uncontrolled", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/jquense/uncontrollable.git"}, "license": "MIT", "main": "lib/cjs/index.js", "module": "lib/esm/index.js", "keywords": ["uncontrolled-component", "react-component", "input", "controlled", "uncontrolled", "form"], "scripts": {"test": "jest", "tdd": "jest --watch", "lint": "eslint src test", "build": "build src", "prepublishOnly": "npm run build"}, "publishConfig": {"directory": "lib"}, "peerDependencies": {"react": ">=15.0.0"}, "jest": {"rootDir": "./test"}, "devDependencies": {"@4c/build": "^2.0.1", "@4c/semantic-release-config": "^2.0.3", "@4c/tsconfig": "^0.3.0", "@babel/cli": "^7.6.4", "@babel/core": "^7.6.4", "@babel/preset-typescript": "^7.6.0", "@typescript-eslint/eslint-plugin": "^2.5.0", "@typescript-eslint/parser": "^2.5.0", "babel-core": "^7.0.0-0", "babel-jest": "^24.9.0", "babel-preset-jason": "^6.0.1", "cpy": "^7.3.0", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.15.1", "eslint": "^6.6.0", "eslint-config-4catalyzer-typescript": "^1.1.0", "eslint-config-jason": "^6.1.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-react": "^7.16.0", "jest": "^24.9.0", "prop-types": "^15.7.2", "react": "^16.11.0", "react-dom": "^16.11.0", "release-script": "^1.0.2", "rimraf": "^3.0.0", "typescript": "^3.6.4"}, "dependencies": {"@babel/runtime": "^7.6.3", "@types/react": ">=16.9.11", "invariant": "^2.2.4", "react-lifecycles-compat": "^3.0.4"}, "bugs": {"url": "https://github.com/jquense/uncontrollable/issues"}, "readme": "ERROR: No README data found!", "homepage": "https://github.com/jquense/uncontrollable#readme", "_id": "uncontrollable@7.1.0"}