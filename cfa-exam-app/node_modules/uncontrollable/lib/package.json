{"name": "uncontrollable", "version": "7.2.0", "description": "Wrap a controlled react component, to allow specific prop/handler pairs to be uncontrolled", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/jquense/uncontrollable.git"}, "license": "MIT", "main": "cjs/index.js", "module": "esm/index.js", "keywords": ["uncontrolled-component", "react-component", "input", "controlled", "uncontrolled", "form"], "publishConfig": {"directory": "lib"}, "peerDependencies": {"react": ">=15.0.0"}, "jest": {"rootDir": "./test"}, "dependencies": {"@babel/runtime": "^7.6.3", "@types/react": ">=16.9.11", "invariant": "^2.2.4", "react-lifecycles-compat": "^3.0.4"}, "bugs": {"url": "https://github.com/jquense/uncontrollable/issues"}, "readme": "ERROR: No README data found!", "homepage": "https://github.com/jquense/uncontrollable#readme", "_id": "uncontrollable@7.1.0"}