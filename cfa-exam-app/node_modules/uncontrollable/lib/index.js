"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard");

exports.__esModule = true;
exports.useUncontrolledProp = exports.uncontrollable = exports.useUncontrolled = void 0;

var _hook = _interopRequireWildcard(require("./hook"));

exports.useUncontrolled = _hook.default;
exports.useUncontrolledProp = _hook.useUncontrolledProp;

var _uncontrollable = _interopRequireDefault(require("./uncontrollable"));

exports.uncontrollable = _uncontrollable.default;