export declare function uncontrolledPropTypes(controlledValues: any, displayName: string): {};
export declare function isProp<P>(props: P, prop: keyof P): boolean;
export declare function defaultKey(key: string): string;
/**
 * Copyright (c) 2013-present, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 */
export declare function canAcceptRef(component: any): any;
