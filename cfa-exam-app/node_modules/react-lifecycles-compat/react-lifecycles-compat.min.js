!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(e.reactLifecyclesCompat={})}(this,function(e){"use strict";function t(){var e=this.constructor.getDerivedStateFromProps(this.props,this.state);null!==e&&void 0!==e&&this.setState(e)}function n(e){this.setState(function(t){var n=this.constructor.getDerivedStateFromProps(e,t);return null!==n&&void 0!==n?n:null}.bind(this))}function o(e,t){try{var n=this.props,o=this.state;this.props=e,this.state=t,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(n,o)}finally{this.props=n,this.state=o}}t.__suppressDeprecationWarning=!0,n.__suppressDeprecationWarning=!0,o.__suppressDeprecationWarning=!0,e.polyfill=function(e){var i=e.prototype;if(!i||!i.isReactComponent)throw new Error("Can only polyfill class components");if("function"!=typeof e.getDerivedStateFromProps&&"function"!=typeof i.getSnapshotBeforeUpdate)return e;var p=null,l=null,r=null;if("function"==typeof i.componentWillMount?p="componentWillMount":"function"==typeof i.UNSAFE_componentWillMount&&(p="UNSAFE_componentWillMount"),"function"==typeof i.componentWillReceiveProps?l="componentWillReceiveProps":"function"==typeof i.UNSAFE_componentWillReceiveProps&&(l="UNSAFE_componentWillReceiveProps"),"function"==typeof i.componentWillUpdate?r="componentWillUpdate":"function"==typeof i.UNSAFE_componentWillUpdate&&(r="UNSAFE_componentWillUpdate"),null!==p||null!==l||null!==r){var s=e.displayName||e.name,a="function"==typeof e.getDerivedStateFromProps?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";throw Error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n"+s+" uses "+a+" but also contains the following legacy lifecycles:"+(null!==p?"\n  "+p:"")+(null!==l?"\n  "+l:"")+(null!==r?"\n  "+r:"")+"\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks")}if("function"==typeof e.getDerivedStateFromProps&&(i.componentWillMount=t,i.componentWillReceiveProps=n),"function"==typeof i.getSnapshotBeforeUpdate){if("function"!=typeof i.componentDidUpdate)throw new Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");i.componentWillUpdate=o;var c=i.componentDidUpdate;i.componentDidUpdate=function(e,t,n){var o=this.__reactInternalSnapshotFlag?this.__reactInternalSnapshot:n;c.call(this,e,t,o)}}return e},Object.defineProperty(e,"__esModule",{value:!0})});
