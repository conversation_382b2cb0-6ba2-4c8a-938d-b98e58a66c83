export class GlobalWorkerOptions {
    static "__#51@#port": null;
    static "__#51@#src": string;
    /**
     * @param {Worker | null} workerPort - Defines global port for worker process.
     *   Overrides the `workerSrc` option.
     */
    static set workerPort(val: Worker | null);
    /**
     * @type {Worker | null}
     */
    static get workerPort(): Worker | null;
    /**
     * @param {string} workerSrc - A string containing the path and filename of
     *   the worker file.
     *
     *   NOTE: The `workerSrc` option should always be set, in order to prevent
     *         any issues when using the PDF.js library.
     */
    static set workerSrc(val: string);
    /**
     * @type {string}
     */
    static get workerSrc(): string;
}
