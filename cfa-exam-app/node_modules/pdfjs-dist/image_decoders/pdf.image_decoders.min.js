/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2023 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */
!function webpackUniversalModuleDefinition(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=e.pdfjsImageDecoders=t():"function"==typeof define&&define.amd?define("pdfjs-dist/image_decoders/pdf.image_decoders",[],(()=>e.pdfjsImageDecoders=t())):"object"==typeof exports?exports["pdfjs-dist/image_decoders/pdf.image_decoders"]=e.pdfjsImageDecoders=t():e["pdfjs-dist/image_decoders/pdf.image_decoders"]=e.pdfjsImageDecoders=t()}(globalThis,(()=>(()=>{"use strict";var e=[,(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});t.VerbosityLevel=t.Util=t.UnknownErrorException=t.UnexpectedResponseException=t.TextRenderingMode=t.RenderingIntentFlag=t.PromiseCapability=t.PermissionFlag=t.PasswordResponses=t.PasswordException=t.PageActionEventType=t.OPS=t.MissingPDFException=t.MAX_IMAGE_SIZE_TO_CACHE=t.LINE_FACTOR=t.LINE_DESCENT_FACTOR=t.InvalidPDFException=t.ImageKind=t.IDENTITY_MATRIX=t.FormatError=t.FeatureTest=t.FONT_IDENTITY_MATRIX=t.DocumentActionEventType=t.CMapCompressionType=t.BaseException=t.BASELINE_FACTOR=t.AnnotationType=t.AnnotationReplyType=t.AnnotationPrefix=t.AnnotationMode=t.AnnotationFlag=t.AnnotationFieldFlag=t.AnnotationEditorType=t.AnnotationEditorPrefix=t.AnnotationEditorParamsType=t.AnnotationBorderStyleType=t.AnnotationActionEventType=t.AbortException=void 0;t.assert=function assert(e,t){e||unreachable(t)};t.bytesToString=bytesToString;t.createValidAbsoluteUrl=function createValidAbsoluteUrl(e,t=null,n=null){if(!e)return null;try{if(n&&"string"==typeof e){if(n.addDefaultProtocol&&e.startsWith("www.")){const t=e.match(/\./g);t?.length>=2&&(e=`http://${e}`)}if(n.tryConvertEncoding)try{e=stringToUTF8String(e)}catch{}}const i=t?new URL(e,t):new URL(e);if(function _isValidProtocol(e){switch(e?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(i))return i}catch{}return null};t.getModificationDate=function getModificationDate(e=new Date){return[e.getUTCFullYear().toString(),(e.getUTCMonth()+1).toString().padStart(2,"0"),e.getUTCDate().toString().padStart(2,"0"),e.getUTCHours().toString().padStart(2,"0"),e.getUTCMinutes().toString().padStart(2,"0"),e.getUTCSeconds().toString().padStart(2,"0")].join("")};t.getUuid=function getUuid(){if("undefined"!=typeof crypto&&"function"==typeof crypto?.randomUUID)return crypto.randomUUID();const e=new Uint8Array(32);if("undefined"!=typeof crypto&&"function"==typeof crypto?.getRandomValues)crypto.getRandomValues(e);else for(let t=0;t<32;t++)e[t]=Math.floor(255*Math.random());return bytesToString(e)};t.getVerbosityLevel=function getVerbosityLevel(){return r};t.info=function info(e){r>=i.INFOS&&console.log(`Info: ${e}`)};t.isArrayBuffer=function isArrayBuffer(e){return"object"==typeof e&&void 0!==e?.byteLength};t.isArrayEqual=function isArrayEqual(e,t){if(e.length!==t.length)return!1;for(let n=0,i=e.length;n<i;n++)if(e[n]!==t[n])return!1;return!0};t.isNodeJS=void 0;t.normalizeUnicode=function normalizeUnicode(e){if(!c){c=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu;l=new Map([["ﬅ","ſt"]])}return e.replaceAll(c,((e,t,n)=>t?t.normalize("NFKC"):l.get(n)))};t.objectFromMap=function objectFromMap(e){const t=Object.create(null);for(const[n,i]of e)t[n]=i;return t};t.objectSize=function objectSize(e){return Object.keys(e).length};t.setVerbosityLevel=function setVerbosityLevel(e){Number.isInteger(e)&&(r=e)};t.shadow=shadow;t.string32=function string32(e){return String.fromCharCode(e>>24&255,e>>16&255,e>>8&255,255&e)};t.stringToBytes=stringToBytes;t.stringToPDFString=function stringToPDFString(e){if(e[0]>="ï"){let t;"þ"===e[0]&&"ÿ"===e[1]?t="utf-16be":"ÿ"===e[0]&&"þ"===e[1]?t="utf-16le":"ï"===e[0]&&"»"===e[1]&&"¿"===e[2]&&(t="utf-8");if(t)try{const n=new TextDecoder(t,{fatal:!0}),i=stringToBytes(e);return n.decode(i)}catch(e){warn(`stringToPDFString: "${e}".`)}}const t=[];for(let n=0,i=e.length;n<i;n++){const i=a[e.charCodeAt(n)];t.push(i?String.fromCharCode(i):e.charAt(n))}return t.join("")};t.stringToUTF8String=stringToUTF8String;t.unreachable=unreachable;t.utf8StringToString=function utf8StringToString(e){return unescape(encodeURIComponent(e))};t.warn=warn;const n=!("object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&"browser"!==process.type);t.isNodeJS=n;t.IDENTITY_MATRIX=[1,0,0,1,0,0];t.FONT_IDENTITY_MATRIX=[.001,0,0,.001,0,0];t.MAX_IMAGE_SIZE_TO_CACHE=1e7;t.LINE_FACTOR=1.35;t.LINE_DESCENT_FACTOR=.35;t.BASELINE_FACTOR=.25925925925925924;t.RenderingIntentFlag={ANY:1,DISPLAY:2,PRINT:4,SAVE:8,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,OPLIST:256};t.AnnotationMode={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3};t.AnnotationEditorPrefix="pdfjs_internal_editor_";t.AnnotationEditorType={DISABLE:-1,NONE:0,FREETEXT:3,STAMP:13,INK:15};t.AnnotationEditorParamsType={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23};t.PermissionFlag={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048};t.TextRenderingMode={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};t.ImageKind={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};t.AnnotationType={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};t.AnnotationReplyType={GROUP:"Group",REPLY:"R"};t.AnnotationFlag={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};t.AnnotationFieldFlag={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};t.AnnotationBorderStyleType={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};t.AnnotationActionEventType={E:"Mouse Enter",X:"Mouse Exit",D:"Mouse Down",U:"Mouse Up",Fo:"Focus",Bl:"Blur",PO:"PageOpen",PC:"PageClose",PV:"PageVisible",PI:"PageInvisible",K:"Keystroke",F:"Format",V:"Validate",C:"Calculate"};t.DocumentActionEventType={WC:"WillClose",WS:"WillSave",DS:"DidSave",WP:"WillPrint",DP:"DidPrint"};t.PageActionEventType={O:"PageOpen",C:"PageClose"};const i={ERRORS:0,WARNINGS:1,INFOS:5};t.VerbosityLevel=i;t.CMapCompressionType={NONE:0,BINARY:1};t.OPS={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};t.PasswordResponses={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let r=i.WARNINGS;function warn(e){r>=i.WARNINGS&&console.log(`Warning: ${e}`)}function unreachable(e){throw new Error(e)}function shadow(e,t,n,i=!1){Object.defineProperty(e,t,{value:n,enumerable:!i,configurable:!0,writable:!1});return n}const s=function BaseExceptionClosure(){function BaseException(e,t){this.constructor===BaseException&&unreachable("Cannot initialize BaseException.");this.message=e;this.name=t}BaseException.prototype=new Error;BaseException.constructor=BaseException;return BaseException}();t.BaseException=s;t.PasswordException=class PasswordException extends s{constructor(e,t){super(e,"PasswordException");this.code=t}};t.UnknownErrorException=class UnknownErrorException extends s{constructor(e,t){super(e,"UnknownErrorException");this.details=t}};t.InvalidPDFException=class InvalidPDFException extends s{constructor(e){super(e,"InvalidPDFException")}};t.MissingPDFException=class MissingPDFException extends s{constructor(e){super(e,"MissingPDFException")}};t.UnexpectedResponseException=class UnexpectedResponseException extends s{constructor(e,t){super(e,"UnexpectedResponseException");this.status=t}};t.FormatError=class FormatError extends s{constructor(e){super(e,"FormatError")}};t.AbortException=class AbortException extends s{constructor(e){super(e,"AbortException")}};function bytesToString(e){"object"==typeof e&&void 0!==e?.length||unreachable("Invalid argument for bytesToString");const t=e.length,n=8192;if(t<n)return String.fromCharCode.apply(null,e);const i=[];for(let r=0;r<t;r+=n){const s=Math.min(r+n,t),o=e.subarray(r,s);i.push(String.fromCharCode.apply(null,o))}return i.join("")}function stringToBytes(e){"string"!=typeof e&&unreachable("Invalid argument for stringToBytes");const t=e.length,n=new Uint8Array(t);for(let i=0;i<t;++i)n[i]=255&e.charCodeAt(i);return n}t.FeatureTest=class FeatureTest{static get isLittleEndian(){return shadow(this,"isLittleEndian",function isLittleEndian(){const e=new Uint8Array(4);e[0]=1;return 1===new Uint32Array(e.buffer,0,1)[0]}())}static get isEvalSupported(){return shadow(this,"isEvalSupported",function isEvalSupported(){try{new Function("");return!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return shadow(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get platform(){return"undefined"==typeof navigator?shadow(this,"platform",{isWin:!1,isMac:!1}):shadow(this,"platform",{isWin:navigator.platform.includes("Win"),isMac:navigator.platform.includes("Mac")})}static get isCSSRoundSupported(){return shadow(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}};const o=[...Array(256).keys()].map((e=>e.toString(16).padStart(2,"0")));t.Util=class Util{static makeHexColor(e,t,n){return`#${o[e]}${o[t]}${o[n]}`}static scaleMinMax(e,t){let n;if(e[0]){if(e[0]<0){n=t[0];t[0]=t[1];t[1]=n}t[0]*=e[0];t[1]*=e[0];if(e[3]<0){n=t[2];t[2]=t[3];t[3]=n}t[2]*=e[3];t[3]*=e[3]}else{n=t[0];t[0]=t[2];t[2]=n;n=t[1];t[1]=t[3];t[3]=n;if(e[1]<0){n=t[2];t[2]=t[3];t[3]=n}t[2]*=e[1];t[3]*=e[1];if(e[2]<0){n=t[0];t[0]=t[1];t[1]=n}t[0]*=e[2];t[1]*=e[2]}t[0]+=e[4];t[1]+=e[4];t[2]+=e[5];t[3]+=e[5]}static transform(e,t){return[e[0]*t[0]+e[2]*t[1],e[1]*t[0]+e[3]*t[1],e[0]*t[2]+e[2]*t[3],e[1]*t[2]+e[3]*t[3],e[0]*t[4]+e[2]*t[5]+e[4],e[1]*t[4]+e[3]*t[5]+e[5]]}static applyTransform(e,t){return[e[0]*t[0]+e[1]*t[2]+t[4],e[0]*t[1]+e[1]*t[3]+t[5]]}static applyInverseTransform(e,t){const n=t[0]*t[3]-t[1]*t[2];return[(e[0]*t[3]-e[1]*t[2]+t[2]*t[5]-t[4]*t[3])/n,(-e[0]*t[1]+e[1]*t[0]+t[4]*t[1]-t[5]*t[0])/n]}static getAxialAlignedBoundingBox(e,t){const n=this.applyTransform(e,t),i=this.applyTransform(e.slice(2,4),t),r=this.applyTransform([e[0],e[3]],t),s=this.applyTransform([e[2],e[1]],t);return[Math.min(n[0],i[0],r[0],s[0]),Math.min(n[1],i[1],r[1],s[1]),Math.max(n[0],i[0],r[0],s[0]),Math.max(n[1],i[1],r[1],s[1])]}static inverseTransform(e){const t=e[0]*e[3]-e[1]*e[2];return[e[3]/t,-e[1]/t,-e[2]/t,e[0]/t,(e[2]*e[5]-e[4]*e[3])/t,(e[4]*e[1]-e[5]*e[0])/t]}static singularValueDecompose2dScale(e){const t=[e[0],e[2],e[1],e[3]],n=e[0]*t[0]+e[1]*t[2],i=e[0]*t[1]+e[1]*t[3],r=e[2]*t[0]+e[3]*t[2],s=e[2]*t[1]+e[3]*t[3],o=(n+s)/2,a=Math.sqrt((n+s)**2-4*(n*s-r*i))/2,c=o+a||1,l=o-a||1;return[Math.sqrt(c),Math.sqrt(l)]}static normalizeRect(e){const t=e.slice(0);if(e[0]>e[2]){t[0]=e[2];t[2]=e[0]}if(e[1]>e[3]){t[1]=e[3];t[3]=e[1]}return t}static intersect(e,t){const n=Math.max(Math.min(e[0],e[2]),Math.min(t[0],t[2])),i=Math.min(Math.max(e[0],e[2]),Math.max(t[0],t[2]));if(n>i)return null;const r=Math.max(Math.min(e[1],e[3]),Math.min(t[1],t[3])),s=Math.min(Math.max(e[1],e[3]),Math.max(t[1],t[3]));return r>s?null:[n,r,i,s]}static bezierBoundingBox(e,t,n,i,r,s,o,a){const c=[],l=[[],[]];let f,d,h,u,p,g,m,b;for(let l=0;l<2;++l){if(0===l){d=6*e-12*n+6*r;f=-3*e+9*n-9*r+3*o;h=3*n-3*e}else{d=6*t-12*i+6*s;f=-3*t+9*i-9*s+3*a;h=3*i-3*t}if(Math.abs(f)<1e-12){if(Math.abs(d)<1e-12)continue;u=-h/d;0<u&&u<1&&c.push(u)}else{m=d*d-4*h*f;b=Math.sqrt(m);if(!(m<0)){p=(-d+b)/(2*f);0<p&&p<1&&c.push(p);g=(-d-b)/(2*f);0<g&&g<1&&c.push(g)}}}let x,y=c.length;const w=y;for(;y--;){u=c[y];x=1-u;l[0][y]=x*x*x*e+3*x*x*u*n+3*x*u*u*r+u*u*u*o;l[1][y]=x*x*x*t+3*x*x*u*i+3*x*u*u*s+u*u*u*a}l[0][w]=e;l[1][w]=t;l[0][w+1]=o;l[1][w+1]=a;l[0].length=l[1].length=w+2;return[Math.min(...l[0]),Math.min(...l[1]),Math.max(...l[0]),Math.max(...l[1])]}};const a=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function stringToUTF8String(e){return decodeURIComponent(escape(e))}t.PromiseCapability=class PromiseCapability{#e=!1;constructor(){this.promise=new Promise(((e,t)=>{this.resolve=t=>{this.#e=!0;e(t)};this.reject=e=>{this.#e=!0;t(e)}}))}get settled(){return this.#e}};let c=null,l=null;t.AnnotationPrefix="pdfjs_internal_id_"},(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.Jbig2Image=void 0;var i=n(1),r=n(3),s=n(6),o=n(7);class Jbig2Error extends i.BaseException{constructor(e){super(`JBIG2 error: ${e}`,"Jbig2Error")}}class ContextCache{getContexts(e){return e in this?this[e]:this[e]=new Int8Array(65536)}}class DecodingContext{constructor(e,t,n){this.data=e;this.start=t;this.end=n}get decoder(){const e=new s.ArithmeticDecoder(this.data,this.start,this.end);return(0,i.shadow)(this,"decoder",e)}get contextCache(){const e=new ContextCache;return(0,i.shadow)(this,"contextCache",e)}}const a=2**31-1,c=-(2**31);function decodeInteger(e,t,n){const i=e.getContexts(t);let r=1;function readBits(e){let t=0;for(let s=0;s<e;s++){const e=n.readBit(i,r);r=r<256?r<<1|e:511&(r<<1|e)|256;t=t<<1|e}return t>>>0}const s=readBits(1),o=readBits(1)?readBits(1)?readBits(1)?readBits(1)?readBits(1)?readBits(32)+4436:readBits(12)+340:readBits(8)+84:readBits(6)+20:readBits(4)+4:readBits(2);let l;0===s?l=o:o>0&&(l=-o);return l>=c&&l<=a?l:null}function decodeIAID(e,t,n){const i=e.getContexts("IAID");let r=1;for(let e=0;e<n;e++){r=r<<1|t.readBit(i,r)}return n<31?r&(1<<n)-1:2147483647&r}const l=["SymbolDictionary",null,null,null,"IntermediateTextRegion",null,"ImmediateTextRegion","ImmediateLosslessTextRegion",null,null,null,null,null,null,null,null,"PatternDictionary",null,null,null,"IntermediateHalftoneRegion",null,"ImmediateHalftoneRegion","ImmediateLosslessHalftoneRegion",null,null,null,null,null,null,null,null,null,null,null,null,"IntermediateGenericRegion",null,"ImmediateGenericRegion","ImmediateLosslessGenericRegion","IntermediateGenericRefinementRegion",null,"ImmediateGenericRefinementRegion","ImmediateLosslessGenericRefinementRegion",null,null,null,null,"PageInformation","EndOfPage","EndOfStripe","EndOfFile","Profiles","Tables",null,null,null,null,null,null,null,null,"Extension"],f=[[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:2,y:-1},{x:-4,y:0},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}],[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:2,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:2,y:-1},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}],[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-2,y:0},{x:-1,y:0}],[{x:-3,y:-1},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-4,y:0},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}]],d=[{coding:[{x:0,y:-1},{x:1,y:-1},{x:-1,y:0}],reference:[{x:0,y:-1},{x:1,y:-1},{x:-1,y:0},{x:0,y:0},{x:1,y:0},{x:-1,y:1},{x:0,y:1},{x:1,y:1}]},{coding:[{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-1,y:0}],reference:[{x:0,y:-1},{x:-1,y:0},{x:0,y:0},{x:1,y:0},{x:0,y:1},{x:1,y:1}]}],h=[39717,1941,229,405],u=[32,8];function decodeBitmap(e,t,n,i,r,s,o,a){if(e){return decodeMMRBitmap(new Reader(a.data,a.start,a.end),t,n,!1)}if(0===i&&!s&&!r&&4===o.length&&3===o[0].x&&-1===o[0].y&&-3===o[1].x&&-1===o[1].y&&2===o[2].x&&-2===o[2].y&&-2===o[3].x&&-2===o[3].y)return function decodeBitmapTemplate0(e,t,n){const i=n.decoder,r=n.contextCache.getContexts("GB"),s=[];let o,a,c,l,f,d,h;for(a=0;a<t;a++){f=s[a]=new Uint8Array(e);d=a<1?f:s[a-1];h=a<2?f:s[a-2];o=h[0]<<13|h[1]<<12|h[2]<<11|d[0]<<7|d[1]<<6|d[2]<<5|d[3]<<4;for(c=0;c<e;c++){f[c]=l=i.readBit(r,o);o=(31735&o)<<1|(c+3<e?h[c+3]<<11:0)|(c+4<e?d[c+4]<<4:0)|l}}return s}(t,n,a);const c=!!s,l=f[i].concat(o);l.sort((function(e,t){return e.y-t.y||e.x-t.x}));const d=l.length,u=new Int8Array(d),p=new Int8Array(d),g=[];let m,b,x=0,y=0,w=0,T=0;for(b=0;b<d;b++){u[b]=l[b].x;p[b]=l[b].y;y=Math.min(y,l[b].x);w=Math.max(w,l[b].x);T=Math.min(T,l[b].y);b<d-1&&l[b].y===l[b+1].y&&l[b].x===l[b+1].x-1?x|=1<<d-1-b:g.push(b)}const C=g.length,S=new Int8Array(C),I=new Int8Array(C),E=new Uint16Array(C);for(m=0;m<C;m++){b=g[m];S[m]=l[b].x;I[m]=l[b].y;E[m]=1<<d-1-b}const P=-y,k=-T,B=t-w,A=h[i];let _=new Uint8Array(t);const R=[],M=a.decoder,v=a.contextCache.getContexts("GB");let L,D,O,F,U,N=0,H=0;for(let e=0;e<n;e++){if(r){N^=M.readBit(v,A);if(N){R.push(_);continue}}_=new Uint8Array(_);R.push(_);for(L=0;L<t;L++){if(c&&s[e][L]){_[L]=0;continue}if(L>=P&&L<B&&e>=k){H=H<<1&x;for(b=0;b<C;b++){D=e+I[b];O=L+S[b];F=R[D][O];if(F){F=E[b];H|=F}}}else{H=0;U=d-1;for(b=0;b<d;b++,U--){O=L+u[b];if(O>=0&&O<t){D=e+p[b];if(D>=0){F=R[D][O];F&&(H|=F<<U)}}}}const n=M.readBit(v,H);_[L]=n}}return R}function decodeRefinement(e,t,n,i,r,s,o,a,c){let l=d[n].coding;0===n&&(l=l.concat([a[0]]));const f=l.length,h=new Int32Array(f),p=new Int32Array(f);let g;for(g=0;g<f;g++){h[g]=l[g].x;p[g]=l[g].y}let m=d[n].reference;0===n&&(m=m.concat([a[1]]));const b=m.length,x=new Int32Array(b),y=new Int32Array(b);for(g=0;g<b;g++){x[g]=m[g].x;y[g]=m[g].y}const w=i[0].length,T=i.length,C=u[n],S=[],I=c.decoder,E=c.contextCache.getContexts("GR");let P=0;for(let n=0;n<t;n++){if(o){P^=I.readBit(E,C);if(P)throw new Jbig2Error("prediction is not supported")}const t=new Uint8Array(e);S.push(t);for(let o=0;o<e;o++){let a,c,l=0;for(g=0;g<f;g++){a=n+p[g];c=o+h[g];a<0||c<0||c>=e?l<<=1:l=l<<1|S[a][c]}for(g=0;g<b;g++){a=n+y[g]-s;c=o+x[g]-r;a<0||a>=T||c<0||c>=w?l<<=1:l=l<<1|i[a][c]}const d=I.readBit(E,l);t[o]=d}}return S}function decodeTextRegion(e,t,n,i,r,s,o,a,c,l,f,d,h,u,p,g,m,b,x){if(e&&t)throw new Jbig2Error("refinement with Huffman is not supported");const y=[];let w,T;for(w=0;w<i;w++){T=new Uint8Array(n);if(r)for(let e=0;e<n;e++)T[e]=r;y.push(T)}const C=m.decoder,S=m.contextCache;let I=e?-u.tableDeltaT.decode(x):-decodeInteger(S,"IADT",C),E=0;w=0;for(;w<s;){I+=e?u.tableDeltaT.decode(x):decodeInteger(S,"IADT",C);E+=e?u.tableFirstS.decode(x):decodeInteger(S,"IAFS",C);let i=E;for(;;){let r=0;o>1&&(r=e?x.readBits(b):decodeInteger(S,"IAIT",C));const s=o*I+r,E=e?u.symbolIDTable.decode(x):decodeIAID(S,C,c),P=t&&(e?x.readBit():decodeInteger(S,"IARI",C));let k=a[E],B=k[0].length,A=k.length;if(P){const e=decodeInteger(S,"IARDW",C),t=decodeInteger(S,"IARDH",C);B+=e;A+=t;k=decodeRefinement(B,A,p,k,(e>>1)+decodeInteger(S,"IARDX",C),(t>>1)+decodeInteger(S,"IARDY",C),!1,g,m)}const _=s-(1&d?0:A-1),R=i-(2&d?B-1:0);let M,v,L;if(l){for(M=0;M<A;M++){T=y[R+M];if(!T)continue;L=k[M];const e=Math.min(n-_,B);switch(h){case 0:for(v=0;v<e;v++)T[_+v]|=L[v];break;case 2:for(v=0;v<e;v++)T[_+v]^=L[v];break;default:throw new Jbig2Error(`operator ${h} is not supported`)}}i+=A-1}else{for(v=0;v<A;v++){T=y[_+v];if(T){L=k[v];switch(h){case 0:for(M=0;M<B;M++)T[R+M]|=L[M];break;case 2:for(M=0;M<B;M++)T[R+M]^=L[M];break;default:throw new Jbig2Error(`operator ${h} is not supported`)}}}i+=B-1}w++;const D=e?u.tableDeltaS.decode(x):decodeInteger(S,"IADS",C);if(null===D)break;i+=D+f}}return y}function readSegmentHeader(e,t){const n={};n.number=(0,r.readUint32)(e,t);const i=e[t+4],s=63&i;if(!l[s])throw new Jbig2Error("invalid segment type: "+s);n.type=s;n.typeName=l[s];n.deferredNonRetain=!!(128&i);const o=!!(64&i),a=e[t+5];let c=a>>5&7;const f=[31&a];let d=t+6;if(7===a){c=536870911&(0,r.readUint32)(e,d-1);d+=3;let t=c+7>>3;f[0]=e[d++];for(;--t>0;)f.push(e[d++])}else if(5===a||6===a)throw new Jbig2Error("invalid referred-to flags");n.retainBits=f;let h=4;n.number<=256?h=1:n.number<=65536&&(h=2);const u=[];let g,m;for(g=0;g<c;g++){let t;t=1===h?e[d]:2===h?(0,r.readUint16)(e,d):(0,r.readUint32)(e,d);u.push(t);d+=h}n.referredTo=u;if(o){n.pageAssociation=(0,r.readUint32)(e,d);d+=4}else n.pageAssociation=e[d++];n.length=(0,r.readUint32)(e,d);d+=4;if(4294967295===n.length){if(38!==s)throw new Jbig2Error("invalid unknown segment length");{const t=readRegionSegmentInformation(e,d),i=!!(1&e[d+p]),r=6,s=new Uint8Array(r);if(!i){s[0]=255;s[1]=172}s[2]=t.height>>>24&255;s[3]=t.height>>16&255;s[4]=t.height>>8&255;s[5]=255&t.height;for(g=d,m=e.length;g<m;g++){let t=0;for(;t<r&&s[t]===e[g+t];)t++;if(t===r){n.length=g+r;break}}if(4294967295===n.length)throw new Jbig2Error("segment end was not found")}}n.headerEnd=d;return n}function readSegments(e,t,n,i){const r=[];let s=n;for(;s<i;){const n=readSegmentHeader(t,s);s=n.headerEnd;const i={header:n,data:t};if(!e.randomAccess){i.start=s;s+=n.length;i.end=s}r.push(i);if(51===n.type)break}if(e.randomAccess)for(let e=0,t=r.length;e<t;e++){r[e].start=s;s+=r[e].header.length;r[e].end=s}return r}function readRegionSegmentInformation(e,t){return{width:(0,r.readUint32)(e,t),height:(0,r.readUint32)(e,t+4),x:(0,r.readUint32)(e,t+8),y:(0,r.readUint32)(e,t+12),combinationOperator:7&e[t+16]}}const p=17;function processSegment(e,t){const n=e.header,i=e.data,s=e.end;let o,a,c,l,f=e.start;switch(n.type){case 0:const e={},t=(0,r.readUint16)(i,f);e.huffman=!!(1&t);e.refinement=!!(2&t);e.huffmanDHSelector=t>>2&3;e.huffmanDWSelector=t>>4&3;e.bitmapSizeSelector=t>>6&1;e.aggregationInstancesSelector=t>>7&1;e.bitmapCodingContextUsed=!!(256&t);e.bitmapCodingContextRetained=!!(512&t);e.template=t>>10&3;e.refinementTemplate=t>>12&1;f+=2;if(!e.huffman){l=0===e.template?4:1;a=[];for(c=0;c<l;c++){a.push({x:(0,r.readInt8)(i,f),y:(0,r.readInt8)(i,f+1)});f+=2}e.at=a}if(e.refinement&&!e.refinementTemplate){a=[];for(c=0;c<2;c++){a.push({x:(0,r.readInt8)(i,f),y:(0,r.readInt8)(i,f+1)});f+=2}e.refinementAt=a}e.numberOfExportedSymbols=(0,r.readUint32)(i,f);f+=4;e.numberOfNewSymbols=(0,r.readUint32)(i,f);f+=4;o=[e,n.number,n.referredTo,i,f,s];break;case 6:case 7:const d={};d.info=readRegionSegmentInformation(i,f);f+=p;const h=(0,r.readUint16)(i,f);f+=2;d.huffman=!!(1&h);d.refinement=!!(2&h);d.logStripSize=h>>2&3;d.stripSize=1<<d.logStripSize;d.referenceCorner=h>>4&3;d.transposed=!!(64&h);d.combinationOperator=h>>7&3;d.defaultPixelValue=h>>9&1;d.dsOffset=h<<17>>27;d.refinementTemplate=h>>15&1;if(d.huffman){const e=(0,r.readUint16)(i,f);f+=2;d.huffmanFS=3&e;d.huffmanDS=e>>2&3;d.huffmanDT=e>>4&3;d.huffmanRefinementDW=e>>6&3;d.huffmanRefinementDH=e>>8&3;d.huffmanRefinementDX=e>>10&3;d.huffmanRefinementDY=e>>12&3;d.huffmanRefinementSizeSelector=!!(16384&e)}if(d.refinement&&!d.refinementTemplate){a=[];for(c=0;c<2;c++){a.push({x:(0,r.readInt8)(i,f),y:(0,r.readInt8)(i,f+1)});f+=2}d.refinementAt=a}d.numberOfSymbolInstances=(0,r.readUint32)(i,f);f+=4;o=[d,n.referredTo,i,f,s];break;case 16:const u={},g=i[f++];u.mmr=!!(1&g);u.template=g>>1&3;u.patternWidth=i[f++];u.patternHeight=i[f++];u.maxPatternIndex=(0,r.readUint32)(i,f);f+=4;o=[u,n.number,i,f,s];break;case 22:case 23:const m={};m.info=readRegionSegmentInformation(i,f);f+=p;const b=i[f++];m.mmr=!!(1&b);m.template=b>>1&3;m.enableSkip=!!(8&b);m.combinationOperator=b>>4&7;m.defaultPixelValue=b>>7&1;m.gridWidth=(0,r.readUint32)(i,f);f+=4;m.gridHeight=(0,r.readUint32)(i,f);f+=4;m.gridOffsetX=4294967295&(0,r.readUint32)(i,f);f+=4;m.gridOffsetY=4294967295&(0,r.readUint32)(i,f);f+=4;m.gridVectorX=(0,r.readUint16)(i,f);f+=2;m.gridVectorY=(0,r.readUint16)(i,f);f+=2;o=[m,n.referredTo,i,f,s];break;case 38:case 39:const x={};x.info=readRegionSegmentInformation(i,f);f+=p;const y=i[f++];x.mmr=!!(1&y);x.template=y>>1&3;x.prediction=!!(8&y);if(!x.mmr){l=0===x.template?4:1;a=[];for(c=0;c<l;c++){a.push({x:(0,r.readInt8)(i,f),y:(0,r.readInt8)(i,f+1)});f+=2}x.at=a}o=[x,i,f,s];break;case 48:const w={width:(0,r.readUint32)(i,f),height:(0,r.readUint32)(i,f+4),resolutionX:(0,r.readUint32)(i,f+8),resolutionY:(0,r.readUint32)(i,f+12)};4294967295===w.height&&delete w.height;const T=i[f+16];(0,r.readUint16)(i,f+17);w.lossless=!!(1&T);w.refinement=!!(2&T);w.defaultPixelValue=T>>2&1;w.combinationOperator=T>>3&3;w.requiresBuffer=!!(32&T);w.combinationOperatorOverride=!!(64&T);o=[w];break;case 49:case 50:case 51:case 62:break;case 53:o=[n.number,i,f,s];break;default:throw new Jbig2Error(`segment type ${n.typeName}(${n.type}) is not implemented`)}const d="on"+n.typeName;d in t&&t[d].apply(t,o)}function processSegments(e,t){for(let n=0,i=e.length;n<i;n++)processSegment(e[n],t)}class SimpleSegmentVisitor{onPageInformation(e){this.currentPageInfo=e;const t=e.width+7>>3,n=new Uint8ClampedArray(t*e.height);e.defaultPixelValue&&n.fill(255);this.buffer=n}drawBitmap(e,t){const n=this.currentPageInfo,i=e.width,r=e.height,s=n.width+7>>3,o=n.combinationOperatorOverride?e.combinationOperator:n.combinationOperator,a=this.buffer,c=128>>(7&e.x);let l,f,d,h,u=e.y*s+(e.x>>3);switch(o){case 0:for(l=0;l<r;l++){d=c;h=u;for(f=0;f<i;f++){t[l][f]&&(a[h]|=d);d>>=1;if(!d){d=128;h++}}u+=s}break;case 2:for(l=0;l<r;l++){d=c;h=u;for(f=0;f<i;f++){t[l][f]&&(a[h]^=d);d>>=1;if(!d){d=128;h++}}u+=s}break;default:throw new Jbig2Error(`operator ${o} is not supported`)}}onImmediateGenericRegion(e,t,n,i){const r=e.info,s=new DecodingContext(t,n,i),o=decodeBitmap(e.mmr,r.width,r.height,e.template,e.prediction,null,e.at,s);this.drawBitmap(r,o)}onImmediateLosslessGenericRegion(){this.onImmediateGenericRegion(...arguments)}onSymbolDictionary(e,t,n,i,s,o){let a,c;if(e.huffman){a=function getSymbolDictionaryHuffmanTables(e,t,n){let i,r,s,o,a=0;switch(e.huffmanDHSelector){case 0:case 1:i=getStandardTable(e.huffmanDHSelector+4);break;case 3:i=getCustomHuffmanTable(a,t,n);a++;break;default:throw new Jbig2Error("invalid Huffman DH selector")}switch(e.huffmanDWSelector){case 0:case 1:r=getStandardTable(e.huffmanDWSelector+2);break;case 3:r=getCustomHuffmanTable(a,t,n);a++;break;default:throw new Jbig2Error("invalid Huffman DW selector")}if(e.bitmapSizeSelector){s=getCustomHuffmanTable(a,t,n);a++}else s=getStandardTable(1);o=e.aggregationInstancesSelector?getCustomHuffmanTable(a,t,n):getStandardTable(1);return{tableDeltaHeight:i,tableDeltaWidth:r,tableBitmapSize:s,tableAggregateInstances:o}}(e,n,this.customTables);c=new Reader(i,s,o)}let l=this.symbols;l||(this.symbols=l={});const f=[];for(const e of n){const t=l[e];t&&f.push(...t)}const d=new DecodingContext(i,s,o);l[t]=function decodeSymbolDictionary(e,t,n,i,s,o,a,c,l,f,d,h){if(e&&t)throw new Jbig2Error("symbol refinement with Huffman is not supported");const u=[];let p=0,g=(0,r.log2)(n.length+i);const m=d.decoder,b=d.contextCache;let x,y;if(e){x=getStandardTable(1);y=[];g=Math.max(g,1)}for(;u.length<i;){p+=e?o.tableDeltaHeight.decode(h):decodeInteger(b,"IADH",m);let i=0,r=0;const s=e?y.length:0;for(;;){const s=e?o.tableDeltaWidth.decode(h):decodeInteger(b,"IADW",m);if(null===s)break;i+=s;r+=i;let x;if(t){const r=decodeInteger(b,"IAAI",m);if(r>1)x=decodeTextRegion(e,t,i,p,0,r,1,n.concat(u),g,0,0,1,0,o,l,f,d,0,h);else{const e=decodeIAID(b,m,g),t=decodeInteger(b,"IARDX",m),r=decodeInteger(b,"IARDY",m);x=decodeRefinement(i,p,l,e<n.length?n[e]:u[e-n.length],t,r,!1,f,d)}u.push(x)}else if(e)y.push(i);else{x=decodeBitmap(!1,i,p,a,!1,null,c,d);u.push(x)}}if(e&&!t){const e=o.tableBitmapSize.decode(h);h.byteAlign();let t;if(0===e)t=readUncompressedBitmap(h,r,p);else{const n=h.end,i=h.position+e;h.end=i;t=decodeMMRBitmap(h,r,p,!1);h.end=n;h.position=i}const n=y.length;if(s===n-1)u.push(t);else{let e,i,r,o,a,c=0;for(e=s;e<n;e++){o=y[e];r=c+o;a=[];for(i=0;i<p;i++)a.push(t[i].subarray(c,r));u.push(a);c=r}}}}const w=[],T=[];let C,S,I=!1;const E=n.length+i;for(;T.length<E;){let t=e?x.decode(h):decodeInteger(b,"IAEX",m);for(;t--;)T.push(I);I=!I}for(C=0,S=n.length;C<S;C++)T[C]&&w.push(n[C]);for(let e=0;e<i;C++,e++)T[C]&&w.push(u[e]);return w}(e.huffman,e.refinement,f,e.numberOfNewSymbols,e.numberOfExportedSymbols,a,e.template,e.at,e.refinementTemplate,e.refinementAt,d,c)}onImmediateTextRegion(e,t,n,i,s){const o=e.info;let a,c;const l=this.symbols,f=[];for(const e of t){const t=l[e];t&&f.push(...t)}const d=(0,r.log2)(f.length);if(e.huffman){c=new Reader(n,i,s);a=function getTextRegionHuffmanTables(e,t,n,i,r){const s=[];for(let e=0;e<=34;e++){const t=r.readBits(4);s.push(new HuffmanLine([e,t,0,0]))}const o=new HuffmanTable(s,!1);s.length=0;for(let e=0;e<i;){const t=o.decode(r);if(t>=32){let n,i,o;switch(t){case 32:if(0===e)throw new Jbig2Error("no previous value in symbol ID table");i=r.readBits(2)+3;n=s[e-1].prefixLength;break;case 33:i=r.readBits(3)+3;n=0;break;case 34:i=r.readBits(7)+11;n=0;break;default:throw new Jbig2Error("invalid code length in symbol ID table")}for(o=0;o<i;o++){s.push(new HuffmanLine([e,n,0,0]));e++}}else{s.push(new HuffmanLine([e,t,0,0]));e++}}r.byteAlign();const a=new HuffmanTable(s,!1);let c,l,f,d=0;switch(e.huffmanFS){case 0:case 1:c=getStandardTable(e.huffmanFS+6);break;case 3:c=getCustomHuffmanTable(d,t,n);d++;break;default:throw new Jbig2Error("invalid Huffman FS selector")}switch(e.huffmanDS){case 0:case 1:case 2:l=getStandardTable(e.huffmanDS+8);break;case 3:l=getCustomHuffmanTable(d,t,n);d++;break;default:throw new Jbig2Error("invalid Huffman DS selector")}switch(e.huffmanDT){case 0:case 1:case 2:f=getStandardTable(e.huffmanDT+11);break;case 3:f=getCustomHuffmanTable(d,t,n);d++;break;default:throw new Jbig2Error("invalid Huffman DT selector")}if(e.refinement)throw new Jbig2Error("refinement with Huffman is not supported");return{symbolIDTable:a,tableFirstS:c,tableDeltaS:l,tableDeltaT:f}}(e,t,this.customTables,f.length,c)}const h=new DecodingContext(n,i,s),u=decodeTextRegion(e.huffman,e.refinement,o.width,o.height,e.defaultPixelValue,e.numberOfSymbolInstances,e.stripSize,f,d,e.transposed,e.dsOffset,e.referenceCorner,e.combinationOperator,a,e.refinementTemplate,e.refinementAt,h,e.logStripSize,c);this.drawBitmap(o,u)}onImmediateLosslessTextRegion(){this.onImmediateTextRegion(...arguments)}onPatternDictionary(e,t,n,i,r){let s=this.patterns;s||(this.patterns=s={});const o=new DecodingContext(n,i,r);s[t]=function decodePatternDictionary(e,t,n,i,r,s){const o=[];if(!e){o.push({x:-t,y:0});0===r&&o.push({x:-3,y:-1},{x:2,y:-2},{x:-2,y:-2})}const a=decodeBitmap(e,(i+1)*t,n,r,!1,null,o,s),c=[];for(let e=0;e<=i;e++){const i=[],r=t*e,s=r+t;for(let e=0;e<n;e++)i.push(a[e].subarray(r,s));c.push(i)}return c}(e.mmr,e.patternWidth,e.patternHeight,e.maxPatternIndex,e.template,o)}onImmediateHalftoneRegion(e,t,n,i,s){const o=this.patterns[t[0]],a=e.info,c=new DecodingContext(n,i,s),l=function decodeHalftoneRegion(e,t,n,i,s,o,a,c,l,f,d,h,u,p,g){if(a)throw new Jbig2Error("skip is not supported");if(0!==c)throw new Jbig2Error(`operator "${c}" is not supported in halftone region`);const m=[];let b,x,y;for(b=0;b<s;b++){y=new Uint8Array(i);if(o)for(x=0;x<i;x++)y[x]=o;m.push(y)}const w=t.length,T=t[0],C=T[0].length,S=T.length,I=(0,r.log2)(w),E=[];if(!e){E.push({x:n<=1?3:2,y:-1});0===n&&E.push({x:-3,y:-1},{x:2,y:-2},{x:-2,y:-2})}const P=[];let k,B,A,_,R,M,v,L,D,O,F;e&&(k=new Reader(g.data,g.start,g.end));for(b=I-1;b>=0;b--){B=e?decodeMMRBitmap(k,l,f,!0):decodeBitmap(!1,l,f,n,!1,null,E,g);P[b]=B}for(A=0;A<f;A++)for(_=0;_<l;_++){R=0;M=0;for(x=I-1;x>=0;x--){R^=P[x][A][_];M|=R<<x}v=t[M];L=d+A*p+_*u>>8;D=h+A*u-_*p>>8;if(L>=0&&L+C<=i&&D>=0&&D+S<=s)for(b=0;b<S;b++){F=m[D+b];O=v[b];for(x=0;x<C;x++)F[L+x]|=O[x]}else{let e,t;for(b=0;b<S;b++){t=D+b;if(!(t<0||t>=s)){F=m[t];O=v[b];for(x=0;x<C;x++){e=L+x;e>=0&&e<i&&(F[e]|=O[x])}}}}}return m}(e.mmr,o,e.template,a.width,a.height,e.defaultPixelValue,e.enableSkip,e.combinationOperator,e.gridWidth,e.gridHeight,e.gridOffsetX,e.gridOffsetY,e.gridVectorX,e.gridVectorY,c);this.drawBitmap(a,l)}onImmediateLosslessHalftoneRegion(){this.onImmediateHalftoneRegion(...arguments)}onTables(e,t,n,i){let s=this.customTables;s||(this.customTables=s={});s[e]=function decodeTablesSegment(e,t,n){const i=e[t],s=4294967295&(0,r.readUint32)(e,t+1),o=4294967295&(0,r.readUint32)(e,t+5),a=new Reader(e,t+9,n),c=1+(i>>1&7),l=1+(i>>4&7),f=[];let d,h,u=s;do{d=a.readBits(c);h=a.readBits(l);f.push(new HuffmanLine([u,d,h,0]));u+=1<<h}while(u<o);d=a.readBits(c);f.push(new HuffmanLine([s-1,d,32,0,"lower"]));d=a.readBits(c);f.push(new HuffmanLine([o,d,32,0]));if(1&i){d=a.readBits(c);f.push(new HuffmanLine([d,0]))}return new HuffmanTable(f,!1)}(t,n,i)}}class HuffmanLine{constructor(e){if(2===e.length){this.isOOB=!0;this.rangeLow=0;this.prefixLength=e[0];this.rangeLength=0;this.prefixCode=e[1];this.isLowerRange=!1}else{this.isOOB=!1;this.rangeLow=e[0];this.prefixLength=e[1];this.rangeLength=e[2];this.prefixCode=e[3];this.isLowerRange="lower"===e[4]}}}class HuffmanTreeNode{constructor(e){this.children=[];if(e){this.isLeaf=!0;this.rangeLength=e.rangeLength;this.rangeLow=e.rangeLow;this.isLowerRange=e.isLowerRange;this.isOOB=e.isOOB}else this.isLeaf=!1}buildTree(e,t){const n=e.prefixCode>>t&1;if(t<=0)this.children[n]=new HuffmanTreeNode(e);else{let i=this.children[n];i||(this.children[n]=i=new HuffmanTreeNode(null));i.buildTree(e,t-1)}}decodeNode(e){if(this.isLeaf){if(this.isOOB)return null;const t=e.readBits(this.rangeLength);return this.rangeLow+(this.isLowerRange?-t:t)}const t=this.children[e.readBit()];if(!t)throw new Jbig2Error("invalid Huffman data");return t.decodeNode(e)}}class HuffmanTable{constructor(e,t){t||this.assignPrefixCodes(e);this.rootNode=new HuffmanTreeNode(null);for(let t=0,n=e.length;t<n;t++){const n=e[t];n.prefixLength>0&&this.rootNode.buildTree(n,n.prefixLength-1)}}decode(e){return this.rootNode.decodeNode(e)}assignPrefixCodes(e){const t=e.length;let n=0;for(let i=0;i<t;i++)n=Math.max(n,e[i].prefixLength);const i=new Uint32Array(n+1);for(let n=0;n<t;n++)i[e[n].prefixLength]++;let r,s,o,a=1,c=0;i[0]=0;for(;a<=n;){c=c+i[a-1]<<1;r=c;s=0;for(;s<t;){o=e[s];if(o.prefixLength===a){o.prefixCode=r;r++}s++}a++}}}const g={};function getStandardTable(e){let t,n=g[e];if(n)return n;switch(e){case 1:t=[[0,1,4,0],[16,2,8,2],[272,3,16,6],[65808,3,32,7]];break;case 2:t=[[0,1,0,0],[1,2,0,2],[2,3,0,6],[3,4,3,14],[11,5,6,30],[75,6,32,62],[6,63]];break;case 3:t=[[-256,8,8,254],[0,1,0,0],[1,2,0,2],[2,3,0,6],[3,4,3,14],[11,5,6,30],[-257,8,32,255,"lower"],[75,7,32,126],[6,62]];break;case 4:t=[[1,1,0,0],[2,2,0,2],[3,3,0,6],[4,4,3,14],[12,5,6,30],[76,5,32,31]];break;case 5:t=[[-255,7,8,126],[1,1,0,0],[2,2,0,2],[3,3,0,6],[4,4,3,14],[12,5,6,30],[-256,7,32,127,"lower"],[76,6,32,62]];break;case 6:t=[[-2048,5,10,28],[-1024,4,9,8],[-512,4,8,9],[-256,4,7,10],[-128,5,6,29],[-64,5,5,30],[-32,4,5,11],[0,2,7,0],[128,3,7,2],[256,3,8,3],[512,4,9,12],[1024,4,10,13],[-2049,6,32,62,"lower"],[2048,6,32,63]];break;case 7:t=[[-1024,4,9,8],[-512,3,8,0],[-256,4,7,9],[-128,5,6,26],[-64,5,5,27],[-32,4,5,10],[0,4,5,11],[32,5,5,28],[64,5,6,29],[128,4,7,12],[256,3,8,1],[512,3,9,2],[1024,3,10,3],[-1025,5,32,30,"lower"],[2048,5,32,31]];break;case 8:t=[[-15,8,3,252],[-7,9,1,508],[-5,8,1,253],[-3,9,0,509],[-2,7,0,124],[-1,4,0,10],[0,2,1,0],[2,5,0,26],[3,6,0,58],[4,3,4,4],[20,6,1,59],[22,4,4,11],[38,4,5,12],[70,5,6,27],[134,5,7,28],[262,6,7,60],[390,7,8,125],[646,6,10,61],[-16,9,32,510,"lower"],[1670,9,32,511],[2,1]];break;case 9:t=[[-31,8,4,252],[-15,9,2,508],[-11,8,2,253],[-7,9,1,509],[-5,7,1,124],[-3,4,1,10],[-1,3,1,2],[1,3,1,3],[3,5,1,26],[5,6,1,58],[7,3,5,4],[39,6,2,59],[43,4,5,11],[75,4,6,12],[139,5,7,27],[267,5,8,28],[523,6,8,60],[779,7,9,125],[1291,6,11,61],[-32,9,32,510,"lower"],[3339,9,32,511],[2,0]];break;case 10:t=[[-21,7,4,122],[-5,8,0,252],[-4,7,0,123],[-3,5,0,24],[-2,2,2,0],[2,5,0,25],[3,6,0,54],[4,7,0,124],[5,8,0,253],[6,2,6,1],[70,5,5,26],[102,6,5,55],[134,6,6,56],[198,6,7,57],[326,6,8,58],[582,6,9,59],[1094,6,10,60],[2118,7,11,125],[-22,8,32,254,"lower"],[4166,8,32,255],[2,2]];break;case 11:t=[[1,1,0,0],[2,2,1,2],[4,4,0,12],[5,4,1,13],[7,5,1,28],[9,5,2,29],[13,6,2,60],[17,7,2,122],[21,7,3,123],[29,7,4,124],[45,7,5,125],[77,7,6,126],[141,7,32,127]];break;case 12:t=[[1,1,0,0],[2,2,0,2],[3,3,1,6],[5,5,0,28],[6,5,1,29],[8,6,1,60],[10,7,0,122],[11,7,1,123],[13,7,2,124],[17,7,3,125],[25,7,4,126],[41,8,5,254],[73,8,32,255]];break;case 13:t=[[1,1,0,0],[2,3,0,4],[3,4,0,12],[4,5,0,28],[5,4,1,13],[7,3,3,5],[15,6,1,58],[17,6,2,59],[21,6,3,60],[29,6,4,61],[45,6,5,62],[77,7,6,126],[141,7,32,127]];break;case 14:t=[[-2,3,0,4],[-1,3,0,5],[0,1,0,0],[1,3,0,6],[2,3,0,7]];break;case 15:t=[[-24,7,4,124],[-8,6,2,60],[-4,5,1,28],[-2,4,0,12],[-1,3,0,4],[0,1,0,0],[1,3,0,5],[2,4,0,13],[3,5,1,29],[5,6,2,61],[9,7,4,125],[-25,7,32,126,"lower"],[25,7,32,127]];break;default:throw new Jbig2Error(`standard table B.${e} does not exist`)}for(let e=0,n=t.length;e<n;e++)t[e]=new HuffmanLine(t[e]);n=new HuffmanTable(t,!0);g[e]=n;return n}class Reader{constructor(e,t,n){this.data=e;this.start=t;this.end=n;this.position=t;this.shift=-1;this.currentByte=0}readBit(){if(this.shift<0){if(this.position>=this.end)throw new Jbig2Error("end of data while reading bit");this.currentByte=this.data[this.position++];this.shift=7}const e=this.currentByte>>this.shift&1;this.shift--;return e}readBits(e){let t,n=0;for(t=e-1;t>=0;t--)n|=this.readBit()<<t;return n}byteAlign(){this.shift=-1}next(){return this.position>=this.end?-1:this.data[this.position++]}}function getCustomHuffmanTable(e,t,n){let i=0;for(let r=0,s=t.length;r<s;r++){const s=n[t[r]];if(s){if(e===i)return s;i++}}throw new Jbig2Error("can't find custom Huffman table")}function readUncompressedBitmap(e,t,n){const i=[];for(let r=0;r<n;r++){const n=new Uint8Array(t);i.push(n);for(let i=0;i<t;i++)n[i]=e.readBit();e.byteAlign()}return i}function decodeMMRBitmap(e,t,n,i){const r={K:-1,Columns:t,Rows:n,BlackIs1:!0,EndOfBlock:i},s=new o.CCITTFaxDecoder(e,r),a=[];let c,l=!1;for(let e=0;e<n;e++){const e=new Uint8Array(t);a.push(e);let n=-1;for(let i=0;i<t;i++){if(n<0){c=s.readNextChar();if(-1===c){c=0;l=!0}n=7}e[i]=c>>n&1;n--}}if(i&&!l){const e=5;for(let t=0;t<e&&-1!==s.readNextChar();t++);}return a}t.Jbig2Image=class Jbig2Image{parseChunks(e){return function parseJbig2Chunks(e){const t=new SimpleSegmentVisitor;for(let n=0,i=e.length;n<i;n++){const i=e[n];processSegments(readSegments({},i.data,i.start,i.end),t)}return t.buffer}(e)}parse(e){const{imgData:t,width:n,height:i}=function parseJbig2(e){const t=e.length;let n=0;if(151!==e[n]||74!==e[n+1]||66!==e[n+2]||50!==e[n+3]||13!==e[n+4]||10!==e[n+5]||26!==e[n+6]||10!==e[n+7])throw new Jbig2Error("parseJbig2 - invalid header.");const i=Object.create(null);n+=8;const s=e[n++];i.randomAccess=!(1&s);if(!(2&s)){i.numberOfPages=(0,r.readUint32)(e,n);n+=4}const o=readSegments(i,e,n,t),a=new SimpleSegmentVisitor;processSegments(o,a);const{width:c,height:l}=a.currentPageInfo,f=a.buffer,d=new Uint8ClampedArray(c*l);let h=0,u=0;for(let e=0;e<l;e++){let e,t=0;for(let n=0;n<c;n++){if(!t){t=128;e=f[u++]}d[h++]=e&t?0:255;t>>=1}}return{imgData:d,width:c,height:l}}(e);this.width=n;this.height=i;return t}}},(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.XRefParseException=t.XRefEntryException=t.ParserEOFException=t.PDF_VERSION_REGEXP=t.MissingDataException=void 0;t.arrayBuffersToBytes=function arrayBuffersToBytes(e){const t=e.length;if(0===t)return new Uint8Array(0);if(1===t)return new Uint8Array(e[0]);let n=0;for(let i=0;i<t;i++)n+=e[i].byteLength;const i=new Uint8Array(n);let r=0;for(let n=0;n<t;n++){const t=new Uint8Array(e[n]);i.set(t,r);r+=t.byteLength}return i};t.collectActions=function collectActions(e,t,n){const s=Object.create(null),o=getInheritableProperty({dict:t,key:"AA",stopWhenFound:!1});if(o)for(let t=o.length-1;t>=0;t--){const i=o[t];if(i instanceof r.Dict)for(const t of i.getKeys()){const o=n[t];if(!o)continue;const a=[];_collectJS(i.getRaw(t),e,a,new r.RefSet);a.length>0&&(s[o]=a)}}if(t.has("A")){const n=[];_collectJS(t.get("A"),e,n,new r.RefSet);n.length>0&&(s.Action=n)}return(0,i.objectSize)(s)>0?s:null};t.encodeToXmlString=function encodeToXmlString(e){const t=[];let n=0;for(let i=0,r=e.length;i<r;i++){const r=e.codePointAt(i);if(32<=r&&r<=126){const s=a[r];if(s){n<i&&t.push(e.substring(n,i));t.push(s);n=i+1}}else{n<i&&t.push(e.substring(n,i));t.push(`&#x${r.toString(16).toUpperCase()};`);r>55295&&(r<57344||r>65533)&&i++;n=i+1}}if(0===t.length)return e;n<e.length&&t.push(e.substring(n,e.length));return t.join("")};t.escapePDFName=function escapePDFName(e){const t=[];let n=0;for(let i=0,r=e.length;i<r;i++){const r=e.charCodeAt(i);if(r<33||r>126||35===r||40===r||41===r||60===r||62===r||91===r||93===r||123===r||125===r||47===r||37===r){n<i&&t.push(e.substring(n,i));t.push(`#${r.toString(16)}`);n=i+1}}if(0===t.length)return e;n<e.length&&t.push(e.substring(n,e.length));return t.join("")};t.escapeString=function escapeString(e){return e.replaceAll(/([()\\\n\r])/g,(e=>"\n"===e?"\\n":"\r"===e?"\\r":`\\${e}`))};t.getInheritableProperty=getInheritableProperty;t.getLookupTableFactory=function getLookupTableFactory(e){let t;return function(){if(e){t=Object.create(null);e(t);e=null}return t}};t.getNewAnnotationsMap=function getNewAnnotationsMap(e){if(!e)return null;const t=new Map;for(const[n,r]of e){if(!n.startsWith(i.AnnotationEditorPrefix))continue;let e=t.get(r.pageIndex);if(!e){e=[];t.set(r.pageIndex,e)}e.push(r)}return t.size>0?t:null};t.getRotationMatrix=function getRotationMatrix(e,t,n){switch(e){case 90:return[0,1,-1,0,t,0];case 180:return[-1,0,0,-1,t,n];case 270:return[0,-1,1,0,0,n];default:throw new Error("Invalid rotation")}};t.isAscii=function isAscii(e){return/^[\x00-\x7F]*$/.test(e)};t.isWhiteSpace=function isWhiteSpace(e){return 32===e||9===e||13===e||10===e};t.log2=function log2(e){if(e<=0)return 0;return Math.ceil(Math.log2(e))};t.numberToString=function numberToString(e){if(Number.isInteger(e))return e.toString();const t=Math.round(100*e);if(t%100==0)return(t/100).toString();if(t%10==0)return e.toFixed(1);return e.toFixed(2)};t.parseXFAPath=function parseXFAPath(e){const t=/(.+)\[(\d+)\]$/;return e.split(".").map((e=>{const n=e.match(t);return n?{name:n[1],pos:parseInt(n[2],10)}:{name:e,pos:0}}))};t.readInt8=function readInt8(e,t){return e[t]<<24>>24};t.readUint16=function readUint16(e,t){return e[t]<<8|e[t+1]};t.readUint32=function readUint32(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0};t.recoverJsURL=function recoverJsURL(e){const t=new RegExp("^\\s*("+["app.launchURL","window.open","xfa.host.gotoURL"].join("|").replaceAll(".","\\.")+")\\((?:'|\")([^'\"]*)(?:'|\")(?:,\\s*(\\w+)\\)|\\))","i").exec(e);if(t?.[2]){const e=t[2];let n=!1;"true"===t[3]&&"app.launchURL"===t[1]&&(n=!0);return{url:e,newWindow:n}}return null};t.stringToUTF16HexString=function stringToUTF16HexString(e){const t=[];for(let n=0,i=e.length;n<i;n++){const i=e.charCodeAt(n);t.push((i>>8&255).toString(16).padStart(2,"0"),(255&i).toString(16).padStart(2,"0"))}return t.join("")};t.stringToUTF16String=function stringToUTF16String(e,t=!1){const n=[];t&&n.push("þÿ");for(let t=0,i=e.length;t<i;t++){const i=e.charCodeAt(t);n.push(String.fromCharCode(i>>8&255),String.fromCharCode(255&i))}return n.join("")};t.toRomanNumerals=function toRomanNumerals(e,t=!1){(0,i.assert)(Number.isInteger(e)&&e>0,"The number should be a positive integer.");const n=[];let r;for(;e>=1e3;){e-=1e3;n.push("M")}r=e/100|0;e%=100;n.push(o[r]);r=e/10|0;e%=10;n.push(o[10+r]);n.push(o[20+e]);const s=n.join("");return t?s.toLowerCase():s};t.validateCSSFont=function validateCSSFont(e){const t=new Set(["100","200","300","400","500","600","700","800","900","1000","normal","bold","bolder","lighter"]),{fontFamily:n,fontWeight:i,italicAngle:r}=e;if(!validateFontName(n,!0))return!1;const s=i?i.toString():"";e.fontWeight=t.has(s)?s:"400";const o=parseFloat(r);e.italicAngle=isNaN(o)||o<-90||o>90?"14":r.toString();return!0};t.validateFontName=validateFontName;var i=n(1),r=n(4),s=n(5);t.PDF_VERSION_REGEXP=/^[1-9]\.\d$/;class MissingDataException extends i.BaseException{constructor(e,t){super(`Missing data [${e}, ${t})`,"MissingDataException");this.begin=e;this.end=t}}t.MissingDataException=MissingDataException;class ParserEOFException extends i.BaseException{constructor(e){super(e,"ParserEOFException")}}t.ParserEOFException=ParserEOFException;class XRefEntryException extends i.BaseException{constructor(e){super(e,"XRefEntryException")}}t.XRefEntryException=XRefEntryException;class XRefParseException extends i.BaseException{constructor(e){super(e,"XRefParseException")}}t.XRefParseException=XRefParseException;function getInheritableProperty({dict:e,key:t,getArray:n=!1,stopWhenFound:i=!0}){let s;const o=new r.RefSet;for(;e instanceof r.Dict&&(!e.objId||!o.has(e.objId));){e.objId&&o.put(e.objId);const r=n?e.getArray(t):e.get(t);if(void 0!==r){if(i)return r;(s||=[]).push(r)}e=e.get("Parent")}return s}const o=["","C","CC","CCC","CD","D","DC","DCC","DCCC","CM","","X","XX","XXX","XL","L","LX","LXX","LXXX","XC","","I","II","III","IV","V","VI","VII","VIII","IX"];function _collectJS(e,t,n,o){if(!e)return;let a=null;if(e instanceof r.Ref){if(o.has(e))return;a=e;o.put(a);e=t.fetch(e)}if(Array.isArray(e))for(const i of e)_collectJS(i,t,n,o);else if(e instanceof r.Dict){if((0,r.isName)(e.get("S"),"JavaScript")){const t=e.get("JS");let r;t instanceof s.BaseStream?r=t.getString():"string"==typeof t&&(r=t);r&&=(0,i.stringToPDFString)(r).replaceAll("\0","");r&&n.push(r)}_collectJS(e.getRaw("Next"),t,n,o)}a&&o.remove(a)}const a={60:"&lt;",62:"&gt;",38:"&amp;",34:"&quot;",39:"&apos;"};function validateFontName(e,t=!1){const n=/^("|').*("|')$/.exec(e);if(n&&n[1]===n[2]){if(new RegExp(`[^\\\\]${n[1]}`).test(e.slice(1,-1))){t&&(0,i.warn)(`FontFamily contains unescaped ${n[1]}: ${e}.`);return!1}}else for(const n of e.split(/[ \t]+/))if(/^(\d|(-(\d|-)))/.test(n)||!/^[\w-\\]+$/.test(n)){t&&(0,i.warn)(`FontFamily contains invalid <custom-ident>: ${e}.`);return!1}return!0}},(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.RefSetCache=t.RefSet=t.Ref=t.Name=t.EOF=t.Dict=t.Cmd=t.CIRCULAR_REF=void 0;t.clearPrimitiveCaches=function clearPrimitiveCaches(){o=Object.create(null);a=Object.create(null);c=Object.create(null)};t.isCmd=function isCmd(e,t){return e instanceof Cmd&&(void 0===t||e.cmd===t)};t.isDict=function isDict(e,t){return e instanceof Dict&&(void 0===t||isName(e.get("Type"),t))};t.isName=isName;t.isRefsEqual=function isRefsEqual(e,t){return e.num===t.num&&e.gen===t.gen};var i=n(1);const r=Symbol("CIRCULAR_REF");t.CIRCULAR_REF=r;const s=Symbol("EOF");t.EOF=s;let o=Object.create(null),a=Object.create(null),c=Object.create(null);class Name{constructor(e){this.name=e}static get(e){return a[e]||=new Name(e)}}t.Name=Name;class Cmd{constructor(e){this.cmd=e}static get(e){return o[e]||=new Cmd(e)}}t.Cmd=Cmd;const l=function nonSerializableClosure(){return l};class Dict{constructor(e=null){this._map=Object.create(null);this.xref=e;this.objId=null;this.suppressEncryption=!1;this.__nonSerializable__=l}assignXref(e){this.xref=e}get size(){return Object.keys(this._map).length}get(e,t,n){let i=this._map[e];if(void 0===i&&void 0!==t){i=this._map[t];void 0===i&&void 0!==n&&(i=this._map[n])}return i instanceof Ref&&this.xref?this.xref.fetch(i,this.suppressEncryption):i}async getAsync(e,t,n){let i=this._map[e];if(void 0===i&&void 0!==t){i=this._map[t];void 0===i&&void 0!==n&&(i=this._map[n])}return i instanceof Ref&&this.xref?this.xref.fetchAsync(i,this.suppressEncryption):i}getArray(e,t,n){let i=this._map[e];if(void 0===i&&void 0!==t){i=this._map[t];void 0===i&&void 0!==n&&(i=this._map[n])}i instanceof Ref&&this.xref&&(i=this.xref.fetch(i,this.suppressEncryption));if(Array.isArray(i)){i=i.slice();for(let e=0,t=i.length;e<t;e++)i[e]instanceof Ref&&this.xref&&(i[e]=this.xref.fetch(i[e],this.suppressEncryption))}return i}getRaw(e){return this._map[e]}getKeys(){return Object.keys(this._map)}getRawValues(){return Object.values(this._map)}set(e,t){this._map[e]=t}has(e){return void 0!==this._map[e]}forEach(e){for(const t in this._map)e(t,this.get(t))}static get empty(){const e=new Dict(null);e.set=(e,t)=>{(0,i.unreachable)("Should not call `set` on the empty dictionary.")};return(0,i.shadow)(this,"empty",e)}static merge({xref:e,dictArray:t,mergeSubDicts:n=!1}){const i=new Dict(e),r=new Map;for(const e of t)if(e instanceof Dict)for(const[t,i]of Object.entries(e._map)){let e=r.get(t);if(void 0===e){e=[];r.set(t,e)}else if(!(n&&i instanceof Dict))continue;e.push(i)}for(const[t,n]of r){if(1===n.length||!(n[0]instanceof Dict)){i._map[t]=n[0];continue}const r=new Dict(e);for(const e of n)for(const[t,n]of Object.entries(e._map))void 0===r._map[t]&&(r._map[t]=n);r.size>0&&(i._map[t]=r)}r.clear();return i.size>0?i:Dict.empty}clone(){const e=new Dict(this.xref);for(const t of this.getKeys())e.set(t,this.getRaw(t));return e}}t.Dict=Dict;class Ref{constructor(e,t){this.num=e;this.gen=t}toString(){return 0===this.gen?`${this.num}R`:`${this.num}R${this.gen}`}static fromString(e){const t=c[e];if(t)return t;const n=/^(\d+)R(\d*)$/.exec(e);return n&&"0"!==n[1]?c[e]=new Ref(parseInt(n[1]),n[2]?parseInt(n[2]):0):null}static get(e,t){const n=0===t?`${e}R`:`${e}R${t}`;return c[n]||=new Ref(e,t)}}t.Ref=Ref;class RefSet{constructor(e=null){this._set=new Set(e?._set)}has(e){return this._set.has(e.toString())}put(e){this._set.add(e.toString())}remove(e){this._set.delete(e.toString())}[Symbol.iterator](){return this._set.values()}clear(){this._set.clear()}}t.RefSet=RefSet;class RefSetCache{constructor(){this._map=new Map}get size(){return this._map.size}get(e){return this._map.get(e.toString())}has(e){return this._map.has(e.toString())}put(e,t){this._map.set(e.toString(),t)}putAlias(e,t){this._map.set(e.toString(),this.get(t))}[Symbol.iterator](){return this._map.values()}clear(){this._map.clear()}}t.RefSetCache=RefSetCache;function isName(e,t){return e instanceof Name&&(void 0===t||e.name===t)}},(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.BaseStream=void 0;var i=n(1);class BaseStream{constructor(){this.constructor===BaseStream&&(0,i.unreachable)("Cannot initialize BaseStream.")}get length(){(0,i.unreachable)("Abstract getter `length` accessed")}get isEmpty(){(0,i.unreachable)("Abstract getter `isEmpty` accessed")}get isDataLoaded(){return(0,i.shadow)(this,"isDataLoaded",!0)}getByte(){(0,i.unreachable)("Abstract method `getByte` called")}getBytes(e){(0,i.unreachable)("Abstract method `getBytes` called")}peekByte(){const e=this.getByte();-1!==e&&this.pos--;return e}peekBytes(e){const t=this.getBytes(e);this.pos-=t.length;return t}getUint16(){const e=this.getByte(),t=this.getByte();return-1===e||-1===t?-1:(e<<8)+t}getInt32(){return(this.getByte()<<24)+(this.getByte()<<16)+(this.getByte()<<8)+this.getByte()}getByteRange(e,t){(0,i.unreachable)("Abstract method `getByteRange` called")}getString(e){return(0,i.bytesToString)(this.getBytes(e))}skip(e){this.pos+=e||1}reset(){(0,i.unreachable)("Abstract method `reset` called")}moveStart(){(0,i.unreachable)("Abstract method `moveStart` called")}makeSubStream(e,t,n=null){(0,i.unreachable)("Abstract method `makeSubStream` called")}getBaseStreams(){return null}}t.BaseStream=BaseStream},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});t.ArithmeticDecoder=void 0;const n=[{qe:22017,nmps:1,nlps:1,switchFlag:1},{qe:13313,nmps:2,nlps:6,switchFlag:0},{qe:6145,nmps:3,nlps:9,switchFlag:0},{qe:2753,nmps:4,nlps:12,switchFlag:0},{qe:1313,nmps:5,nlps:29,switchFlag:0},{qe:545,nmps:38,nlps:33,switchFlag:0},{qe:22017,nmps:7,nlps:6,switchFlag:1},{qe:21505,nmps:8,nlps:14,switchFlag:0},{qe:18433,nmps:9,nlps:14,switchFlag:0},{qe:14337,nmps:10,nlps:14,switchFlag:0},{qe:12289,nmps:11,nlps:17,switchFlag:0},{qe:9217,nmps:12,nlps:18,switchFlag:0},{qe:7169,nmps:13,nlps:20,switchFlag:0},{qe:5633,nmps:29,nlps:21,switchFlag:0},{qe:22017,nmps:15,nlps:14,switchFlag:1},{qe:21505,nmps:16,nlps:14,switchFlag:0},{qe:20737,nmps:17,nlps:15,switchFlag:0},{qe:18433,nmps:18,nlps:16,switchFlag:0},{qe:14337,nmps:19,nlps:17,switchFlag:0},{qe:13313,nmps:20,nlps:18,switchFlag:0},{qe:12289,nmps:21,nlps:19,switchFlag:0},{qe:10241,nmps:22,nlps:19,switchFlag:0},{qe:9217,nmps:23,nlps:20,switchFlag:0},{qe:8705,nmps:24,nlps:21,switchFlag:0},{qe:7169,nmps:25,nlps:22,switchFlag:0},{qe:6145,nmps:26,nlps:23,switchFlag:0},{qe:5633,nmps:27,nlps:24,switchFlag:0},{qe:5121,nmps:28,nlps:25,switchFlag:0},{qe:4609,nmps:29,nlps:26,switchFlag:0},{qe:4353,nmps:30,nlps:27,switchFlag:0},{qe:2753,nmps:31,nlps:28,switchFlag:0},{qe:2497,nmps:32,nlps:29,switchFlag:0},{qe:2209,nmps:33,nlps:30,switchFlag:0},{qe:1313,nmps:34,nlps:31,switchFlag:0},{qe:1089,nmps:35,nlps:32,switchFlag:0},{qe:673,nmps:36,nlps:33,switchFlag:0},{qe:545,nmps:37,nlps:34,switchFlag:0},{qe:321,nmps:38,nlps:35,switchFlag:0},{qe:273,nmps:39,nlps:36,switchFlag:0},{qe:133,nmps:40,nlps:37,switchFlag:0},{qe:73,nmps:41,nlps:38,switchFlag:0},{qe:37,nmps:42,nlps:39,switchFlag:0},{qe:21,nmps:43,nlps:40,switchFlag:0},{qe:9,nmps:44,nlps:41,switchFlag:0},{qe:5,nmps:45,nlps:42,switchFlag:0},{qe:1,nmps:45,nlps:43,switchFlag:0},{qe:22017,nmps:46,nlps:46,switchFlag:0}];t.ArithmeticDecoder=class ArithmeticDecoder{constructor(e,t,n){this.data=e;this.bp=t;this.dataEnd=n;this.chigh=e[t];this.clow=0;this.byteIn();this.chigh=this.chigh<<7&65535|this.clow>>9&127;this.clow=this.clow<<7&65535;this.ct-=7;this.a=32768}byteIn(){const e=this.data;let t=this.bp;if(255===e[t])if(e[t+1]>143){this.clow+=65280;this.ct=8}else{t++;this.clow+=e[t]<<9;this.ct=7;this.bp=t}else{t++;this.clow+=t<this.dataEnd?e[t]<<8:65280;this.ct=8;this.bp=t}if(this.clow>65535){this.chigh+=this.clow>>16;this.clow&=65535}}readBit(e,t){let i=e[t]>>1,r=1&e[t];const s=n[i],o=s.qe;let a,c=this.a-o;if(this.chigh<o)if(c<o){c=o;a=r;i=s.nmps}else{c=o;a=1^r;1===s.switchFlag&&(r=a);i=s.nlps}else{this.chigh-=o;if(0!=(32768&c)){this.a=c;return r}if(c<o){a=1^r;1===s.switchFlag&&(r=a);i=s.nlps}else{a=r;i=s.nmps}}do{0===this.ct&&this.byteIn();c<<=1;this.chigh=this.chigh<<1&65535|this.clow>>15&1;this.clow=this.clow<<1&65535;this.ct--}while(0==(32768&c));this.a=c;e[t]=i<<1|r;return a}}},(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.CCITTFaxDecoder=void 0;var i=n(1);const r=-1,s=[[-1,-1],[-1,-1],[7,8],[7,7],[6,6],[6,6],[6,5],[6,5],[4,0],[4,0],[4,0],[4,0],[4,0],[4,0],[4,0],[4,0],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2]],o=[[-1,-1],[12,-2],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[11,1792],[11,1792],[12,1984],[12,2048],[12,2112],[12,2176],[12,2240],[12,2304],[11,1856],[11,1856],[11,1920],[11,1920],[12,2368],[12,2432],[12,2496],[12,2560]],a=[[-1,-1],[-1,-1],[-1,-1],[-1,-1],[8,29],[8,29],[8,30],[8,30],[8,45],[8,45],[8,46],[8,46],[7,22],[7,22],[7,22],[7,22],[7,23],[7,23],[7,23],[7,23],[8,47],[8,47],[8,48],[8,48],[6,13],[6,13],[6,13],[6,13],[6,13],[6,13],[6,13],[6,13],[7,20],[7,20],[7,20],[7,20],[8,33],[8,33],[8,34],[8,34],[8,35],[8,35],[8,36],[8,36],[8,37],[8,37],[8,38],[8,38],[7,19],[7,19],[7,19],[7,19],[8,31],[8,31],[8,32],[8,32],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,12],[6,12],[6,12],[6,12],[6,12],[6,12],[6,12],[6,12],[8,53],[8,53],[8,54],[8,54],[7,26],[7,26],[7,26],[7,26],[8,39],[8,39],[8,40],[8,40],[8,41],[8,41],[8,42],[8,42],[8,43],[8,43],[8,44],[8,44],[7,21],[7,21],[7,21],[7,21],[7,28],[7,28],[7,28],[7,28],[8,61],[8,61],[8,62],[8,62],[8,63],[8,63],[8,0],[8,0],[8,320],[8,320],[8,384],[8,384],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[7,27],[7,27],[7,27],[7,27],[8,59],[8,59],[8,60],[8,60],[9,1472],[9,1536],[9,1600],[9,1728],[7,18],[7,18],[7,18],[7,18],[7,24],[7,24],[7,24],[7,24],[8,49],[8,49],[8,50],[8,50],[8,51],[8,51],[8,52],[8,52],[7,25],[7,25],[7,25],[7,25],[8,55],[8,55],[8,56],[8,56],[8,57],[8,57],[8,58],[8,58],[6,192],[6,192],[6,192],[6,192],[6,192],[6,192],[6,192],[6,192],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[8,448],[8,448],[8,512],[8,512],[9,704],[9,768],[8,640],[8,640],[8,576],[8,576],[9,832],[9,896],[9,960],[9,1024],[9,1088],[9,1152],[9,1216],[9,1280],[9,1344],[9,1408],[7,256],[7,256],[7,256],[7,256],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[6,16],[6,16],[6,16],[6,16],[6,16],[6,16],[6,16],[6,16],[6,17],[6,17],[6,17],[6,17],[6,17],[6,17],[6,17],[6,17],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[6,14],[6,14],[6,14],[6,14],[6,14],[6,14],[6,14],[6,14],[6,15],[6,15],[6,15],[6,15],[6,15],[6,15],[6,15],[6,15],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7]],c=[[-1,-1],[-1,-1],[12,-2],[12,-2],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[11,1792],[11,1792],[11,1792],[11,1792],[12,1984],[12,1984],[12,2048],[12,2048],[12,2112],[12,2112],[12,2176],[12,2176],[12,2240],[12,2240],[12,2304],[12,2304],[11,1856],[11,1856],[11,1856],[11,1856],[11,1920],[11,1920],[11,1920],[11,1920],[12,2368],[12,2368],[12,2432],[12,2432],[12,2496],[12,2496],[12,2560],[12,2560],[10,18],[10,18],[10,18],[10,18],[10,18],[10,18],[10,18],[10,18],[12,52],[12,52],[13,640],[13,704],[13,768],[13,832],[12,55],[12,55],[12,56],[12,56],[13,1280],[13,1344],[13,1408],[13,1472],[12,59],[12,59],[12,60],[12,60],[13,1536],[13,1600],[11,24],[11,24],[11,24],[11,24],[11,25],[11,25],[11,25],[11,25],[13,1664],[13,1728],[12,320],[12,320],[12,384],[12,384],[12,448],[12,448],[13,512],[13,576],[12,53],[12,53],[12,54],[12,54],[13,896],[13,960],[13,1024],[13,1088],[13,1152],[13,1216],[10,64],[10,64],[10,64],[10,64],[10,64],[10,64],[10,64],[10,64]],l=[[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[11,23],[11,23],[12,50],[12,51],[12,44],[12,45],[12,46],[12,47],[12,57],[12,58],[12,61],[12,256],[10,16],[10,16],[10,16],[10,16],[10,17],[10,17],[10,17],[10,17],[12,48],[12,49],[12,62],[12,63],[12,30],[12,31],[12,32],[12,33],[12,40],[12,41],[11,22],[11,22],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[9,15],[9,15],[9,15],[9,15],[9,15],[9,15],[9,15],[9,15],[12,128],[12,192],[12,26],[12,27],[12,28],[12,29],[11,19],[11,19],[11,20],[11,20],[12,34],[12,35],[12,36],[12,37],[12,38],[12,39],[11,21],[11,21],[12,42],[12,43],[10,0],[10,0],[10,0],[10,0],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12]],f=[[-1,-1],[-1,-1],[-1,-1],[-1,-1],[6,9],[6,8],[5,7],[5,7],[4,6],[4,6],[4,6],[4,6],[4,5],[4,5],[4,5],[4,5],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2]];t.CCITTFaxDecoder=class CCITTFaxDecoder{constructor(e,t={}){if(!e||"function"!=typeof e.next)throw new Error('CCITTFaxDecoder - invalid "source" parameter.');this.source=e;this.eof=!1;this.encoding=t.K||0;this.eoline=t.EndOfLine||!1;this.byteAlign=t.EncodedByteAlign||!1;this.columns=t.Columns||1728;this.rows=t.Rows||0;this.eoblock=t.EndOfBlock??!0;this.black=t.BlackIs1||!1;this.codingLine=new Uint32Array(this.columns+1);this.refLine=new Uint32Array(this.columns+2);this.codingLine[0]=this.columns;this.codingPos=0;this.row=0;this.nextLine2D=this.encoding<0;this.inputBits=0;this.inputBuf=0;this.outputBits=0;this.rowsDone=!1;let n;for(;0===(n=this._lookBits(12));)this._eatBits(1);1===n&&this._eatBits(12);if(this.encoding>0){this.nextLine2D=!this._lookBits(1);this._eatBits(1)}}readNextChar(){if(this.eof)return-1;const e=this.refLine,t=this.codingLine,n=this.columns;let s,o,a,c,l;if(0===this.outputBits){this.rowsDone&&(this.eof=!0);if(this.eof)return-1;this.err=!1;let a,l,f;if(this.nextLine2D){for(c=0;t[c]<n;++c)e[c]=t[c];e[c++]=n;e[c]=n;t[0]=0;this.codingPos=0;s=0;o=0;for(;t[this.codingPos]<n;){a=this._getTwoDimCode();switch(a){case 0:this._addPixels(e[s+1],o);e[s+1]<n&&(s+=2);break;case 1:a=l=0;if(o){do{a+=f=this._getBlackCode()}while(f>=64);do{l+=f=this._getWhiteCode()}while(f>=64)}else{do{a+=f=this._getWhiteCode()}while(f>=64);do{l+=f=this._getBlackCode()}while(f>=64)}this._addPixels(t[this.codingPos]+a,o);t[this.codingPos]<n&&this._addPixels(t[this.codingPos]+l,1^o);for(;e[s]<=t[this.codingPos]&&e[s]<n;)s+=2;break;case 7:this._addPixels(e[s]+3,o);o^=1;if(t[this.codingPos]<n){++s;for(;e[s]<=t[this.codingPos]&&e[s]<n;)s+=2}break;case 5:this._addPixels(e[s]+2,o);o^=1;if(t[this.codingPos]<n){++s;for(;e[s]<=t[this.codingPos]&&e[s]<n;)s+=2}break;case 3:this._addPixels(e[s]+1,o);o^=1;if(t[this.codingPos]<n){++s;for(;e[s]<=t[this.codingPos]&&e[s]<n;)s+=2}break;case 2:this._addPixels(e[s],o);o^=1;if(t[this.codingPos]<n){++s;for(;e[s]<=t[this.codingPos]&&e[s]<n;)s+=2}break;case 8:this._addPixelsNeg(e[s]-3,o);o^=1;if(t[this.codingPos]<n){s>0?--s:++s;for(;e[s]<=t[this.codingPos]&&e[s]<n;)s+=2}break;case 6:this._addPixelsNeg(e[s]-2,o);o^=1;if(t[this.codingPos]<n){s>0?--s:++s;for(;e[s]<=t[this.codingPos]&&e[s]<n;)s+=2}break;case 4:this._addPixelsNeg(e[s]-1,o);o^=1;if(t[this.codingPos]<n){s>0?--s:++s;for(;e[s]<=t[this.codingPos]&&e[s]<n;)s+=2}break;case r:this._addPixels(n,0);this.eof=!0;break;default:(0,i.info)("bad 2d code");this._addPixels(n,0);this.err=!0}}}else{t[0]=0;this.codingPos=0;o=0;for(;t[this.codingPos]<n;){a=0;if(o)do{a+=f=this._getBlackCode()}while(f>=64);else do{a+=f=this._getWhiteCode()}while(f>=64);this._addPixels(t[this.codingPos]+a,o);o^=1}}let d=!1;this.byteAlign&&(this.inputBits&=-8);if(this.eoblock||this.row!==this.rows-1){a=this._lookBits(12);if(this.eoline)for(;a!==r&&1!==a;){this._eatBits(1);a=this._lookBits(12)}else for(;0===a;){this._eatBits(1);a=this._lookBits(12)}if(1===a){this._eatBits(12);d=!0}else a===r&&(this.eof=!0)}else this.rowsDone=!0;if(!this.eof&&this.encoding>0&&!this.rowsDone){this.nextLine2D=!this._lookBits(1);this._eatBits(1)}if(this.eoblock&&d&&this.byteAlign){a=this._lookBits(12);if(1===a){this._eatBits(12);if(this.encoding>0){this._lookBits(1);this._eatBits(1)}if(this.encoding>=0)for(c=0;c<4;++c){a=this._lookBits(12);1!==a&&(0,i.info)("bad rtc code: "+a);this._eatBits(12);if(this.encoding>0){this._lookBits(1);this._eatBits(1)}}this.eof=!0}}else if(this.err&&this.eoline){for(;;){a=this._lookBits(13);if(a===r){this.eof=!0;return-1}if(a>>1==1)break;this._eatBits(1)}this._eatBits(12);if(this.encoding>0){this._eatBits(1);this.nextLine2D=!(1&a)}}this.outputBits=t[0]>0?t[this.codingPos=0]:t[this.codingPos=1];this.row++}if(this.outputBits>=8){l=1&this.codingPos?0:255;this.outputBits-=8;if(0===this.outputBits&&t[this.codingPos]<n){this.codingPos++;this.outputBits=t[this.codingPos]-t[this.codingPos-1]}}else{a=8;l=0;do{if("number"!=typeof this.outputBits)throw new i.FormatError('Invalid /CCITTFaxDecode data, "outputBits" must be a number.');if(this.outputBits>a){l<<=a;1&this.codingPos||(l|=255>>8-a);this.outputBits-=a;a=0}else{l<<=this.outputBits;1&this.codingPos||(l|=255>>8-this.outputBits);a-=this.outputBits;this.outputBits=0;if(t[this.codingPos]<n){this.codingPos++;this.outputBits=t[this.codingPos]-t[this.codingPos-1]}else if(a>0){l<<=a;a=0}}}while(a)}this.black&&(l^=255);return l}_addPixels(e,t){const n=this.codingLine;let r=this.codingPos;if(e>n[r]){if(e>this.columns){(0,i.info)("row is wrong length");this.err=!0;e=this.columns}1&r^t&&++r;n[r]=e}this.codingPos=r}_addPixelsNeg(e,t){const n=this.codingLine;let r=this.codingPos;if(e>n[r]){if(e>this.columns){(0,i.info)("row is wrong length");this.err=!0;e=this.columns}1&r^t&&++r;n[r]=e}else if(e<n[r]){if(e<0){(0,i.info)("invalid code");this.err=!0;e=0}for(;r>0&&e<n[r-1];)--r;n[r]=e}this.codingPos=r}_findTableCode(e,t,n,i){const s=i||0;for(let i=e;i<=t;++i){let e=this._lookBits(i);if(e===r)return[!0,1,!1];i<t&&(e<<=t-i);if(!s||e>=s){const t=n[e-s];if(t[0]===i){this._eatBits(i);return[!0,t[1],!0]}}}return[!1,0,!1]}_getTwoDimCode(){let e,t=0;if(this.eoblock){t=this._lookBits(7);e=s[t];if(e?.[0]>0){this._eatBits(e[0]);return e[1]}}else{const e=this._findTableCode(1,7,s);if(e[0]&&e[2])return e[1]}(0,i.info)("Bad two dim code");return r}_getWhiteCode(){let e,t=0;if(this.eoblock){t=this._lookBits(12);if(t===r)return 1;e=t>>5==0?o[t]:a[t>>3];if(e[0]>0){this._eatBits(e[0]);return e[1]}}else{let e=this._findTableCode(1,9,a);if(e[0])return e[1];e=this._findTableCode(11,12,o);if(e[0])return e[1]}(0,i.info)("bad white code");this._eatBits(1);return 1}_getBlackCode(){let e,t;if(this.eoblock){e=this._lookBits(13);if(e===r)return 1;t=e>>7==0?c[e]:e>>9==0&&e>>7!=0?l[(e>>1)-64]:f[e>>7];if(t[0]>0){this._eatBits(t[0]);return t[1]}}else{let e=this._findTableCode(2,6,f);if(e[0])return e[1];e=this._findTableCode(7,12,l,64);if(e[0])return e[1];e=this._findTableCode(10,13,c);if(e[0])return e[1]}(0,i.info)("bad black code");this._eatBits(1);return 1}_lookBits(e){let t;for(;this.inputBits<e;){if(-1===(t=this.source.next()))return 0===this.inputBits?r:this.inputBuf<<e-this.inputBits&65535>>16-e;this.inputBuf=this.inputBuf<<8|t;this.inputBits+=8}return this.inputBuf>>this.inputBits-e&65535>>16-e}_eatBits(e){(this.inputBits-=e)<0&&(this.inputBits=0)}}},(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.JpegImage=void 0;var i=n(1),r=n(9),s=n(3);class JpegError extends i.BaseException{constructor(e){super(`JPEG error: ${e}`,"JpegError")}}class DNLMarkerError extends i.BaseException{constructor(e,t){super(e,"DNLMarkerError");this.scanLines=t}}class EOIMarkerError extends i.BaseException{constructor(e){super(e,"EOIMarkerError")}}const o=new Uint8Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]),a=4017,c=799,l=3406,f=2276,d=1567,h=3784,u=5793,p=2896;function buildHuffmanTable(e,t){let n,i,r=0,s=16;for(;s>0&&!e[s-1];)s--;const o=[{children:[],index:0}];let a,c=o[0];for(n=0;n<s;n++){for(i=0;i<e[n];i++){c=o.pop();c.children[c.index]=t[r];for(;c.index>0;)c=o.pop();c.index++;o.push(c);for(;o.length<=n;){o.push(a={children:[],index:0});c.children[c.index]=a.children;c=a}r++}if(n+1<s){o.push(a={children:[],index:0});c.children[c.index]=a.children;c=a}}return o[0].children}function getBlockBufferOffset(e,t,n){return 64*((e.blocksPerLine+1)*t+n)}function decodeScan(e,t,n,r,a,c,l,f,d,h=!1){const u=n.mcusPerLine,p=n.progressive,g=t;let m=0,b=0;function readBit(){if(b>0){b--;return m>>b&1}m=e[t++];if(255===m){const i=e[t++];if(i){if(220===i&&h){t+=2;const i=(0,s.readUint16)(e,t);t+=2;if(i>0&&i!==n.scanLines)throw new DNLMarkerError("Found DNL marker (0xFFDC) while parsing scan data",i)}else if(217===i){if(h){const e=T*(8===n.precision?8:0);if(e>0&&Math.round(n.scanLines/e)>=5)throw new DNLMarkerError("Found EOI marker (0xFFD9) while parsing scan data, possibly caused by incorrect `scanLines` parameter",e)}throw new EOIMarkerError("Found EOI marker (0xFFD9) while parsing scan data")}throw new JpegError(`unexpected marker ${(m<<8|i).toString(16)}`)}}b=7;return m>>>7}function decodeHuffman(e){let t=e;for(;;){t=t[readBit()];switch(typeof t){case"number":return t;case"object":continue}throw new JpegError("invalid huffman sequence")}}function receive(e){let t=0;for(;e>0;){t=t<<1|readBit();e--}return t}function receiveAndExtend(e){if(1===e)return 1===readBit()?1:-1;const t=receive(e);return t>=1<<e-1?t:t+(-1<<e)+1}let x=0;let y,w=0;let T=0;function decodeMcu(e,t,n,i,r){const s=n%u;T=(n/u|0)*e.v+i;const o=s*e.h+r;t(e,getBlockBufferOffset(e,T,o))}function decodeBlock(e,t,n){T=n/e.blocksPerLine|0;const i=n%e.blocksPerLine;t(e,getBlockBufferOffset(e,T,i))}const C=r.length;let S,I,E,P,k,B;B=p?0===c?0===f?function decodeDCFirst(e,t){const n=decodeHuffman(e.huffmanTableDC),i=0===n?0:receiveAndExtend(n)<<d;e.blockData[t]=e.pred+=i}:function decodeDCSuccessive(e,t){e.blockData[t]|=readBit()<<d}:0===f?function decodeACFirst(e,t){if(x>0){x--;return}let n=c;const i=l;for(;n<=i;){const i=decodeHuffman(e.huffmanTableAC),r=15&i,s=i>>4;if(0===r){if(s<15){x=receive(s)+(1<<s)-1;break}n+=16;continue}n+=s;const a=o[n];e.blockData[t+a]=receiveAndExtend(r)*(1<<d);n++}}:function decodeACSuccessive(e,t){let n=c;const i=l;let r,s,a=0;for(;n<=i;){const i=t+o[n],c=e.blockData[i]<0?-1:1;switch(w){case 0:s=decodeHuffman(e.huffmanTableAC);r=15&s;a=s>>4;if(0===r)if(a<15){x=receive(a)+(1<<a);w=4}else{a=16;w=1}else{if(1!==r)throw new JpegError("invalid ACn encoding");y=receiveAndExtend(r);w=a?2:3}continue;case 1:case 2:if(e.blockData[i])e.blockData[i]+=c*(readBit()<<d);else{a--;0===a&&(w=2===w?3:0)}break;case 3:if(e.blockData[i])e.blockData[i]+=c*(readBit()<<d);else{e.blockData[i]=y<<d;w=0}break;case 4:e.blockData[i]&&(e.blockData[i]+=c*(readBit()<<d))}n++}if(4===w){x--;0===x&&(w=0)}}:function decodeBaseline(e,t){const n=decodeHuffman(e.huffmanTableDC),i=0===n?0:receiveAndExtend(n);e.blockData[t]=e.pred+=i;let r=1;for(;r<64;){const n=decodeHuffman(e.huffmanTableAC),i=15&n,s=n>>4;if(0===i){if(s<15)break;r+=16;continue}r+=s;const a=o[r];e.blockData[t+a]=receiveAndExtend(i);r++}};let A,_=0;const R=1===C?r[0].blocksPerLine*r[0].blocksPerColumn:u*n.mcusPerColumn;let M,v;for(;_<=R;){const n=a?Math.min(R-_,a):R;if(n>0){for(I=0;I<C;I++)r[I].pred=0;x=0;if(1===C){S=r[0];for(k=0;k<n;k++){decodeBlock(S,B,_);_++}}else for(k=0;k<n;k++){for(I=0;I<C;I++){S=r[I];M=S.h;v=S.v;for(E=0;E<v;E++)for(P=0;P<M;P++)decodeMcu(S,B,_,E,P)}_++}}b=0;A=findNextFileMarker(e,t);if(!A)break;if(A.invalid){const e=n>0?"unexpected":"excessive";(0,i.warn)(`decodeScan - ${e} MCU data, current marker is: ${A.invalid}`);t=A.offset}if(!(A.marker>=65488&&A.marker<=65495))break;t+=2}return t-g}function quantizeAndInverse(e,t,n){const i=e.quantizationTable,r=e.blockData;let s,o,g,m,b,x,y,w,T,C,S,I,E,P,k,B,A;if(!i)throw new JpegError("missing required Quantization Table.");for(let e=0;e<64;e+=8){T=r[t+e];C=r[t+e+1];S=r[t+e+2];I=r[t+e+3];E=r[t+e+4];P=r[t+e+5];k=r[t+e+6];B=r[t+e+7];T*=i[e];if(0!=(C|S|I|E|P|k|B)){C*=i[e+1];S*=i[e+2];I*=i[e+3];E*=i[e+4];P*=i[e+5];k*=i[e+6];B*=i[e+7];s=u*T+128>>8;o=u*E+128>>8;g=S;m=k;b=p*(C-B)+128>>8;w=p*(C+B)+128>>8;x=I<<4;y=P<<4;s=s+o+1>>1;o=s-o;A=g*h+m*d+128>>8;g=g*d-m*h+128>>8;m=A;b=b+y+1>>1;y=b-y;w=w+x+1>>1;x=w-x;s=s+m+1>>1;m=s-m;o=o+g+1>>1;g=o-g;A=b*f+w*l+2048>>12;b=b*l-w*f+2048>>12;w=A;A=x*c+y*a+2048>>12;x=x*a-y*c+2048>>12;y=A;n[e]=s+w;n[e+7]=s-w;n[e+1]=o+y;n[e+6]=o-y;n[e+2]=g+x;n[e+5]=g-x;n[e+3]=m+b;n[e+4]=m-b}else{A=u*T+512>>10;n[e]=A;n[e+1]=A;n[e+2]=A;n[e+3]=A;n[e+4]=A;n[e+5]=A;n[e+6]=A;n[e+7]=A}}for(let e=0;e<8;++e){T=n[e];C=n[e+8];S=n[e+16];I=n[e+24];E=n[e+32];P=n[e+40];k=n[e+48];B=n[e+56];if(0!=(C|S|I|E|P|k|B)){s=u*T+2048>>12;o=u*E+2048>>12;g=S;m=k;b=p*(C-B)+2048>>12;w=p*(C+B)+2048>>12;x=I;y=P;s=4112+(s+o+1>>1);o=s-o;A=g*h+m*d+2048>>12;g=g*d-m*h+2048>>12;m=A;b=b+y+1>>1;y=b-y;w=w+x+1>>1;x=w-x;s=s+m+1>>1;m=s-m;o=o+g+1>>1;g=o-g;A=b*f+w*l+2048>>12;b=b*l-w*f+2048>>12;w=A;A=x*c+y*a+2048>>12;x=x*a-y*c+2048>>12;y=A;T=s+w;B=s-w;C=o+y;k=o-y;S=g+x;P=g-x;I=m+b;E=m-b;T<16?T=0:T>=4080?T=255:T>>=4;C<16?C=0:C>=4080?C=255:C>>=4;S<16?S=0:S>=4080?S=255:S>>=4;I<16?I=0:I>=4080?I=255:I>>=4;E<16?E=0:E>=4080?E=255:E>>=4;P<16?P=0:P>=4080?P=255:P>>=4;k<16?k=0:k>=4080?k=255:k>>=4;B<16?B=0:B>=4080?B=255:B>>=4;r[t+e]=T;r[t+e+8]=C;r[t+e+16]=S;r[t+e+24]=I;r[t+e+32]=E;r[t+e+40]=P;r[t+e+48]=k;r[t+e+56]=B}else{A=u*T+8192>>14;A=A<-2040?0:A>=2024?255:A+2056>>4;r[t+e]=A;r[t+e+8]=A;r[t+e+16]=A;r[t+e+24]=A;r[t+e+32]=A;r[t+e+40]=A;r[t+e+48]=A;r[t+e+56]=A}}}function buildComponentData(e,t){const n=t.blocksPerLine,i=t.blocksPerColumn,r=new Int16Array(64);for(let e=0;e<i;e++)for(let i=0;i<n;i++){quantizeAndInverse(t,getBlockBufferOffset(t,e,i),r)}return t.blockData}function findNextFileMarker(e,t,n=t){const i=e.length-1;let r=n<t?n:t;if(t>=i)return null;const o=(0,s.readUint16)(e,t);if(o>=65472&&o<=65534)return{invalid:null,marker:o,offset:t};let a=(0,s.readUint16)(e,r);for(;!(a>=65472&&a<=65534);){if(++r>=i)return null;a=(0,s.readUint16)(e,r)}return{invalid:o.toString(16),marker:a,offset:r}}t.JpegImage=class JpegImage{constructor({decodeTransform:e=null,colorTransform:t=-1}={}){this._decodeTransform=e;this._colorTransform=t}parse(e,{dnlScanLines:t=null}={}){function readDataBlock(){const t=(0,s.readUint16)(e,a);a+=2;let n=a+t-2;const r=findNextFileMarker(e,n,a);if(r?.invalid){(0,i.warn)("readDataBlock - incorrect length, current marker is: "+r.invalid);n=r.offset}const o=e.subarray(a,n);a+=o.length;return o}function prepareComponents(e){const t=Math.ceil(e.samplesPerLine/8/e.maxH),n=Math.ceil(e.scanLines/8/e.maxV);for(const i of e.components){const r=Math.ceil(Math.ceil(e.samplesPerLine/8)*i.h/e.maxH),s=Math.ceil(Math.ceil(e.scanLines/8)*i.v/e.maxV),o=t*i.h,a=64*(n*i.v)*(o+1);i.blockData=new Int16Array(a);i.blocksPerLine=r;i.blocksPerColumn=s}e.mcusPerLine=t;e.mcusPerColumn=n}let n,r,a=0,c=null,l=null,f=0;const d=[],h=[],u=[];let p=(0,s.readUint16)(e,a);a+=2;if(65496!==p)throw new JpegError("SOI not found");p=(0,s.readUint16)(e,a);a+=2;e:for(;65497!==p;){let g,m,b;switch(p){case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:const x=readDataBlock();65504===p&&74===x[0]&&70===x[1]&&73===x[2]&&70===x[3]&&0===x[4]&&(c={version:{major:x[5],minor:x[6]},densityUnits:x[7],xDensity:x[8]<<8|x[9],yDensity:x[10]<<8|x[11],thumbWidth:x[12],thumbHeight:x[13],thumbData:x.subarray(14,14+3*x[12]*x[13])});65518===p&&65===x[0]&&100===x[1]&&111===x[2]&&98===x[3]&&101===x[4]&&(l={version:x[5]<<8|x[6],flags0:x[7]<<8|x[8],flags1:x[9]<<8|x[10],transformCode:x[11]});break;case 65499:const y=(0,s.readUint16)(e,a);a+=2;const w=y+a-2;let T;for(;a<w;){const t=e[a++],n=new Uint16Array(64);if(t>>4==0)for(m=0;m<64;m++){T=o[m];n[T]=e[a++]}else{if(t>>4!=1)throw new JpegError("DQT - invalid table spec");for(m=0;m<64;m++){T=o[m];n[T]=(0,s.readUint16)(e,a);a+=2}}d[15&t]=n}break;case 65472:case 65473:case 65474:if(n)throw new JpegError("Only single frame JPEGs supported");a+=2;n={};n.extended=65473===p;n.progressive=65474===p;n.precision=e[a++];const C=(0,s.readUint16)(e,a);a+=2;n.scanLines=t||C;n.samplesPerLine=(0,s.readUint16)(e,a);a+=2;n.components=[];n.componentIds={};const S=e[a++];let I=0,E=0;for(g=0;g<S;g++){const t=e[a],i=e[a+1]>>4,r=15&e[a+1];I<i&&(I=i);E<r&&(E=r);const s=e[a+2];b=n.components.push({h:i,v:r,quantizationId:s,quantizationTable:null});n.componentIds[t]=b-1;a+=3}n.maxH=I;n.maxV=E;prepareComponents(n);break;case 65476:const P=(0,s.readUint16)(e,a);a+=2;for(g=2;g<P;){const t=e[a++],n=new Uint8Array(16);let i=0;for(m=0;m<16;m++,a++)i+=n[m]=e[a];const r=new Uint8Array(i);for(m=0;m<i;m++,a++)r[m]=e[a];g+=17+i;(t>>4==0?u:h)[15&t]=buildHuffmanTable(n,r)}break;case 65501:a+=2;r=(0,s.readUint16)(e,a);a+=2;break;case 65498:const k=1==++f&&!t;a+=2;const B=e[a++],A=[];for(g=0;g<B;g++){const t=e[a++],i=n.componentIds[t],r=n.components[i];r.index=t;const s=e[a++];r.huffmanTableDC=u[s>>4];r.huffmanTableAC=h[15&s];A.push(r)}const _=e[a++],R=e[a++],M=e[a++];try{const t=decodeScan(e,a,n,A,r,_,R,M>>4,15&M,k);a+=t}catch(t){if(t instanceof DNLMarkerError){(0,i.warn)(`${t.message} -- attempting to re-parse the JPEG image.`);return this.parse(e,{dnlScanLines:t.scanLines})}if(t instanceof EOIMarkerError){(0,i.warn)(`${t.message} -- ignoring the rest of the image data.`);break e}throw t}break;case 65500:a+=4;break;case 65535:255!==e[a]&&a--;break;default:const v=findNextFileMarker(e,a-2,a-3);if(v?.invalid){(0,i.warn)("JpegImage.parse - unexpected data, current marker is: "+v.invalid);a=v.offset;break}if(!v||a>=e.length-1){(0,i.warn)("JpegImage.parse - reached the end of the image data without finding an EOI marker (0xFFD9).");break e}throw new JpegError("JpegImage.parse - unknown marker: "+p.toString(16))}p=(0,s.readUint16)(e,a);a+=2}this.width=n.samplesPerLine;this.height=n.scanLines;this.jfif=c;this.adobe=l;this.components=[];for(const e of n.components){const t=d[e.quantizationId];t&&(e.quantizationTable=t);this.components.push({index:e.index,output:buildComponentData(0,e),scaleX:e.h/n.maxH,scaleY:e.v/n.maxV,blocksPerLine:e.blocksPerLine,blocksPerColumn:e.blocksPerColumn})}this.numComponents=this.components.length}_getLinearizedBlockData(e,t,n=!1){const i=this.width/e,r=this.height/t;let s,o,a,c,l,f,d,h,u,p,g,m=0;const b=this.components.length,x=e*t*b,y=new Uint8ClampedArray(x),w=new Uint32Array(e),T=4294967288;let C;for(d=0;d<b;d++){s=this.components[d];o=s.scaleX*i;a=s.scaleY*r;m=d;g=s.output;c=s.blocksPerLine+1<<3;if(o!==C){for(l=0;l<e;l++){h=0|l*o;w[l]=(h&T)<<3|7&h}C=o}for(f=0;f<t;f++){h=0|f*a;p=c*(h&T)|(7&h)<<3;for(l=0;l<e;l++){y[m]=g[p+w[l]];m+=b}}}let S=this._decodeTransform;n||4!==b||S||(S=new Int32Array([-256,255,-256,255,-256,255,-256,255]));if(S)for(d=0;d<x;)for(h=0,u=0;h<b;h++,d++,u+=2)y[d]=(y[d]*S[u]>>8)+S[u+1];return y}get _isColorConversionNeeded(){return this.adobe?!!this.adobe.transformCode:3===this.numComponents?0!==this._colorTransform&&(82!==this.components[0].index||71!==this.components[1].index||66!==this.components[2].index):1===this._colorTransform}_convertYccToRgb(e){let t,n,i;for(let r=0,s=e.length;r<s;r+=3){t=e[r];n=e[r+1];i=e[r+2];e[r]=t-179.456+1.402*i;e[r+1]=t+135.459-.344*n-.714*i;e[r+2]=t-226.816+1.772*n}return e}_convertYccToRgba(e,t){for(let n=0,i=0,r=e.length;n<r;n+=3,i+=4){const r=e[n],s=e[n+1],o=e[n+2];t[i]=r-179.456+1.402*o;t[i+1]=r+135.459-.344*s-.714*o;t[i+2]=r-226.816+1.772*s;t[i+3]=255}return t}_convertYcckToRgb(e){let t,n,i,r,s=0;for(let o=0,a=e.length;o<a;o+=4){t=e[o];n=e[o+1];i=e[o+2];r=e[o+3];e[s++]=n*(-660635669420364e-19*n+.000437130475926232*i-54080610064599e-18*t+.00048449797120281*r-.154362151871126)-122.67195406894+i*(-.000957964378445773*i+.000817076911346625*t-.00477271405408747*r+1.53380253221734)+t*(.000961250184130688*t-.00266257332283933*r+.48357088451265)+r*(-.000336197177618394*r+.484791561490776);e[s++]=107.268039397724+n*(219927104525741e-19*n-.000640992018297945*i+.000659397001245577*t+.000426105652938837*r-.176491792462875)+i*(-.000778269941513683*i+.00130872261408275*t+.000770482631801132*r-.151051492775562)+t*(.00126935368114843*t-.00265090189010898*r+.25802910206845)+r*(-.000318913117588328*r-.213742400323665);e[s++]=n*(-.000570115196973677*n-263409051004589e-19*i+.0020741088115012*t-.00288260236853442*r+.814272968359295)-20.810012546947+i*(-153496057440975e-19*i-.000132689043961446*t+.000560833691242812*r-.195152027534049)+t*(.00174418132927582*t-.00255243321439347*r+.116935020465145)+r*(-.000343531996510555*r+.24165260232407)}return e.subarray(0,s)}_convertYcckToRgba(e){for(let t=0,n=e.length;t<n;t+=4){const n=e[t],i=e[t+1],r=e[t+2],s=e[t+3];e[t]=i*(-660635669420364e-19*i+.000437130475926232*r-54080610064599e-18*n+.00048449797120281*s-.154362151871126)-122.67195406894+r*(-.000957964378445773*r+.000817076911346625*n-.00477271405408747*s+1.53380253221734)+n*(.000961250184130688*n-.00266257332283933*s+.48357088451265)+s*(-.000336197177618394*s+.484791561490776);e[t+1]=107.268039397724+i*(219927104525741e-19*i-.000640992018297945*r+.000659397001245577*n+.000426105652938837*s-.176491792462875)+r*(-.000778269941513683*r+.00130872261408275*n+.000770482631801132*s-.151051492775562)+n*(.00126935368114843*n-.00265090189010898*s+.25802910206845)+s*(-.000318913117588328*s-.213742400323665);e[t+2]=i*(-.000570115196973677*i-263409051004589e-19*r+.0020741088115012*n-.00288260236853442*s+.814272968359295)-20.810012546947+r*(-153496057440975e-19*r-.000132689043961446*n+.000560833691242812*s-.195152027534049)+n*(.00174418132927582*n-.00255243321439347*s+.116935020465145)+s*(-.000343531996510555*s+.24165260232407);e[t+3]=255}return e}_convertYcckToCmyk(e){let t,n,i;for(let r=0,s=e.length;r<s;r+=4){t=e[r];n=e[r+1];i=e[r+2];e[r]=434.456-t-1.402*i;e[r+1]=119.541-t+.344*n+.714*i;e[r+2]=481.816-t-1.772*n}return e}_convertCmykToRgb(e){let t,n,i,r,s=0;for(let o=0,a=e.length;o<a;o+=4){t=e[o];n=e[o+1];i=e[o+2];r=e[o+3];e[s++]=255+t*(-6747147073602441e-20*t+.0008379262121013727*n+.0002894718188643294*i+.003264231057537806*r-1.1185611867203937)+n*(26374107616089405e-21*n-8626949158638572e-20*i-.0002748769067499491*r-.02155688794978967)+i*(-3878099212869363e-20*i-.0003267808279485286*r+.0686742238595345)-r*(.0003361971776183937*r+.7430659151342254);e[s++]=255+t*(.00013596372813588848*t+.000924537132573585*n+.00010567359618683593*i+.0004791864687436512*r-.3109689587515875)+n*(-.00023545346108370344*n+.0002702845253534714*i+.0020200308977307156*r-.7488052167015494)+i*(6834815998235662e-20*i+.00015168452363460973*r-.09751927774728933)-r*(.0003189131175883281*r+.7364883807733168);e[s++]=255+t*(13598650411385307e-21*t+.00012423956175490851*n+.0004751985097583589*i-36729317476630422e-22*r-.05562186980264034)+n*(.00016141380598724676*n+.0009692239130725186*i+.0007782692450036253*r-.44015232367526463)+i*(5.068882914068769e-7*i+.0017778369011375071*r-.7591454649749609)-r*(.0003435319965105553*r+.7063770186160144)}return e.subarray(0,s)}_convertCmykToRgba(e){for(let t=0,n=e.length;t<n;t+=4){const n=e[t],i=e[t+1],r=e[t+2],s=e[t+3];e[t]=255+n*(-6747147073602441e-20*n+.0008379262121013727*i+.0002894718188643294*r+.003264231057537806*s-1.1185611867203937)+i*(26374107616089405e-21*i-8626949158638572e-20*r-.0002748769067499491*s-.02155688794978967)+r*(-3878099212869363e-20*r-.0003267808279485286*s+.0686742238595345)-s*(.0003361971776183937*s+.7430659151342254);e[t+1]=255+n*(.00013596372813588848*n+.000924537132573585*i+.00010567359618683593*r+.0004791864687436512*s-.3109689587515875)+i*(-.00023545346108370344*i+.0002702845253534714*r+.0020200308977307156*s-.7488052167015494)+r*(6834815998235662e-20*r+.00015168452363460973*s-.09751927774728933)-s*(.0003189131175883281*s+.7364883807733168);e[t+2]=255+n*(13598650411385307e-21*n+.00012423956175490851*i+.0004751985097583589*r-36729317476630422e-22*s-.05562186980264034)+i*(.00016141380598724676*i+.0009692239130725186*r+.0007782692450036253*s-.44015232367526463)+r*(5.068882914068769e-7*r+.0017778369011375071*s-.7591454649749609)-s*(.0003435319965105553*s+.7063770186160144);e[t+3]=255}return e}getData({width:e,height:t,forceRGBA:n=!1,forceRGB:i=!1,isSourcePDF:s=!1}){if(this.numComponents>4)throw new JpegError("Unsupported color mode");const o=this._getLinearizedBlockData(e,t,s);if(1===this.numComponents&&(n||i)){const e=o.length*(n?4:3),t=new Uint8ClampedArray(e);let i=0;if(n)(0,r.grayToRGBA)(o,new Uint32Array(t.buffer));else for(const e of o){t[i++]=e;t[i++]=e;t[i++]=e}return t}if(3===this.numComponents&&this._isColorConversionNeeded){if(n){const e=new Uint8ClampedArray(o.length/3*4);return this._convertYccToRgba(o,e)}return this._convertYccToRgb(o)}if(4===this.numComponents){if(this._isColorConversionNeeded)return n?this._convertYcckToRgba(o):i?this._convertYcckToRgb(o):this._convertYcckToCmyk(o);if(n)return this._convertCmykToRgba(o);if(i)return this._convertCmykToRgb(o)}return o}}},(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.convertBlackAndWhiteToRGBA=convertBlackAndWhiteToRGBA;t.convertToRGBA=function convertToRGBA(e){switch(e.kind){case i.ImageKind.GRAYSCALE_1BPP:return convertBlackAndWhiteToRGBA(e);case i.ImageKind.RGB_24BPP:return function convertRGBToRGBA({src:e,srcPos:t=0,dest:n,destPos:r=0,width:s,height:o}){let a=0;const c=e.length>>2,l=new Uint32Array(e.buffer,t,c);if(i.FeatureTest.isLittleEndian){for(;a<c-2;a+=3,r+=4){const e=l[a],t=l[a+1],i=l[a+2];n[r]=4278190080|e;n[r+1]=e>>>24|t<<8|4278190080;n[r+2]=t>>>16|i<<16|4278190080;n[r+3]=i>>>8|4278190080}for(let t=4*a,i=e.length;t<i;t+=3)n[r++]=e[t]|e[t+1]<<8|e[t+2]<<16|4278190080}else{for(;a<c-2;a+=3,r+=4){const e=l[a],t=l[a+1],i=l[a+2];n[r]=255|e;n[r+1]=e<<24|t>>>8|255;n[r+2]=t<<16|i>>>16|255;n[r+3]=i<<8|255}for(let t=4*a,i=e.length;t<i;t+=3)n[r++]=e[t]<<24|e[t+1]<<16|e[t+2]<<8|255}return{srcPos:t,destPos:r}}(e)}return null};t.grayToRGBA=function grayToRGBA(e,t){if(i.FeatureTest.isLittleEndian)for(let n=0,i=e.length;n<i;n++)t[n]=65793*e[n]|4278190080;else for(let n=0,i=e.length;n<i;n++)t[n]=16843008*e[n]|255};var i=n(1);function convertBlackAndWhiteToRGBA({src:e,srcPos:t=0,dest:n,width:r,height:s,nonBlackColor:o=4294967295,inverseDecode:a=!1}){const c=i.FeatureTest.isLittleEndian?4278190080:255,[l,f]=a?[o,c]:[c,o],d=r>>3,h=7&r,u=e.length;n=new Uint32Array(n.buffer);let p=0;for(let i=0;i<s;i++){for(const i=t+d;t<i;t++){const i=t<u?e[t]:255;n[p++]=128&i?f:l;n[p++]=64&i?f:l;n[p++]=32&i?f:l;n[p++]=16&i?f:l;n[p++]=8&i?f:l;n[p++]=4&i?f:l;n[p++]=2&i?f:l;n[p++]=1&i?f:l}if(0===h)continue;const i=t<u?e[t++]:255;for(let e=0;e<h;e++)n[p++]=i&1<<7-e?f:l}return{srcPos:t,destPos:p}}},(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.JpxImage=void 0;var i=n(1),r=n(3),s=n(6);class JpxError extends i.BaseException{constructor(e){super(`JPX error: ${e}`,"JpxError")}}const o={LL:0,LH:1,HL:1,HH:2};t.JpxImage=class JpxImage{constructor(){this.failOnCorruptedImage=!1}parse(e){if(65359===(0,r.readUint16)(e,0)){this.parseCodestream(e,0,e.length);return}const t=e.length;let n=0;for(;n<t;){let s=8,o=(0,r.readUint32)(e,n);const a=(0,r.readUint32)(e,n+4);n+=s;if(1===o){o=4294967296*(0,r.readUint32)(e,n)+(0,r.readUint32)(e,n+4);n+=8;s+=8}0===o&&(o=t-n+s);if(o<s)throw new JpxError("Invalid box field size");const c=o-s;let l=!0;switch(a){case 1785737832:l=!1;break;case 1668246642:const t=e[n];if(1===t){const t=(0,r.readUint32)(e,n+3);switch(t){case 16:case 17:case 18:break;default:(0,i.warn)("Unknown colorspace "+t)}}else 2===t&&(0,i.info)("ICC profile not supported");break;case 1785737827:this.parseCodestream(e,n,n+c);break;case 1783636e3:218793738!==(0,r.readUint32)(e,n)&&(0,i.warn)("Invalid JP2 signature");break;case 1783634458:case 1718909296:case 1920099697:case 1919251232:case 1768449138:break;default:const s=String.fromCharCode(a>>24&255,a>>16&255,a>>8&255,255&a);(0,i.warn)(`Unsupported header type ${a} (${s}).`)}l&&(n+=c)}}parseImageProperties(e){let t=e.getByte();for(;t>=0;){const n=t;t=e.getByte();if(65361===(n<<8|t)){e.skip(4);const t=e.getInt32()>>>0,n=e.getInt32()>>>0,i=e.getInt32()>>>0,r=e.getInt32()>>>0;e.skip(16);const s=e.getUint16();this.width=t-i;this.height=n-r;this.componentsCount=s;this.bitsPerComponent=8;return}}throw new JpxError("No size marker found in JPX stream")}parseCodestream(e,t,n){const s={};let o=!1;try{let a=t;for(;a+1<n;){const t=(0,r.readUint16)(e,a);a+=2;let n,c,l,f,d,h,u=0;switch(t){case 65359:s.mainHeader=!0;break;case 65497:break;case 65361:u=(0,r.readUint16)(e,a);const p={};p.Xsiz=(0,r.readUint32)(e,a+4);p.Ysiz=(0,r.readUint32)(e,a+8);p.XOsiz=(0,r.readUint32)(e,a+12);p.YOsiz=(0,r.readUint32)(e,a+16);p.XTsiz=(0,r.readUint32)(e,a+20);p.YTsiz=(0,r.readUint32)(e,a+24);p.XTOsiz=(0,r.readUint32)(e,a+28);p.YTOsiz=(0,r.readUint32)(e,a+32);const g=(0,r.readUint16)(e,a+36);p.Csiz=g;const m=[];n=a+38;for(let t=0;t<g;t++){const t={precision:1+(127&e[n]),isSigned:!!(128&e[n]),XRsiz:e[n+1],YRsiz:e[n+2]};n+=3;calculateComponentDimensions(t,p);m.push(t)}s.SIZ=p;s.components=m;calculateTileGrids(s,m);s.QCC=[];s.COC=[];break;case 65372:u=(0,r.readUint16)(e,a);const b={};n=a+2;c=e[n++];switch(31&c){case 0:f=8;d=!0;break;case 1:f=16;d=!1;break;case 2:f=16;d=!0;break;default:throw new Error("Invalid SQcd value "+c)}b.noQuantization=8===f;b.scalarExpounded=d;b.guardBits=c>>5;l=[];for(;n<u+a;){const t={};if(8===f){t.epsilon=e[n++]>>3;t.mu=0}else{t.epsilon=e[n]>>3;t.mu=(7&e[n])<<8|e[n+1];n+=2}l.push(t)}b.SPqcds=l;if(s.mainHeader)s.QCD=b;else{s.currentTile.QCD=b;s.currentTile.QCC=[]}break;case 65373:u=(0,r.readUint16)(e,a);const x={};n=a+2;let y;if(s.SIZ.Csiz<257)y=e[n++];else{y=(0,r.readUint16)(e,n);n+=2}c=e[n++];switch(31&c){case 0:f=8;d=!0;break;case 1:f=16;d=!1;break;case 2:f=16;d=!0;break;default:throw new Error("Invalid SQcd value "+c)}x.noQuantization=8===f;x.scalarExpounded=d;x.guardBits=c>>5;l=[];for(;n<u+a;){const t={};if(8===f){t.epsilon=e[n++]>>3;t.mu=0}else{t.epsilon=e[n]>>3;t.mu=(7&e[n])<<8|e[n+1];n+=2}l.push(t)}x.SPqcds=l;s.mainHeader?s.QCC[y]=x:s.currentTile.QCC[y]=x;break;case 65362:u=(0,r.readUint16)(e,a);const w={};n=a+2;const T=e[n++];w.entropyCoderWithCustomPrecincts=!!(1&T);w.sopMarkerUsed=!!(2&T);w.ephMarkerUsed=!!(4&T);w.progressionOrder=e[n++];w.layersCount=(0,r.readUint16)(e,n);n+=2;w.multipleComponentTransform=e[n++];w.decompositionLevelsCount=e[n++];w.xcb=2+(15&e[n++]);w.ycb=2+(15&e[n++]);const C=e[n++];w.selectiveArithmeticCodingBypass=!!(1&C);w.resetContextProbabilities=!!(2&C);w.terminationOnEachCodingPass=!!(4&C);w.verticallyStripe=!!(8&C);w.predictableTermination=!!(16&C);w.segmentationSymbolUsed=!!(32&C);w.reversibleTransformation=e[n++];if(w.entropyCoderWithCustomPrecincts){const t=[];for(;n<u+a;){const i=e[n++];t.push({PPx:15&i,PPy:i>>4})}w.precinctsSizes=t}const S=[];w.selectiveArithmeticCodingBypass&&S.push("selectiveArithmeticCodingBypass");w.terminationOnEachCodingPass&&S.push("terminationOnEachCodingPass");w.verticallyStripe&&S.push("verticallyStripe");w.predictableTermination&&S.push("predictableTermination");if(S.length>0){o=!0;(0,i.warn)(`JPX: Unsupported COD options (${S.join(", ")}).`)}if(s.mainHeader)s.COD=w;else{s.currentTile.COD=w;s.currentTile.COC=[]}break;case 65424:u=(0,r.readUint16)(e,a);h={};h.index=(0,r.readUint16)(e,a+2);h.length=(0,r.readUint32)(e,a+4);h.dataEnd=h.length+a-2;h.partIndex=e[a+8];h.partsCount=e[a+9];s.mainHeader=!1;if(0===h.partIndex){h.COD=s.COD;h.COC=s.COC.slice(0);h.QCD=s.QCD;h.QCC=s.QCC.slice(0)}s.currentTile=h;break;case 65427:h=s.currentTile;if(0===h.partIndex){initializeTile(s,h.index);buildPackets(s)}u=h.dataEnd-a;parseTilePackets(s,e,a,u);break;case 65363:(0,i.warn)("JPX: Codestream code 0xFF53 (COC) is not implemented.");case 65365:case 65367:case 65368:case 65380:u=(0,r.readUint16)(e,a);break;default:throw new Error("Unknown codestream code: "+t.toString(16))}a+=u}}catch(e){if(o||this.failOnCorruptedImage)throw new JpxError(e.message);(0,i.warn)(`JPX: Trying to recover from: "${e.message}".`)}this.tiles=function transformComponents(e){const t=e.SIZ,n=e.components,i=t.Csiz,r=[];for(let t=0,s=e.tiles.length;t<s;t++){const s=e.tiles[t],o=[];for(let t=0;t<i;t++)o[t]=transformTile(e,s,t);const a=o[0],c=new Uint8ClampedArray(a.items.length*i),l={left:a.left,top:a.top,width:a.width,height:a.height,items:c};let f,d,h,u,p,g,m,b=0;if(s.codingStyleDefaultParameters.multipleComponentTransform){const e=4===i,t=o[0].items,r=o[1].items,a=o[2].items,l=e?o[3].items:null;f=n[0].precision-8;d=.5+(128<<f);const x=s.components[0],y=i-3;u=t.length;if(x.codingStyleParameters.reversibleTransformation)for(h=0;h<u;h++,b+=y){p=t[h]+d;g=r[h];m=a[h];const e=p-(m+g>>2);c[b++]=e+m>>f;c[b++]=e>>f;c[b++]=e+g>>f}else for(h=0;h<u;h++,b+=y){p=t[h]+d;g=r[h];m=a[h];c[b++]=p+1.402*m>>f;c[b++]=p-.34413*g-.71414*m>>f;c[b++]=p+1.772*g>>f}if(e)for(h=0,b=3;h<u;h++,b+=4)c[b]=l[h]+d>>f}else for(let e=0;e<i;e++){const t=o[e].items;f=n[e].precision-8;d=.5+(128<<f);for(b=e,h=0,u=t.length;h<u;h++){c[b]=t[h]+d>>f;b+=i}}r.push(l)}return r}(s);this.width=s.SIZ.Xsiz-s.SIZ.XOsiz;this.height=s.SIZ.Ysiz-s.SIZ.YOsiz;this.componentsCount=s.SIZ.Csiz}};function calculateComponentDimensions(e,t){e.x0=Math.ceil(t.XOsiz/e.XRsiz);e.x1=Math.ceil(t.Xsiz/e.XRsiz);e.y0=Math.ceil(t.YOsiz/e.YRsiz);e.y1=Math.ceil(t.Ysiz/e.YRsiz);e.width=e.x1-e.x0;e.height=e.y1-e.y0}function calculateTileGrids(e,t){const n=e.SIZ,i=[];let r;const s=Math.ceil((n.Xsiz-n.XTOsiz)/n.XTsiz),o=Math.ceil((n.Ysiz-n.YTOsiz)/n.YTsiz);for(let e=0;e<o;e++)for(let t=0;t<s;t++){r={};r.tx0=Math.max(n.XTOsiz+t*n.XTsiz,n.XOsiz);r.ty0=Math.max(n.YTOsiz+e*n.YTsiz,n.YOsiz);r.tx1=Math.min(n.XTOsiz+(t+1)*n.XTsiz,n.Xsiz);r.ty1=Math.min(n.YTOsiz+(e+1)*n.YTsiz,n.Ysiz);r.width=r.tx1-r.tx0;r.height=r.ty1-r.ty0;r.components=[];i.push(r)}e.tiles=i;for(let e=0,s=n.Csiz;e<s;e++){const n=t[e];for(let t=0,s=i.length;t<s;t++){const s={};r=i[t];s.tcx0=Math.ceil(r.tx0/n.XRsiz);s.tcy0=Math.ceil(r.ty0/n.YRsiz);s.tcx1=Math.ceil(r.tx1/n.XRsiz);s.tcy1=Math.ceil(r.ty1/n.YRsiz);s.width=s.tcx1-s.tcx0;s.height=s.tcy1-s.tcy0;r.components[e]=s}}}function getBlocksDimensions(e,t,n){const i=t.codingStyleParameters,r={};if(i.entropyCoderWithCustomPrecincts){r.PPx=i.precinctsSizes[n].PPx;r.PPy=i.precinctsSizes[n].PPy}else{r.PPx=15;r.PPy=15}r.xcb_=n>0?Math.min(i.xcb,r.PPx-1):Math.min(i.xcb,r.PPx);r.ycb_=n>0?Math.min(i.ycb,r.PPy-1):Math.min(i.ycb,r.PPy);return r}function buildPrecincts(e,t,n){const i=1<<n.PPx,r=1<<n.PPy,s=0===t.resLevel,o=1<<n.PPx+(s?0:-1),a=1<<n.PPy+(s?0:-1),c=t.trx1>t.trx0?Math.ceil(t.trx1/i)-Math.floor(t.trx0/i):0,l=t.try1>t.try0?Math.ceil(t.try1/r)-Math.floor(t.try0/r):0,f=c*l;t.precinctParameters={precinctWidth:i,precinctHeight:r,numprecinctswide:c,numprecinctshigh:l,numprecincts:f,precinctWidthInSubband:o,precinctHeightInSubband:a}}function buildCodeblocks(e,t,n){const i=n.xcb_,r=n.ycb_,s=1<<i,o=1<<r,a=t.tbx0>>i,c=t.tby0>>r,l=t.tbx1+s-1>>i,f=t.tby1+o-1>>r,d=t.resolution.precinctParameters,h=[],u=[];let p,g,m,b;for(g=c;g<f;g++)for(p=a;p<l;p++){m={cbx:p,cby:g,tbx0:s*p,tby0:o*g,tbx1:s*(p+1),tby1:o*(g+1)};m.tbx0_=Math.max(t.tbx0,m.tbx0);m.tby0_=Math.max(t.tby0,m.tby0);m.tbx1_=Math.min(t.tbx1,m.tbx1);m.tby1_=Math.min(t.tby1,m.tby1);b=Math.floor((m.tbx0_-t.tbx0)/d.precinctWidthInSubband)+Math.floor((m.tby0_-t.tby0)/d.precinctHeightInSubband)*d.numprecinctswide;m.precinctNumber=b;m.subbandType=t.type;m.Lblock=3;if(m.tbx1_<=m.tbx0_||m.tby1_<=m.tby0_)continue;h.push(m);let e=u[b];if(void 0!==e){p<e.cbxMin?e.cbxMin=p:p>e.cbxMax&&(e.cbxMax=p);g<e.cbyMin?e.cbxMin=g:g>e.cbyMax&&(e.cbyMax=g)}else u[b]=e={cbxMin:p,cbyMin:g,cbxMax:p,cbyMax:g};m.precinct=e}t.codeblockParameters={codeblockWidth:i,codeblockHeight:r,numcodeblockwide:l-a+1,numcodeblockhigh:f-c+1};t.codeblocks=h;t.precincts=u}function createPacket(e,t,n){const i=[],r=e.subbands;for(let e=0,n=r.length;e<n;e++){const n=r[e].codeblocks;for(let e=0,r=n.length;e<r;e++){const r=n[e];r.precinctNumber===t&&i.push(r)}}return{layerNumber:n,codeblocks:i}}function LayerResolutionComponentPositionIterator(e){const t=e.SIZ,n=e.currentTile.index,i=e.tiles[n],r=i.codingStyleDefaultParameters.layersCount,s=t.Csiz;let o=0;for(let e=0;e<s;e++)o=Math.max(o,i.components[e].codingStyleParameters.decompositionLevelsCount);let a=0,c=0,l=0,f=0;this.nextPacket=function JpxImage_nextPacket(){for(;a<r;a++){for(;c<=o;c++){for(;l<s;l++){const e=i.components[l];if(c>e.codingStyleParameters.decompositionLevelsCount)continue;const t=e.resolutions[c],n=t.precinctParameters.numprecincts;for(;f<n;){const e=createPacket(t,f,a);f++;return e}f=0}l=0}c=0}throw new JpxError("Out of packets")}}function ResolutionLayerComponentPositionIterator(e){const t=e.SIZ,n=e.currentTile.index,i=e.tiles[n],r=i.codingStyleDefaultParameters.layersCount,s=t.Csiz;let o=0;for(let e=0;e<s;e++)o=Math.max(o,i.components[e].codingStyleParameters.decompositionLevelsCount);let a=0,c=0,l=0,f=0;this.nextPacket=function JpxImage_nextPacket(){for(;a<=o;a++){for(;c<r;c++){for(;l<s;l++){const e=i.components[l];if(a>e.codingStyleParameters.decompositionLevelsCount)continue;const t=e.resolutions[a],n=t.precinctParameters.numprecincts;for(;f<n;){const e=createPacket(t,f,c);f++;return e}f=0}l=0}c=0}throw new JpxError("Out of packets")}}function ResolutionPositionComponentLayerIterator(e){const t=e.SIZ,n=e.currentTile.index,i=e.tiles[n],r=i.codingStyleDefaultParameters.layersCount,s=t.Csiz;let o,a,c,l,f=0;for(c=0;c<s;c++){const e=i.components[c];f=Math.max(f,e.codingStyleParameters.decompositionLevelsCount)}const d=new Int32Array(f+1);for(a=0;a<=f;++a){let e=0;for(c=0;c<s;++c){const t=i.components[c].resolutions;a<t.length&&(e=Math.max(e,t[a].precinctParameters.numprecincts))}d[a]=e}o=0;a=0;c=0;l=0;this.nextPacket=function JpxImage_nextPacket(){for(;a<=f;a++){for(;l<d[a];l++){for(;c<s;c++){const e=i.components[c];if(a>e.codingStyleParameters.decompositionLevelsCount)continue;const t=e.resolutions[a],n=t.precinctParameters.numprecincts;if(!(l>=n)){for(;o<r;){const e=createPacket(t,l,o);o++;return e}o=0}}c=0}l=0}throw new JpxError("Out of packets")}}function PositionComponentResolutionLayerIterator(e){const t=e.SIZ,n=e.currentTile.index,i=e.tiles[n],r=i.codingStyleDefaultParameters.layersCount,s=t.Csiz,o=getPrecinctSizesInImageScale(i),a=o;let c=0,l=0,f=0,d=0,h=0;this.nextPacket=function JpxImage_nextPacket(){for(;h<a.maxNumHigh;h++){for(;d<a.maxNumWide;d++){for(;f<s;f++){const e=i.components[f],t=e.codingStyleParameters.decompositionLevelsCount;for(;l<=t;l++){const t=e.resolutions[l],n=o.components[f].resolutions[l],i=getPrecinctIndexIfExist(d,h,n,a,t);if(null!==i){for(;c<r;){const e=createPacket(t,i,c);c++;return e}c=0}}l=0}f=0}d=0}throw new JpxError("Out of packets")}}function ComponentPositionResolutionLayerIterator(e){const t=e.SIZ,n=e.currentTile.index,i=e.tiles[n],r=i.codingStyleDefaultParameters.layersCount,s=t.Csiz,o=getPrecinctSizesInImageScale(i);let a=0,c=0,l=0,f=0,d=0;this.nextPacket=function JpxImage_nextPacket(){for(;l<s;++l){const e=i.components[l],t=o.components[l],n=e.codingStyleParameters.decompositionLevelsCount;for(;d<t.maxNumHigh;d++){for(;f<t.maxNumWide;f++){for(;c<=n;c++){const n=e.resolutions[c],i=t.resolutions[c],s=getPrecinctIndexIfExist(f,d,i,t,n);if(null!==s){for(;a<r;){const e=createPacket(n,s,a);a++;return e}a=0}}c=0}f=0}d=0}throw new JpxError("Out of packets")}}function getPrecinctIndexIfExist(e,t,n,i,r){const s=e*i.minWidth,o=t*i.minHeight;if(s%n.width!=0||o%n.height!=0)return null;const a=o/n.width*r.precinctParameters.numprecinctswide;return s/n.height+a}function getPrecinctSizesInImageScale(e){const t=e.components.length;let n=Number.MAX_VALUE,i=Number.MAX_VALUE,r=0,s=0;const o=new Array(t);for(let a=0;a<t;a++){const t=e.components[a],c=t.codingStyleParameters.decompositionLevelsCount,l=new Array(c+1);let f=Number.MAX_VALUE,d=Number.MAX_VALUE,h=0,u=0,p=1;for(let e=c;e>=0;--e){const n=t.resolutions[e],i=p*n.precinctParameters.precinctWidth,r=p*n.precinctParameters.precinctHeight;f=Math.min(f,i);d=Math.min(d,r);h=Math.max(h,n.precinctParameters.numprecinctswide);u=Math.max(u,n.precinctParameters.numprecinctshigh);l[e]={width:i,height:r};p<<=1}n=Math.min(n,f);i=Math.min(i,d);r=Math.max(r,h);s=Math.max(s,u);o[a]={resolutions:l,minWidth:f,minHeight:d,maxNumWide:h,maxNumHigh:u}}return{components:o,minWidth:n,minHeight:i,maxNumWide:r,maxNumHigh:s}}function buildPackets(e){const t=e.SIZ,n=e.currentTile.index,i=e.tiles[n],r=t.Csiz;for(let e=0;e<r;e++){const t=i.components[e],n=t.codingStyleParameters.decompositionLevelsCount,r=[],s=[];for(let e=0;e<=n;e++){const i=getBlocksDimensions(0,t,e),o={},a=1<<n-e;o.trx0=Math.ceil(t.tcx0/a);o.try0=Math.ceil(t.tcy0/a);o.trx1=Math.ceil(t.tcx1/a);o.try1=Math.ceil(t.tcy1/a);o.resLevel=e;buildPrecincts(0,o,i);r.push(o);let c;if(0===e){c={};c.type="LL";c.tbx0=Math.ceil(t.tcx0/a);c.tby0=Math.ceil(t.tcy0/a);c.tbx1=Math.ceil(t.tcx1/a);c.tby1=Math.ceil(t.tcy1/a);c.resolution=o;buildCodeblocks(0,c,i);s.push(c);o.subbands=[c]}else{const r=1<<n-e+1,a=[];c={};c.type="HL";c.tbx0=Math.ceil(t.tcx0/r-.5);c.tby0=Math.ceil(t.tcy0/r);c.tbx1=Math.ceil(t.tcx1/r-.5);c.tby1=Math.ceil(t.tcy1/r);c.resolution=o;buildCodeblocks(0,c,i);s.push(c);a.push(c);c={};c.type="LH";c.tbx0=Math.ceil(t.tcx0/r);c.tby0=Math.ceil(t.tcy0/r-.5);c.tbx1=Math.ceil(t.tcx1/r);c.tby1=Math.ceil(t.tcy1/r-.5);c.resolution=o;buildCodeblocks(0,c,i);s.push(c);a.push(c);c={};c.type="HH";c.tbx0=Math.ceil(t.tcx0/r-.5);c.tby0=Math.ceil(t.tcy0/r-.5);c.tbx1=Math.ceil(t.tcx1/r-.5);c.tby1=Math.ceil(t.tcy1/r-.5);c.resolution=o;buildCodeblocks(0,c,i);s.push(c);a.push(c);o.subbands=a}}t.resolutions=r;t.subbands=s}const s=i.codingStyleDefaultParameters.progressionOrder;switch(s){case 0:i.packetsIterator=new LayerResolutionComponentPositionIterator(e);break;case 1:i.packetsIterator=new ResolutionLayerComponentPositionIterator(e);break;case 2:i.packetsIterator=new ResolutionPositionComponentLayerIterator(e);break;case 3:i.packetsIterator=new PositionComponentResolutionLayerIterator(e);break;case 4:i.packetsIterator=new ComponentPositionResolutionLayerIterator(e);break;default:throw new JpxError(`Unsupported progression order ${s}`)}}function parseTilePackets(e,t,n,i){let s,o=0,a=0,c=!1;function readBits(e){for(;a<e;){const e=t[n+o];o++;if(c){s=s<<7|e;a+=7;c=!1}else{s=s<<8|e;a+=8}255===e&&(c=!0)}a-=e;return s>>>a&(1<<e)-1}function skipMarkerIfEqual(e){if(255===t[n+o-1]&&t[n+o]===e){skipBytes(1);return!0}if(255===t[n+o]&&t[n+o+1]===e){skipBytes(2);return!0}return!1}function skipBytes(e){o+=e}function alignToByte(){a=0;if(c){o++;c=!1}}function readCodingpasses(){if(0===readBits(1))return 1;if(0===readBits(1))return 2;let e=readBits(2);if(e<3)return e+3;e=readBits(5);if(e<31)return e+6;e=readBits(7);return e+37}const l=e.currentTile.index,f=e.tiles[l],d=e.COD.sopMarkerUsed,h=e.COD.ephMarkerUsed,u=f.packetsIterator;for(;o<i;){alignToByte();d&&skipMarkerIfEqual(145)&&skipBytes(4);const e=u.nextPacket();if(!readBits(1))continue;const i=e.layerNumber,s=[];let a;for(let t=0,n=e.codeblocks.length;t<n;t++){a=e.codeblocks[t];let n=a.precinct;const o=a.cbx-n.cbxMin,c=a.cby-n.cbyMin;let l,f,d=!1,h=!1;if(void 0!==a.included)d=!!readBits(1);else{n=a.precinct;let e;if(void 0!==n.inclusionTree)e=n.inclusionTree;else{const t=n.cbxMax-n.cbxMin+1,r=n.cbyMax-n.cbyMin+1;e=new InclusionTree(t,r,i);f=new TagTree(t,r);n.inclusionTree=e;n.zeroBitPlanesTree=f;for(let e=0;e<i;e++)if(0!==readBits(1))throw new JpxError("Invalid tag tree")}if(e.reset(o,c,i))for(;;){if(!readBits(1)){e.incrementValue(i);break}l=!e.nextLevel();if(l){a.included=!0;d=h=!0;break}}}if(!d)continue;if(h){f=n.zeroBitPlanesTree;f.reset(o,c);for(;;)if(readBits(1)){l=!f.nextLevel();if(l)break}else f.incrementValue();a.zeroBitPlanes=f.value}const u=readCodingpasses();for(;readBits(1);)a.Lblock++;const p=(0,r.log2)(u),g=readBits((u<1<<p?p-1:p)+a.Lblock);s.push({codeblock:a,codingpasses:u,dataLength:g})}alignToByte();h&&skipMarkerIfEqual(146);for(;s.length>0;){const e=s.shift();a=e.codeblock;void 0===a.data&&(a.data=[]);a.data.push({data:t,start:n+o,end:n+o+e.dataLength,codingpasses:e.codingpasses});o+=e.dataLength}}return o}function copyCoefficients(e,t,n,i,r,o,a,c,l){const f=i.tbx0,d=i.tby0,h=i.tbx1-i.tbx0,u=i.codeblocks,p="H"===i.type.charAt(0)?1:0,g="H"===i.type.charAt(1)?t:0;for(let n=0,m=u.length;n<m;++n){const m=u[n],b=m.tbx1_-m.tbx0_,x=m.tby1_-m.tby0_;if(0===b||0===x)continue;if(void 0===m.data)continue;const y=new BitModel(b,x,m.subbandType,m.zeroBitPlanes,o);let w=2;const T=m.data;let C,S,I,E=0,P=0;for(C=0,S=T.length;C<S;C++){I=T[C];E+=I.end-I.start;P+=I.codingpasses}const k=new Uint8Array(E);let B=0;for(C=0,S=T.length;C<S;C++){I=T[C];const e=I.data.subarray(I.start,I.end);k.set(e,B);B+=e.length}const A=new s.ArithmeticDecoder(k,0,E);y.setDecoder(A);for(C=0;C<P;C++){switch(w){case 0:y.runSignificancePropagationPass();break;case 1:y.runMagnitudeRefinementPass();break;case 2:y.runCleanupPass();c&&y.checkSegmentationSymbol()}l&&y.reset();w=(w+1)%3}let _=m.tbx0_-f+(m.tby0_-d)*h;const R=y.coefficentsSign,M=y.coefficentsMagnitude,v=y.bitsDecoded,L=a?0:.5;let D,O,F;B=0;const U="LL"!==i.type;for(C=0;C<x;C++){const n=2*(_/h|0)*(t-h)+p+g;for(D=0;D<b;D++){O=M[B];if(0!==O){O=(O+L)*r;0!==R[B]&&(O=-O);F=v[B];e[U?n+(_<<1):_]=a&&F>=o?O:O*(1<<o-F)}_++;B++}_+=h-b}}}function transformTile(e,t,n){const i=t.components[n],r=i.codingStyleParameters,s=i.quantizationParameters,a=r.decompositionLevelsCount,c=s.SPqcds,l=s.scalarExpounded,f=s.guardBits,d=r.segmentationSymbolUsed,h=r.resetContextProbabilities,u=e.components[n].precision,p=r.reversibleTransformation,g=p?new ReversibleTransform:new IrreversibleTransform,m=[];let b=0;for(let e=0;e<=a;e++){const t=i.resolutions[e],n=t.trx1-t.trx0,r=t.try1-t.try0,s=new Float32Array(n*r);for(let i=0,r=t.subbands.length;i<r;i++){let r,a;if(l){r=c[b].mu;a=c[b].epsilon;b++}else{r=c[0].mu;a=c[0].epsilon+(e>0?1-e:0)}const g=t.subbands[i],m=o[g.type];copyCoefficients(s,n,0,g,p?1:2**(u+m-a)*(1+r/2048),f+a-1,p,d,h)}m.push({width:n,height:r,items:s})}const x=g.calculate(m,i.tcx0,i.tcy0);return{left:i.tcx0,top:i.tcy0,width:x.width,height:x.height,items:x.items}}function initializeTile(e,t){const n=e.SIZ.Csiz,i=e.tiles[t];for(let t=0;t<n;t++){const n=i.components[t],r=void 0!==e.currentTile.QCC[t]?e.currentTile.QCC[t]:e.currentTile.QCD;n.quantizationParameters=r;const s=void 0!==e.currentTile.COC[t]?e.currentTile.COC[t]:e.currentTile.COD;n.codingStyleParameters=s}i.codingStyleDefaultParameters=e.currentTile.COD}class TagTree{constructor(e,t){const n=(0,r.log2)(Math.max(e,t))+1;this.levels=[];for(let i=0;i<n;i++){const n={width:e,height:t,items:[]};this.levels.push(n);e=Math.ceil(e/2);t=Math.ceil(t/2)}}reset(e,t){let n,i=0,r=0;for(;i<this.levels.length;){n=this.levels[i];const s=e+t*n.width;if(void 0!==n.items[s]){r=n.items[s];break}n.index=s;e>>=1;t>>=1;i++}i--;n=this.levels[i];n.items[n.index]=r;this.currentLevel=i;delete this.value}incrementValue(){const e=this.levels[this.currentLevel];e.items[e.index]++}nextLevel(){let e=this.currentLevel,t=this.levels[e];const n=t.items[t.index];e--;if(e<0){this.value=n;return!1}this.currentLevel=e;t=this.levels[e];t.items[t.index]=n;return!0}}class InclusionTree{constructor(e,t,n){const i=(0,r.log2)(Math.max(e,t))+1;this.levels=[];for(let r=0;r<i;r++){const i=new Uint8Array(e*t);for(let e=0,t=i.length;e<t;e++)i[e]=n;const r={width:e,height:t,items:i};this.levels.push(r);e=Math.ceil(e/2);t=Math.ceil(t/2)}}reset(e,t,n){let i=0;for(;i<this.levels.length;){const r=this.levels[i],s=e+t*r.width;r.index=s;const o=r.items[s];if(255===o)break;if(o>n){this.currentLevel=i;this.propagateValues();return!1}e>>=1;t>>=1;i++}this.currentLevel=i-1;return!0}incrementValue(e){const t=this.levels[this.currentLevel];t.items[t.index]=e+1;this.propagateValues()}propagateValues(){let e=this.currentLevel,t=this.levels[e];const n=t.items[t.index];for(;--e>=0;){t=this.levels[e];t.items[t.index]=n}}nextLevel(){let e=this.currentLevel,t=this.levels[e];const n=t.items[t.index];t.items[t.index]=255;e--;if(e<0)return!1;this.currentLevel=e;t=this.levels[e];t.items[t.index]=n;return!0}}class BitModel{static UNIFORM_CONTEXT=17;static RUNLENGTH_CONTEXT=18;static LLAndLHContextsLabel=new Uint8Array([0,5,8,0,3,7,8,0,4,7,8,0,0,0,0,0,1,6,8,0,3,7,8,0,4,7,8,0,0,0,0,0,2,6,8,0,3,7,8,0,4,7,8,0,0,0,0,0,2,6,8,0,3,7,8,0,4,7,8,0,0,0,0,0,2,6,8,0,3,7,8,0,4,7,8]);static HLContextLabel=new Uint8Array([0,3,4,0,5,7,7,0,8,8,8,0,0,0,0,0,1,3,4,0,6,7,7,0,8,8,8,0,0,0,0,0,2,3,4,0,6,7,7,0,8,8,8,0,0,0,0,0,2,3,4,0,6,7,7,0,8,8,8,0,0,0,0,0,2,3,4,0,6,7,7,0,8,8,8]);static HHContextLabel=new Uint8Array([0,1,2,0,1,2,2,0,2,2,2,0,0,0,0,0,3,4,5,0,4,5,5,0,5,5,5,0,0,0,0,0,6,7,7,0,7,7,7,0,7,7,7,0,0,0,0,0,8,8,8,0,8,8,8,0,8,8,8,0,0,0,0,0,8,8,8,0,8,8,8,0,8,8,8]);constructor(e,t,n,i,r){this.width=e;this.height=t;let s;s="HH"===n?BitModel.HHContextLabel:"HL"===n?BitModel.HLContextLabel:BitModel.LLAndLHContextsLabel;this.contextLabelTable=s;const o=e*t;this.neighborsSignificance=new Uint8Array(o);this.coefficentsSign=new Uint8Array(o);let a;a=r>14?new Uint32Array(o):r>6?new Uint16Array(o):new Uint8Array(o);this.coefficentsMagnitude=a;this.processingFlags=new Uint8Array(o);const c=new Uint8Array(o);if(0!==i)for(let e=0;e<o;e++)c[e]=i;this.bitsDecoded=c;this.reset()}setDecoder(e){this.decoder=e}reset(){this.contexts=new Int8Array(19);this.contexts[0]=8;this.contexts[BitModel.UNIFORM_CONTEXT]=92;this.contexts[BitModel.RUNLENGTH_CONTEXT]=6}setNeighborsSignificance(e,t,n){const i=this.neighborsSignificance,r=this.width,s=this.height,o=t>0,a=t+1<r;let c;if(e>0){c=n-r;o&&(i[c-1]+=16);a&&(i[c+1]+=16);i[c]+=4}if(e+1<s){c=n+r;o&&(i[c-1]+=16);a&&(i[c+1]+=16);i[c]+=4}o&&(i[n-1]+=1);a&&(i[n+1]+=1);i[n]|=128}runSignificancePropagationPass(){const e=this.decoder,t=this.width,n=this.height,i=this.coefficentsMagnitude,r=this.coefficentsSign,s=this.neighborsSignificance,o=this.processingFlags,a=this.contexts,c=this.contextLabelTable,l=this.bitsDecoded;for(let f=0;f<n;f+=4)for(let d=0;d<t;d++){let h=f*t+d;for(let u=0;u<4;u++,h+=t){const t=f+u;if(t>=n)break;o[h]&=-2;if(i[h]||!s[h])continue;const p=c[s[h]];if(e.readBit(a,p)){const e=this.decodeSignBit(t,d,h);r[h]=e;i[h]=1;this.setNeighborsSignificance(t,d,h);o[h]|=2}l[h]++;o[h]|=1}}}decodeSignBit(e,t,n){const i=this.width,r=this.height,s=this.coefficentsMagnitude,o=this.coefficentsSign;let a,c,l,f,d,h;f=t>0&&0!==s[n-1];if(t+1<i&&0!==s[n+1]){l=o[n+1];if(f){c=o[n-1];a=1-l-c}else a=1-l-l}else if(f){c=o[n-1];a=1-c-c}else a=0;const u=3*a;f=e>0&&0!==s[n-i];if(e+1<r&&0!==s[n+i]){l=o[n+i];if(f){c=o[n-i];a=1-l-c+u}else a=1-l-l+u}else if(f){c=o[n-i];a=1-c-c+u}else a=u;if(a>=0){d=9+a;h=this.decoder.readBit(this.contexts,d)}else{d=9-a;h=1^this.decoder.readBit(this.contexts,d)}return h}runMagnitudeRefinementPass(){const e=this.decoder,t=this.width,n=this.height,i=this.coefficentsMagnitude,r=this.neighborsSignificance,s=this.contexts,o=this.bitsDecoded,a=this.processingFlags,c=t*n,l=4*t;for(let n,f=0;f<c;f=n){n=Math.min(c,f+l);for(let c=0;c<t;c++)for(let l=f+c;l<n;l+=t){if(!i[l]||0!=(1&a[l]))continue;let t=16;if(0!=(2&a[l])){a[l]^=2;t=0===(127&r[l])?15:14}const n=e.readBit(s,t);i[l]=i[l]<<1|n;o[l]++;a[l]|=1}}}runCleanupPass(){const e=this.decoder,t=this.width,n=this.height,i=this.neighborsSignificance,r=this.coefficentsMagnitude,s=this.coefficentsSign,o=this.contexts,a=this.contextLabelTable,c=this.bitsDecoded,l=this.processingFlags,f=t,d=2*t,h=3*t;let u;for(let p=0;p<n;p=u){u=Math.min(p+4,n);const g=p*t,m=p+3<n;for(let n=0;n<t;n++){const b=g+n;let x,y=0,w=b,T=p;if(m&&0===l[b]&&0===l[b+f]&&0===l[b+d]&&0===l[b+h]&&0===i[b]&&0===i[b+f]&&0===i[b+d]&&0===i[b+h]){if(!e.readBit(o,BitModel.RUNLENGTH_CONTEXT)){c[b]++;c[b+f]++;c[b+d]++;c[b+h]++;continue}y=e.readBit(o,BitModel.UNIFORM_CONTEXT)<<1|e.readBit(o,BitModel.UNIFORM_CONTEXT);if(0!==y){T=p+y;w+=y*t}x=this.decodeSignBit(T,n,w);s[w]=x;r[w]=1;this.setNeighborsSignificance(T,n,w);l[w]|=2;w=b;for(let e=p;e<=T;e++,w+=t)c[w]++;y++}for(T=p+y;T<u;T++,w+=t){if(r[w]||0!=(1&l[w]))continue;const t=a[i[w]];if(1===e.readBit(o,t)){x=this.decodeSignBit(T,n,w);s[w]=x;r[w]=1;this.setNeighborsSignificance(T,n,w);l[w]|=2}c[w]++}}}}checkSegmentationSymbol(){const e=this.decoder,t=this.contexts;if(10!==(e.readBit(t,BitModel.UNIFORM_CONTEXT)<<3|e.readBit(t,BitModel.UNIFORM_CONTEXT)<<2|e.readBit(t,BitModel.UNIFORM_CONTEXT)<<1|e.readBit(t,BitModel.UNIFORM_CONTEXT)))throw new JpxError("Invalid segmentation symbol")}}class Transform{constructor(){this.constructor===Transform&&(0,i.unreachable)("Cannot initialize Transform.")}calculate(e,t,n){let i=e[0];for(let r=1,s=e.length;r<s;r++)i=this.iterate(i,e[r],t,n);return i}extend(e,t,n){let i=t-1,r=t+1,s=t+n-2,o=t+n;e[i--]=e[r++];e[o++]=e[s--];e[i--]=e[r++];e[o++]=e[s--];e[i--]=e[r++];e[o++]=e[s--];e[i]=e[r];e[o]=e[s]}filter(e,t,n){(0,i.unreachable)("Abstract method `filter` called")}iterate(e,t,n,i){const r=e.width,s=e.height;let o=e.items;const a=t.width,c=t.height,l=t.items;let f,d,h,u,p,g;for(h=0,f=0;f<s;f++){u=2*f*a;for(d=0;d<r;d++,h++,u+=2)l[u]=o[h]}o=e.items=null;const m=new Float32Array(a+8);if(1===a){if(0!=(1&n))for(g=0,h=0;g<c;g++,h+=a)l[h]*=.5}else for(g=0,h=0;g<c;g++,h+=a){m.set(l.subarray(h,h+a),4);this.extend(m,4,a);this.filter(m,4,a);l.set(m.subarray(4,4+a),h)}let b=16;const x=[];for(f=0;f<b;f++)x.push(new Float32Array(c+8));let y,w=0;e=4+c;if(1===c){if(0!=(1&i))for(p=0;p<a;p++)l[p]*=.5}else for(p=0;p<a;p++){if(0===w){b=Math.min(a-p,b);for(h=p,u=4;u<e;h+=a,u++)for(y=0;y<b;y++)x[y][u]=l[h+y];w=b}w--;const t=x[w];this.extend(t,4,c);this.filter(t,4,c);if(0===w){h=p-b+1;for(u=4;u<e;h+=a,u++)for(y=0;y<b;y++)l[h+y]=x[y][u]}}return{width:a,height:c,items:l}}}class IrreversibleTransform extends Transform{filter(e,t,n){const i=n>>1;let r,s,o,a;const c=-1.586134342059924,l=-.052980118572961,f=.882911075530934,d=.443506852043971,h=1.230174104914001;r=(t|=0)-3;for(s=i+4;s--;r+=2)e[r]*=.8128930661159609;r=t-2;o=d*e[r-1];for(s=i+3;s--;r+=2){a=d*e[r+1];e[r]=h*e[r]-o-a;if(!s--)break;r+=2;o=d*e[r+1];e[r]=h*e[r]-o-a}r=t-1;o=f*e[r-1];for(s=i+2;s--;r+=2){a=f*e[r+1];e[r]-=o+a;if(!s--)break;r+=2;o=f*e[r+1];e[r]-=o+a}r=t;o=l*e[r-1];for(s=i+1;s--;r+=2){a=l*e[r+1];e[r]-=o+a;if(!s--)break;r+=2;o=l*e[r+1];e[r]-=o+a}if(0!==i){r=t+1;o=c*e[r-1];for(s=i;s--;r+=2){a=c*e[r+1];e[r]-=o+a;if(!s--)break;r+=2;o=c*e[r+1];e[r]-=o+a}}}}class ReversibleTransform extends Transform{filter(e,t,n){const i=n>>1;let r,s;for(r=t|=0,s=i+1;s--;r+=2)e[r]-=e[r-1]+e[r+1]+2>>2;for(r=t+1,s=i;s--;r+=2)e[r]+=e[r-1]+e[r+1]>>1}}}],t={};function __w_pdfjs_require__(n){var i=t[n];if(void 0!==i)return i.exports;var r=t[n]={exports:{}};e[n](r,r.exports,__w_pdfjs_require__);return r.exports}var n={};(()=>{var e=n;Object.defineProperty(e,"__esModule",{value:!0});Object.defineProperty(e,"Jbig2Image",{enumerable:!0,get:function(){return i.Jbig2Image}});Object.defineProperty(e,"JpegImage",{enumerable:!0,get:function(){return r.JpegImage}});Object.defineProperty(e,"JpxImage",{enumerable:!0,get:function(){return s.JpxImage}});Object.defineProperty(e,"getVerbosityLevel",{enumerable:!0,get:function(){return t.getVerbosityLevel}});Object.defineProperty(e,"setVerbosityLevel",{enumerable:!0,get:function(){return t.setVerbosityLevel}});var t=__w_pdfjs_require__(1),i=__w_pdfjs_require__(2),r=__w_pdfjs_require__(8),s=__w_pdfjs_require__(10)})();return n})()));