{"ast": null, "code": "// PDF Parser for CFA Questions and Answers\nimport * as pdfjsLib from 'pdfjs-dist';\n\n// Set worker path - use a compatible version\npdfjsLib.GlobalWorkerOptions.workerSrc = new URL('pdfjs-dist/build/pdf.worker.js', import.meta.url).toString();\nclass CFAQuestionParser {\n  constructor() {\n    this.questions = [];\n    this.answers = {};\n  }\n  async parseQuestionFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      let fullText = '';\n\n      // Extract text from all pages\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n        const pageText = textContent.items.map(item => item.str).join(' ');\n        fullText += pageText + '\\n';\n      }\n\n      // Parse questions from text\n      const questions = this.extractQuestions(fullText);\n      return questions;\n    } catch (error) {\n      console.error('Error parsing question file:', error);\n      throw error;\n    }\n  }\n  async parseAnswerFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      let fullText = '';\n\n      // Extract text from all pages\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n        const pageText = textContent.items.map(item => item.str).join(' ');\n        fullText += pageText + '\\n';\n      }\n\n      // Parse answers from text\n      const answers = this.extractAnswers(fullText);\n      return answers;\n    } catch (error) {\n      console.error('Error parsing answer file:', error);\n      throw error;\n    }\n  }\n  extractQuestions(text) {\n    const questions = [];\n\n    // Clean text\n    text = text.replace(/\\s+/g, ' ').trim();\n\n    // Split by Question # pattern\n    const questionBlocks = text.split(/(?=Question #\\d+)/);\n    let currentContext = '';\n    for (const block of questionBlocks) {\n      if (!block.trim()) continue;\n\n      // Check for single question pattern\n      const singleMatch = block.match(/Question #(\\d+) of (\\d+) Question ID: (\\d+)\\s*(.*)/s);\n\n      // Check for group question pattern\n      const groupMatch = block.match(/Question #(\\d+) - (\\d+) of (\\d+) Question ID: (\\d+)\\s*(.*)/s);\n      if (groupMatch) {\n        const [, startQ, endQ, totalQ, questionId, content] = groupMatch;\n\n        // Extract context before the question range\n        const contextMatch = content.match(/^(.*?)(?=Question #\\d+ - \\d+)/s);\n        if (contextMatch) {\n          currentContext = contextMatch[1].trim();\n        }\n\n        // Extract individual questions in the group\n        const groupQuestions = this.extractGroupQuestions(content, parseInt(startQ), parseInt(endQ), parseInt(totalQ), currentContext);\n        questions.push(...groupQuestions);\n      } else if (singleMatch) {\n        const [, questionNum, totalQ, questionId, content] = singleMatch;\n        const questionData = this.parseSingleQuestion(content, parseInt(questionNum), parseInt(totalQ), questionId);\n        if (questionData) {\n          questions.push(questionData);\n        }\n\n        // Reset context for single questions\n        currentContext = '';\n      }\n    }\n    return questions;\n  }\n  extractGroupQuestions(content, startQ, endQ, totalQ, context) {\n    const questions = [];\n\n    // Split by individual question patterns within the group\n    const subQuestionPattern = /Question #(\\d+) - \\d+ of \\d+ Question ID: (\\d+)\\s*(.*?)(?=Question #\\d+|$)/gs;\n    let match;\n    while ((match = subQuestionPattern.exec(content)) !== null) {\n      const [, questionNum, questionId, questionContent] = match;\n      const questionData = this.parseSingleQuestion(questionContent, parseInt(questionNum), totalQ, questionId, context);\n      if (questionData) {\n        questions.push(questionData);\n      }\n    }\n    return questions;\n  }\n  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {\n    if (!content.trim()) return null;\n\n    // Split content into lines\n    const lines = content.split('\\n').map(line => line.trim()).filter(line => line);\n    let questionText = '';\n    const choices = {};\n    let currentChoice = null;\n    let choiceText = '';\n    for (const line of lines) {\n      // Check if line starts with choice pattern (A), B), C), etc.\n      const choiceMatch = line.match(/^([A-E])\\)\\s*(.*)/);\n      if (choiceMatch) {\n        // Save previous choice\n        if (currentChoice) {\n          choices[currentChoice] = choiceText.trim();\n        }\n        currentChoice = choiceMatch[1];\n        choiceText = choiceMatch[2];\n      } else if (currentChoice) {\n        // Continue choice text\n        choiceText += ' ' + line;\n      } else {\n        // Question text\n        questionText += ' ' + line;\n      }\n    }\n\n    // Save last choice\n    if (currentChoice) {\n      choices[currentChoice] = choiceText.trim();\n    }\n    if (!questionText.trim() || Object.keys(choices).length === 0) {\n      return null;\n    }\n    return {\n      questionNumber: questionNum,\n      totalQuestions: totalQ,\n      questionId: questionId,\n      context: context,\n      questionText: questionText.trim(),\n      choices: choices,\n      isGroupQuestion: Boolean(context)\n    };\n  }\n  extractAnswers(text) {\n    const answers = {};\n\n    // Clean text\n    text = text.replace(/\\s+/g, ' ').trim();\n\n    // Split by Question # pattern\n    const questionBlocks = text.split(/Question #(\\d+)/);\n    for (let i = 1; i < questionBlocks.length; i += 2) {\n      if (i + 1 < questionBlocks.length) {\n        const questionNum = parseInt(questionBlocks[i]);\n        const content = questionBlocks[i + 1];\n\n        // Find correct answer\n        let correctAnswer = null;\n        const answerPatterns = [/The correct answer is ([A-E])/i, /Correct answer:\\s*([A-E])/i, /Answer:\\s*([A-E])/i, /([A-E])\\s*is correct/i];\n        for (const pattern of answerPatterns) {\n          const match = content.match(pattern);\n          if (match) {\n            correctAnswer = match[1].toUpperCase();\n            break;\n          }\n        }\n\n        // Find explanation\n        let explanation = '';\n        const explanationPatterns = [/Explanation[:\\s]+(.*?)(?=Question #|$)/is, /Explanation[:\\s]+(.*?)(?=\\(Module|$)/is];\n        for (const pattern of explanationPatterns) {\n          const match = content.match(pattern);\n          if (match) {\n            explanation = match[1].trim();\n            break;\n          }\n        }\n        if (correctAnswer) {\n          answers[questionNum] = {\n            correctAnswer: correctAnswer,\n            explanation: explanation\n          };\n        }\n      }\n    }\n    return answers;\n  }\n  async parseFiles(questionFile, answerFile) {\n    try {\n      const [questions, answers] = await Promise.all([this.parseQuestionFile(questionFile), this.parseAnswerFile(answerFile)]);\n      return {\n        questions,\n        answers\n      };\n    } catch (error) {\n      console.error('Error parsing files:', error);\n      throw error;\n    }\n  }\n}\nexport default CFAQuestionParser;", "map": {"version": 3, "names": ["pdfjsLib", "GlobalWorkerOptions", "workerSrc", "URL", "import", "meta", "url", "toString", "CFAQuestionParser", "constructor", "questions", "answers", "parseQuestionFile", "file", "arrayBuffer", "pdf", "getDocument", "promise", "fullText", "i", "numPages", "page", "getPage", "textContent", "getTextContent", "pageText", "items", "map", "item", "str", "join", "extractQuestions", "error", "console", "parseAnswerFile", "extractAnswers", "text", "replace", "trim", "questionBlocks", "split", "currentContext", "block", "singleMatch", "match", "groupMatch", "startQ", "endQ", "totalQ", "questionId", "content", "contextMatch", "groupQuestions", "extractGroupQuestions", "parseInt", "push", "questionNum", "questionData", "parseSingleQuestion", "context", "subQuestionPattern", "exec", "questionContent", "lines", "line", "filter", "questionText", "choices", "currentChoice", "choiceText", "choiceMatch", "Object", "keys", "length", "questionNumber", "totalQuestions", "isGroupQuestion", "Boolean", "<PERSON><PERSON><PERSON><PERSON>", "answerPatterns", "pattern", "toUpperCase", "explanation", "explanationPatterns", "parseFiles", "questionFile", "answerFile", "Promise", "all"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/utils/pdfParser.js"], "sourcesContent": ["// PDF Parser for CFA Questions and Answers\nimport * as pdfjsLib from 'pdfjs-dist';\n\n// Set worker path - use a compatible version\npdfjsLib.GlobalWorkerOptions.workerSrc = new URL(\n  'pdfjs-dist/build/pdf.worker.js',\n  import.meta.url,\n).toString();\n\nclass CFAQuestionParser {\n  constructor() {\n    this.questions = [];\n    this.answers = {};\n  }\n\n  async parseQuestionFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      \n      let fullText = '';\n      \n      // Extract text from all pages\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n        const pageText = textContent.items.map(item => item.str).join(' ');\n        fullText += pageText + '\\n';\n      }\n      \n      // Parse questions from text\n      const questions = this.extractQuestions(fullText);\n      return questions;\n    } catch (error) {\n      console.error('Error parsing question file:', error);\n      throw error;\n    }\n  }\n\n  async parseAnswerFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      \n      let fullText = '';\n      \n      // Extract text from all pages\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n        const pageText = textContent.items.map(item => item.str).join(' ');\n        fullText += pageText + '\\n';\n      }\n      \n      // Parse answers from text\n      const answers = this.extractAnswers(fullText);\n      return answers;\n    } catch (error) {\n      console.error('Error parsing answer file:', error);\n      throw error;\n    }\n  }\n\n  extractQuestions(text) {\n    const questions = [];\n    \n    // Clean text\n    text = text.replace(/\\s+/g, ' ').trim();\n    \n    // Split by Question # pattern\n    const questionBlocks = text.split(/(?=Question #\\d+)/);\n    \n    let currentContext = '';\n    \n    for (const block of questionBlocks) {\n      if (!block.trim()) continue;\n      \n      // Check for single question pattern\n      const singleMatch = block.match(/Question #(\\d+) of (\\d+) Question ID: (\\d+)\\s*(.*)/s);\n      \n      // Check for group question pattern\n      const groupMatch = block.match(/Question #(\\d+) - (\\d+) of (\\d+) Question ID: (\\d+)\\s*(.*)/s);\n      \n      if (groupMatch) {\n        const [, startQ, endQ, totalQ, questionId, content] = groupMatch;\n        \n        // Extract context before the question range\n        const contextMatch = content.match(/^(.*?)(?=Question #\\d+ - \\d+)/s);\n        if (contextMatch) {\n          currentContext = contextMatch[1].trim();\n        }\n        \n        // Extract individual questions in the group\n        const groupQuestions = this.extractGroupQuestions(\n          content, \n          parseInt(startQ), \n          parseInt(endQ), \n          parseInt(totalQ), \n          currentContext\n        );\n        \n        questions.push(...groupQuestions);\n        \n      } else if (singleMatch) {\n        const [, questionNum, totalQ, questionId, content] = singleMatch;\n        \n        const questionData = this.parseSingleQuestion(\n          content, \n          parseInt(questionNum), \n          parseInt(totalQ), \n          questionId\n        );\n        \n        if (questionData) {\n          questions.push(questionData);\n        }\n        \n        // Reset context for single questions\n        currentContext = '';\n      }\n    }\n    \n    return questions;\n  }\n\n  extractGroupQuestions(content, startQ, endQ, totalQ, context) {\n    const questions = [];\n    \n    // Split by individual question patterns within the group\n    const subQuestionPattern = /Question #(\\d+) - \\d+ of \\d+ Question ID: (\\d+)\\s*(.*?)(?=Question #\\d+|$)/gs;\n    let match;\n    \n    while ((match = subQuestionPattern.exec(content)) !== null) {\n      const [, questionNum, questionId, questionContent] = match;\n      \n      const questionData = this.parseSingleQuestion(\n        questionContent,\n        parseInt(questionNum),\n        totalQ,\n        questionId,\n        context\n      );\n      \n      if (questionData) {\n        questions.push(questionData);\n      }\n    }\n    \n    return questions;\n  }\n\n  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {\n    if (!content.trim()) return null;\n    \n    // Split content into lines\n    const lines = content.split('\\n').map(line => line.trim()).filter(line => line);\n    \n    let questionText = '';\n    const choices = {};\n    let currentChoice = null;\n    let choiceText = '';\n    \n    for (const line of lines) {\n      // Check if line starts with choice pattern (A), B), C), etc.\n      const choiceMatch = line.match(/^([A-E])\\)\\s*(.*)/);\n      \n      if (choiceMatch) {\n        // Save previous choice\n        if (currentChoice) {\n          choices[currentChoice] = choiceText.trim();\n        }\n        \n        currentChoice = choiceMatch[1];\n        choiceText = choiceMatch[2];\n      } else if (currentChoice) {\n        // Continue choice text\n        choiceText += ' ' + line;\n      } else {\n        // Question text\n        questionText += ' ' + line;\n      }\n    }\n    \n    // Save last choice\n    if (currentChoice) {\n      choices[currentChoice] = choiceText.trim();\n    }\n    \n    if (!questionText.trim() || Object.keys(choices).length === 0) {\n      return null;\n    }\n    \n    return {\n      questionNumber: questionNum,\n      totalQuestions: totalQ,\n      questionId: questionId,\n      context: context,\n      questionText: questionText.trim(),\n      choices: choices,\n      isGroupQuestion: Boolean(context)\n    };\n  }\n\n  extractAnswers(text) {\n    const answers = {};\n    \n    // Clean text\n    text = text.replace(/\\s+/g, ' ').trim();\n    \n    // Split by Question # pattern\n    const questionBlocks = text.split(/Question #(\\d+)/);\n    \n    for (let i = 1; i < questionBlocks.length; i += 2) {\n      if (i + 1 < questionBlocks.length) {\n        const questionNum = parseInt(questionBlocks[i]);\n        const content = questionBlocks[i + 1];\n        \n        // Find correct answer\n        let correctAnswer = null;\n        const answerPatterns = [\n          /The correct answer is ([A-E])/i,\n          /Correct answer:\\s*([A-E])/i,\n          /Answer:\\s*([A-E])/i,\n          /([A-E])\\s*is correct/i\n        ];\n        \n        for (const pattern of answerPatterns) {\n          const match = content.match(pattern);\n          if (match) {\n            correctAnswer = match[1].toUpperCase();\n            break;\n          }\n        }\n        \n        // Find explanation\n        let explanation = '';\n        const explanationPatterns = [\n          /Explanation[:\\s]+(.*?)(?=Question #|$)/is,\n          /Explanation[:\\s]+(.*?)(?=\\(Module|$)/is\n        ];\n        \n        for (const pattern of explanationPatterns) {\n          const match = content.match(pattern);\n          if (match) {\n            explanation = match[1].trim();\n            break;\n          }\n        }\n        \n        if (correctAnswer) {\n          answers[questionNum] = {\n            correctAnswer: correctAnswer,\n            explanation: explanation\n          };\n        }\n      }\n    }\n    \n    return answers;\n  }\n\n  async parseFiles(questionFile, answerFile) {\n    try {\n      const [questions, answers] = await Promise.all([\n        this.parseQuestionFile(questionFile),\n        this.parseAnswerFile(answerFile)\n      ]);\n      \n      return { questions, answers };\n    } catch (error) {\n      console.error('Error parsing files:', error);\n      throw error;\n    }\n  }\n}\n\nexport default CFAQuestionParser;\n"], "mappings": "AAAA;AACA,OAAO,KAAKA,QAAQ,MAAM,YAAY;;AAEtC;AACAA,QAAQ,CAACC,mBAAmB,CAACC,SAAS,GAAG,IAAIC,GAAG,CAC9C,gCAAgC,EAChCC,MAAM,CAACC,IAAI,CAACC,GACd,CAAC,CAACC,QAAQ,CAAC,CAAC;AAEZ,MAAMC,iBAAiB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;EACnB;EAEA,MAAMC,iBAAiBA,CAACC,IAAI,EAAE;IAC5B,IAAI;MACF,MAAMC,WAAW,GAAG,MAAMD,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5C,MAAMC,GAAG,GAAG,MAAMf,QAAQ,CAACgB,WAAW,CAACF,WAAW,CAAC,CAACG,OAAO;MAE3D,IAAIC,QAAQ,GAAG,EAAE;;MAEjB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,GAAG,CAACK,QAAQ,EAAED,CAAC,EAAE,EAAE;QACtC,MAAME,IAAI,GAAG,MAAMN,GAAG,CAACO,OAAO,CAACH,CAAC,CAAC;QACjC,MAAMI,WAAW,GAAG,MAAMF,IAAI,CAACG,cAAc,CAAC,CAAC;QAC/C,MAAMC,QAAQ,GAAGF,WAAW,CAACG,KAAK,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;QAClEZ,QAAQ,IAAIO,QAAQ,GAAG,IAAI;MAC7B;;MAEA;MACA,MAAMf,SAAS,GAAG,IAAI,CAACqB,gBAAgB,CAACb,QAAQ,CAAC;MACjD,OAAOR,SAAS;IAClB,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;EAEA,MAAME,eAAeA,CAACrB,IAAI,EAAE;IAC1B,IAAI;MACF,MAAMC,WAAW,GAAG,MAAMD,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5C,MAAMC,GAAG,GAAG,MAAMf,QAAQ,CAACgB,WAAW,CAACF,WAAW,CAAC,CAACG,OAAO;MAE3D,IAAIC,QAAQ,GAAG,EAAE;;MAEjB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,GAAG,CAACK,QAAQ,EAAED,CAAC,EAAE,EAAE;QACtC,MAAME,IAAI,GAAG,MAAMN,GAAG,CAACO,OAAO,CAACH,CAAC,CAAC;QACjC,MAAMI,WAAW,GAAG,MAAMF,IAAI,CAACG,cAAc,CAAC,CAAC;QAC/C,MAAMC,QAAQ,GAAGF,WAAW,CAACG,KAAK,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;QAClEZ,QAAQ,IAAIO,QAAQ,GAAG,IAAI;MAC7B;;MAEA;MACA,MAAMd,OAAO,GAAG,IAAI,CAACwB,cAAc,CAACjB,QAAQ,CAAC;MAC7C,OAAOP,OAAO;IAChB,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF;EAEAD,gBAAgBA,CAACK,IAAI,EAAE;IACrB,MAAM1B,SAAS,GAAG,EAAE;;IAEpB;IACA0B,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC;;IAEvC;IACA,MAAMC,cAAc,GAAGH,IAAI,CAACI,KAAK,CAAC,mBAAmB,CAAC;IAEtD,IAAIC,cAAc,GAAG,EAAE;IAEvB,KAAK,MAAMC,KAAK,IAAIH,cAAc,EAAE;MAClC,IAAI,CAACG,KAAK,CAACJ,IAAI,CAAC,CAAC,EAAE;;MAEnB;MACA,MAAMK,WAAW,GAAGD,KAAK,CAACE,KAAK,CAAC,qDAAqD,CAAC;;MAEtF;MACA,MAAMC,UAAU,GAAGH,KAAK,CAACE,KAAK,CAAC,6DAA6D,CAAC;MAE7F,IAAIC,UAAU,EAAE;QACd,MAAM,GAAGC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,CAAC,GAAGL,UAAU;;QAEhE;QACA,MAAMM,YAAY,GAAGD,OAAO,CAACN,KAAK,CAAC,gCAAgC,CAAC;QACpE,IAAIO,YAAY,EAAE;UAChBV,cAAc,GAAGU,YAAY,CAAC,CAAC,CAAC,CAACb,IAAI,CAAC,CAAC;QACzC;;QAEA;QACA,MAAMc,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAC/CH,OAAO,EACPI,QAAQ,CAACR,MAAM,CAAC,EAChBQ,QAAQ,CAACP,IAAI,CAAC,EACdO,QAAQ,CAACN,MAAM,CAAC,EAChBP,cACF,CAAC;QAED/B,SAAS,CAAC6C,IAAI,CAAC,GAAGH,cAAc,CAAC;MAEnC,CAAC,MAAM,IAAIT,WAAW,EAAE;QACtB,MAAM,GAAGa,WAAW,EAAER,MAAM,EAAEC,UAAU,EAAEC,OAAO,CAAC,GAAGP,WAAW;QAEhE,MAAMc,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CR,OAAO,EACPI,QAAQ,CAACE,WAAW,CAAC,EACrBF,QAAQ,CAACN,MAAM,CAAC,EAChBC,UACF,CAAC;QAED,IAAIQ,YAAY,EAAE;UAChB/C,SAAS,CAAC6C,IAAI,CAACE,YAAY,CAAC;QAC9B;;QAEA;QACAhB,cAAc,GAAG,EAAE;MACrB;IACF;IAEA,OAAO/B,SAAS;EAClB;EAEA2C,qBAAqBA,CAACH,OAAO,EAAEJ,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEW,OAAO,EAAE;IAC5D,MAAMjD,SAAS,GAAG,EAAE;;IAEpB;IACA,MAAMkD,kBAAkB,GAAG,8EAA8E;IACzG,IAAIhB,KAAK;IAET,OAAO,CAACA,KAAK,GAAGgB,kBAAkB,CAACC,IAAI,CAACX,OAAO,CAAC,MAAM,IAAI,EAAE;MAC1D,MAAM,GAAGM,WAAW,EAAEP,UAAU,EAAEa,eAAe,CAAC,GAAGlB,KAAK;MAE1D,MAAMa,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CI,eAAe,EACfR,QAAQ,CAACE,WAAW,CAAC,EACrBR,MAAM,EACNC,UAAU,EACVU,OACF,CAAC;MAED,IAAIF,YAAY,EAAE;QAChB/C,SAAS,CAAC6C,IAAI,CAACE,YAAY,CAAC;MAC9B;IACF;IAEA,OAAO/C,SAAS;EAClB;EAEAgD,mBAAmBA,CAACR,OAAO,EAAEM,WAAW,EAAER,MAAM,EAAEC,UAAU,EAAEU,OAAO,GAAG,EAAE,EAAE;IAC1E,IAAI,CAACT,OAAO,CAACZ,IAAI,CAAC,CAAC,EAAE,OAAO,IAAI;;IAEhC;IACA,MAAMyB,KAAK,GAAGb,OAAO,CAACV,KAAK,CAAC,IAAI,CAAC,CAACb,GAAG,CAACqC,IAAI,IAAIA,IAAI,CAAC1B,IAAI,CAAC,CAAC,CAAC,CAAC2B,MAAM,CAACD,IAAI,IAAIA,IAAI,CAAC;IAE/E,IAAIE,YAAY,GAAG,EAAE;IACrB,MAAMC,OAAO,GAAG,CAAC,CAAC;IAClB,IAAIC,aAAa,GAAG,IAAI;IACxB,IAAIC,UAAU,GAAG,EAAE;IAEnB,KAAK,MAAML,IAAI,IAAID,KAAK,EAAE;MACxB;MACA,MAAMO,WAAW,GAAGN,IAAI,CAACpB,KAAK,CAAC,mBAAmB,CAAC;MAEnD,IAAI0B,WAAW,EAAE;QACf;QACA,IAAIF,aAAa,EAAE;UACjBD,OAAO,CAACC,aAAa,CAAC,GAAGC,UAAU,CAAC/B,IAAI,CAAC,CAAC;QAC5C;QAEA8B,aAAa,GAAGE,WAAW,CAAC,CAAC,CAAC;QAC9BD,UAAU,GAAGC,WAAW,CAAC,CAAC,CAAC;MAC7B,CAAC,MAAM,IAAIF,aAAa,EAAE;QACxB;QACAC,UAAU,IAAI,GAAG,GAAGL,IAAI;MAC1B,CAAC,MAAM;QACL;QACAE,YAAY,IAAI,GAAG,GAAGF,IAAI;MAC5B;IACF;;IAEA;IACA,IAAII,aAAa,EAAE;MACjBD,OAAO,CAACC,aAAa,CAAC,GAAGC,UAAU,CAAC/B,IAAI,CAAC,CAAC;IAC5C;IAEA,IAAI,CAAC4B,YAAY,CAAC5B,IAAI,CAAC,CAAC,IAAIiC,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,CAACM,MAAM,KAAK,CAAC,EAAE;MAC7D,OAAO,IAAI;IACb;IAEA,OAAO;MACLC,cAAc,EAAElB,WAAW;MAC3BmB,cAAc,EAAE3B,MAAM;MACtBC,UAAU,EAAEA,UAAU;MACtBU,OAAO,EAAEA,OAAO;MAChBO,YAAY,EAAEA,YAAY,CAAC5B,IAAI,CAAC,CAAC;MACjC6B,OAAO,EAAEA,OAAO;MAChBS,eAAe,EAAEC,OAAO,CAAClB,OAAO;IAClC,CAAC;EACH;EAEAxB,cAAcA,CAACC,IAAI,EAAE;IACnB,MAAMzB,OAAO,GAAG,CAAC,CAAC;;IAElB;IACAyB,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC;;IAEvC;IACA,MAAMC,cAAc,GAAGH,IAAI,CAACI,KAAK,CAAC,iBAAiB,CAAC;IAEpD,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,cAAc,CAACkC,MAAM,EAAEtD,CAAC,IAAI,CAAC,EAAE;MACjD,IAAIA,CAAC,GAAG,CAAC,GAAGoB,cAAc,CAACkC,MAAM,EAAE;QACjC,MAAMjB,WAAW,GAAGF,QAAQ,CAACf,cAAc,CAACpB,CAAC,CAAC,CAAC;QAC/C,MAAM+B,OAAO,GAAGX,cAAc,CAACpB,CAAC,GAAG,CAAC,CAAC;;QAErC;QACA,IAAI2D,aAAa,GAAG,IAAI;QACxB,MAAMC,cAAc,GAAG,CACrB,gCAAgC,EAChC,4BAA4B,EAC5B,oBAAoB,EACpB,uBAAuB,CACxB;QAED,KAAK,MAAMC,OAAO,IAAID,cAAc,EAAE;UACpC,MAAMnC,KAAK,GAAGM,OAAO,CAACN,KAAK,CAACoC,OAAO,CAAC;UACpC,IAAIpC,KAAK,EAAE;YACTkC,aAAa,GAAGlC,KAAK,CAAC,CAAC,CAAC,CAACqC,WAAW,CAAC,CAAC;YACtC;UACF;QACF;;QAEA;QACA,IAAIC,WAAW,GAAG,EAAE;QACpB,MAAMC,mBAAmB,GAAG,CAC1B,0CAA0C,EAC1C,wCAAwC,CACzC;QAED,KAAK,MAAMH,OAAO,IAAIG,mBAAmB,EAAE;UACzC,MAAMvC,KAAK,GAAGM,OAAO,CAACN,KAAK,CAACoC,OAAO,CAAC;UACpC,IAAIpC,KAAK,EAAE;YACTsC,WAAW,GAAGtC,KAAK,CAAC,CAAC,CAAC,CAACN,IAAI,CAAC,CAAC;YAC7B;UACF;QACF;QAEA,IAAIwC,aAAa,EAAE;UACjBnE,OAAO,CAAC6C,WAAW,CAAC,GAAG;YACrBsB,aAAa,EAAEA,aAAa;YAC5BI,WAAW,EAAEA;UACf,CAAC;QACH;MACF;IACF;IAEA,OAAOvE,OAAO;EAChB;EAEA,MAAMyE,UAAUA,CAACC,YAAY,EAAEC,UAAU,EAAE;IACzC,IAAI;MACF,MAAM,CAAC5E,SAAS,EAAEC,OAAO,CAAC,GAAG,MAAM4E,OAAO,CAACC,GAAG,CAAC,CAC7C,IAAI,CAAC5E,iBAAiB,CAACyE,YAAY,CAAC,EACpC,IAAI,CAACnD,eAAe,CAACoD,UAAU,CAAC,CACjC,CAAC;MAEF,OAAO;QAAE5E,SAAS;QAAEC;MAAQ,CAAC;IAC/B,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF;AACF;AAEA,eAAexB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}