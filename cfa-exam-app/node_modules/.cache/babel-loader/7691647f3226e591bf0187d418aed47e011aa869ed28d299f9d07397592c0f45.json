{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { useEffect, useMemo, useRef, useCallback } from 'react';\nimport classNames from 'classnames';\nimport useTimeout from '@restart/hooks/useTimeout';\nimport ToastFade from './ToastFade';\nimport ToastHeader from './ToastHeader';\nimport ToastBody from './ToastBody';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport ToastContext from './ToastContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Toast = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  transition: Transition = ToastFade,\n  show = true,\n  animation = true,\n  delay = 5000,\n  autohide = false,\n  onClose,\n  onEntered,\n  onExit,\n  onExiting,\n  onEnter,\n  onEntering,\n  onExited,\n  bg,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'toast');\n\n  // We use refs for these, because we don't want to restart the autohide\n  // timer in case these values change.\n  const delayRef = useRef(delay);\n  const onCloseRef = useRef(onClose);\n  useEffect(() => {\n    delayRef.current = delay;\n    onCloseRef.current = onClose;\n  }, [delay, onClose]);\n  const autohideTimeout = useTimeout();\n  const autohideToast = !!(autohide && show);\n  const autohideFunc = useCallback(() => {\n    if (autohideToast) {\n      onCloseRef.current == null || onCloseRef.current();\n    }\n  }, [autohideToast]);\n  useEffect(() => {\n    // Only reset timer if show or autohide changes.\n    autohideTimeout.set(autohideFunc, delayRef.current);\n  }, [autohideTimeout, autohideFunc]);\n  const toastContext = useMemo(() => ({\n    onClose\n  }), [onClose]);\n  const hasAnimation = !!(Transition && animation);\n  const toast = /*#__PURE__*/_jsx(\"div\", {\n    ...props,\n    ref: ref,\n    className: classNames(bsPrefix, className, bg && `bg-${bg}`, !hasAnimation && (show ? 'show' : 'hide')),\n    role: \"alert\",\n    \"aria-live\": \"assertive\",\n    \"aria-atomic\": \"true\"\n  });\n  return /*#__PURE__*/_jsx(ToastContext.Provider, {\n    value: toastContext,\n    children: hasAnimation && Transition ? /*#__PURE__*/_jsx(Transition, {\n      in: show,\n      onEnter: onEnter,\n      onEntering: onEntering,\n      onEntered: onEntered,\n      onExit: onExit,\n      onExiting: onExiting,\n      onExited: onExited,\n      unmountOnExit: true,\n      children: toast\n    }) : toast\n  });\n});\nToast.displayName = 'Toast';\nexport default Object.assign(Toast, {\n  Body: ToastBody,\n  Header: ToastHeader\n});", "map": {"version": 3, "names": ["React", "useEffect", "useMemo", "useRef", "useCallback", "classNames", "useTimeout", "ToastFade", "ToastHeader", "ToastBody", "useBootstrapPrefix", "ToastContext", "jsx", "_jsx", "Toast", "forwardRef", "bsPrefix", "className", "transition", "Transition", "show", "animation", "delay", "autohide", "onClose", "onEntered", "onExit", "onExiting", "onEnter", "onEntering", "onExited", "bg", "props", "ref", "delayRef", "onCloseRef", "current", "autohideTimeout", "autohideToast", "autohideFunc", "set", "toastContext", "hasAnimation", "toast", "role", "Provider", "value", "children", "in", "unmountOnExit", "displayName", "Object", "assign", "Body", "Header"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/react-bootstrap/esm/Toast.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { useEffect, useMemo, useRef, useCallback } from 'react';\nimport classNames from 'classnames';\nimport useTimeout from '@restart/hooks/useTimeout';\nimport ToastFade from './ToastFade';\nimport ToastHeader from './ToastHeader';\nimport ToastBody from './ToastBody';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport ToastContext from './ToastContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Toast = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  transition: Transition = ToastFade,\n  show = true,\n  animation = true,\n  delay = 5000,\n  autohide = false,\n  onClose,\n  onEntered,\n  onExit,\n  onExiting,\n  onEnter,\n  onEntering,\n  onExited,\n  bg,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'toast');\n\n  // We use refs for these, because we don't want to restart the autohide\n  // timer in case these values change.\n  const delayRef = useRef(delay);\n  const onCloseRef = useRef(onClose);\n  useEffect(() => {\n    delayRef.current = delay;\n    onCloseRef.current = onClose;\n  }, [delay, onClose]);\n  const autohideTimeout = useTimeout();\n  const autohideToast = !!(autohide && show);\n  const autohideFunc = useCallback(() => {\n    if (autohideToast) {\n      onCloseRef.current == null || onCloseRef.current();\n    }\n  }, [autohideToast]);\n  useEffect(() => {\n    // Only reset timer if show or autohide changes.\n    autohideTimeout.set(autohideFunc, delayRef.current);\n  }, [autohideTimeout, autohideFunc]);\n  const toastContext = useMemo(() => ({\n    onClose\n  }), [onClose]);\n  const hasAnimation = !!(Transition && animation);\n  const toast = /*#__PURE__*/_jsx(\"div\", {\n    ...props,\n    ref: ref,\n    className: classNames(bsPrefix, className, bg && `bg-${bg}`, !hasAnimation && (show ? 'show' : 'hide')),\n    role: \"alert\",\n    \"aria-live\": \"assertive\",\n    \"aria-atomic\": \"true\"\n  });\n  return /*#__PURE__*/_jsx(ToastContext.Provider, {\n    value: toastContext,\n    children: hasAnimation && Transition ? /*#__PURE__*/_jsx(Transition, {\n      in: show,\n      onEnter: onEnter,\n      onEntering: onEntering,\n      onEntered: onEntered,\n      onExit: onExit,\n      onExiting: onExiting,\n      onExited: onExited,\n      unmountOnExit: true,\n      children: toast\n    }) : toast\n  });\n});\nToast.displayName = 'Toast';\nexport default Object.assign(Toast, {\n  Body: ToastBody,\n  Header: ToastHeader\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC/D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,KAAK,GAAG,aAAad,KAAK,CAACe,UAAU,CAAC,CAAC;EAC3CC,QAAQ;EACRC,SAAS;EACTC,UAAU,EAAEC,UAAU,GAAGZ,SAAS;EAClCa,IAAI,GAAG,IAAI;EACXC,SAAS,GAAG,IAAI;EAChBC,KAAK,GAAG,IAAI;EACZC,QAAQ,GAAG,KAAK;EAChBC,OAAO;EACPC,SAAS;EACTC,MAAM;EACNC,SAAS;EACTC,OAAO;EACPC,UAAU;EACVC,QAAQ;EACRC,EAAE;EACF,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTjB,QAAQ,GAAGN,kBAAkB,CAACM,QAAQ,EAAE,OAAO,CAAC;;EAEhD;EACA;EACA,MAAMkB,QAAQ,GAAG/B,MAAM,CAACmB,KAAK,CAAC;EAC9B,MAAMa,UAAU,GAAGhC,MAAM,CAACqB,OAAO,CAAC;EAClCvB,SAAS,CAAC,MAAM;IACdiC,QAAQ,CAACE,OAAO,GAAGd,KAAK;IACxBa,UAAU,CAACC,OAAO,GAAGZ,OAAO;EAC9B,CAAC,EAAE,CAACF,KAAK,EAAEE,OAAO,CAAC,CAAC;EACpB,MAAMa,eAAe,GAAG/B,UAAU,CAAC,CAAC;EACpC,MAAMgC,aAAa,GAAG,CAAC,EAAEf,QAAQ,IAAIH,IAAI,CAAC;EAC1C,MAAMmB,YAAY,GAAGnC,WAAW,CAAC,MAAM;IACrC,IAAIkC,aAAa,EAAE;MACjBH,UAAU,CAACC,OAAO,IAAI,IAAI,IAAID,UAAU,CAACC,OAAO,CAAC,CAAC;IACpD;EACF,CAAC,EAAE,CAACE,aAAa,CAAC,CAAC;EACnBrC,SAAS,CAAC,MAAM;IACd;IACAoC,eAAe,CAACG,GAAG,CAACD,YAAY,EAAEL,QAAQ,CAACE,OAAO,CAAC;EACrD,CAAC,EAAE,CAACC,eAAe,EAAEE,YAAY,CAAC,CAAC;EACnC,MAAME,YAAY,GAAGvC,OAAO,CAAC,OAAO;IAClCsB;EACF,CAAC,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACd,MAAMkB,YAAY,GAAG,CAAC,EAAEvB,UAAU,IAAIE,SAAS,CAAC;EAChD,MAAMsB,KAAK,GAAG,aAAa9B,IAAI,CAAC,KAAK,EAAE;IACrC,GAAGmB,KAAK;IACRC,GAAG,EAAEA,GAAG;IACRhB,SAAS,EAAEZ,UAAU,CAACW,QAAQ,EAAEC,SAAS,EAAEc,EAAE,IAAI,MAAMA,EAAE,EAAE,EAAE,CAACW,YAAY,KAAKtB,IAAI,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC;IACvGwB,IAAI,EAAE,OAAO;IACb,WAAW,EAAE,WAAW;IACxB,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,OAAO,aAAa/B,IAAI,CAACF,YAAY,CAACkC,QAAQ,EAAE;IAC9CC,KAAK,EAAEL,YAAY;IACnBM,QAAQ,EAAEL,YAAY,IAAIvB,UAAU,GAAG,aAAaN,IAAI,CAACM,UAAU,EAAE;MACnE6B,EAAE,EAAE5B,IAAI;MACRQ,OAAO,EAAEA,OAAO;MAChBC,UAAU,EAAEA,UAAU;MACtBJ,SAAS,EAAEA,SAAS;MACpBC,MAAM,EAAEA,MAAM;MACdC,SAAS,EAAEA,SAAS;MACpBG,QAAQ,EAAEA,QAAQ;MAClBmB,aAAa,EAAE,IAAI;MACnBF,QAAQ,EAAEJ;IACZ,CAAC,CAAC,GAAGA;EACP,CAAC,CAAC;AACJ,CAAC,CAAC;AACF7B,KAAK,CAACoC,WAAW,GAAG,OAAO;AAC3B,eAAeC,MAAM,CAACC,MAAM,CAACtC,KAAK,EAAE;EAClCuC,IAAI,EAAE5C,SAAS;EACf6C,MAAM,EAAE9C;AACV,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}