{"ast": null, "code": "import { useState } from 'react';\n\n/**\n * A convenience hook around `useState` designed to be paired with\n * the component [callback ref](https://reactjs.org/docs/refs-and-the-dom.html#callback-refs) api.\n * Callback refs are useful over `useRef()` when you need to respond to the ref being set\n * instead of lazily accessing it in an effect.\n *\n * ```ts\n * const [element, attachRef] = useCallbackRef<HTMLDivElement>()\n *\n * useEffect(() => {\n *   if (!element) return\n *\n *   const calendar = new FullCalendar.Calendar(element)\n *\n *   return () => {\n *     calendar.destroy()\n *   }\n * }, [element])\n *\n * return <div ref={attachRef} />\n * ```\n *\n * @category refs\n */\nexport default function useCallbackRef() {\n  return useState(null);\n}", "map": {"version": 3, "names": ["useState", "useCallbackRef"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/@restart/ui/node_modules/@restart/hooks/esm/useCallbackRef.js"], "sourcesContent": ["import { useState } from 'react';\n\n/**\n * A convenience hook around `useState` designed to be paired with\n * the component [callback ref](https://reactjs.org/docs/refs-and-the-dom.html#callback-refs) api.\n * Callback refs are useful over `useRef()` when you need to respond to the ref being set\n * instead of lazily accessing it in an effect.\n *\n * ```ts\n * const [element, attachRef] = useCallbackRef<HTMLDivElement>()\n *\n * useEffect(() => {\n *   if (!element) return\n *\n *   const calendar = new FullCalendar.Calendar(element)\n *\n *   return () => {\n *     calendar.destroy()\n *   }\n * }, [element])\n *\n * return <div ref={attachRef} />\n * ```\n *\n * @category refs\n */\nexport default function useCallbackRef() {\n  return useState(null);\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAAA,EAAG;EACvC,OAAOD,QAAQ,CAAC,IAAI,CAAC;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}