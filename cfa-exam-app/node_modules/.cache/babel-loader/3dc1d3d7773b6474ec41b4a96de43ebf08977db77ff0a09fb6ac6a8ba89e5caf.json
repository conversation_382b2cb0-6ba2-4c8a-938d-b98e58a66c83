{"ast": null, "code": "import useMediaQuery from './useMediaQuery';\nimport { useMemo } from 'react';\n/**\n * Create a responsive hook we a set of breakpoint names and widths.\n * You can use any valid css units as well as a numbers (for pixels).\n *\n * **NOTE:** The object key order is important! it's assumed to be in order from smallest to largest\n *\n * ```ts\n * const useBreakpoint = createBreakpointHook({\n *  xs: 0,\n *  sm: 576,\n *  md: 768,\n *  lg: 992,\n *  xl: 1200,\n * })\n * ```\n *\n * **Watch out!** using string values will sometimes construct media queries using css `calc()` which\n * is NOT supported in media queries by all browsers at the moment. use numbers for\n * the widest range of browser support.\n *\n * @param breakpointValues A object hash of names to breakpoint dimensions\n */\nexport function createBreakpointHook(breakpointValues) {\n  const names = Object.keys(breakpointValues);\n  function and(query, next) {\n    if (query === next) {\n      return next;\n    }\n    return query ? `${query} and ${next}` : next;\n  }\n  function getNext(breakpoint) {\n    return names[Math.min(names.indexOf(breakpoint) + 1, names.length - 1)];\n  }\n  function getMaxQuery(breakpoint) {\n    const next = getNext(breakpoint);\n    let value = breakpointValues[next];\n    if (typeof value === 'number') value = `${value - 0.2}px`;else value = `calc(${value} - 0.2px)`;\n    return `(max-width: ${value})`;\n  }\n  function getMinQuery(breakpoint) {\n    let value = breakpointValues[breakpoint];\n    if (typeof value === 'number') {\n      value = `${value}px`;\n    }\n    return `(min-width: ${value})`;\n  }\n\n  /**\n   * Match a set of breakpoints\n   *\n   * ```tsx\n   * const MidSizeOnly = () => {\n   *   const isMid = useBreakpoint({ lg: 'down', sm: 'up' });\n   *\n   *   if (isMid) return <div>On a Reasonable sized Screen!</div>\n   *   return null;\n   * }\n   * ```\n   * @param breakpointMap An object map of breakpoints and directions, queries are constructed using \"and\" to join\n   * breakpoints together\n   * @param window Optionally specify the target window to match against (useful when rendering into iframes)\n   */\n\n  /**\n   * Match a single breakpoint exactly, up, or down.\n   *\n   * ```tsx\n   * const PhoneOnly = () => {\n   *   const isSmall = useBreakpoint('sm', 'down');\n   *\n   *   if (isSmall) return <div>On a Small Screen!</div>\n   *   return null;\n   * }\n   * ```\n   *\n   * @param breakpoint The breakpoint key\n   * @param direction A direction 'up' for a max, 'down' for min, true to match only the breakpoint\n   * @param window Optionally specify the target window to match against (useful when rendering into iframes)\n   */\n\n  function useBreakpoint(breakpointOrMap, direction, window) {\n    let breakpointMap;\n    if (typeof breakpointOrMap === 'object') {\n      breakpointMap = breakpointOrMap;\n      window = direction;\n      direction = true;\n    } else {\n      direction = direction || true;\n      breakpointMap = {\n        [breakpointOrMap]: direction\n      };\n    }\n    let query = useMemo(() => Object.entries(breakpointMap).reduce((query, [key, direction]) => {\n      if (direction === 'up' || direction === true) {\n        query = and(query, getMinQuery(key));\n      }\n      if (direction === 'down' || direction === true) {\n        query = and(query, getMaxQuery(key));\n      }\n      return query;\n    }, ''), [JSON.stringify(breakpointMap)]);\n    return useMediaQuery(query, window);\n  }\n  return useBreakpoint;\n}\nconst useBreakpoint = createBreakpointHook({\n  xs: 0,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1400\n});\nexport default useBreakpoint;", "map": {"version": 3, "names": ["useMediaQuery", "useMemo", "createBreakpointHook", "breakpoint<PERSON><PERSON><PERSON>", "names", "Object", "keys", "and", "query", "next", "getNext", "breakpoint", "Math", "min", "indexOf", "length", "getMaxQuery", "value", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "useBreakpoint", "breakpointOrMap", "direction", "window", "breakpointMap", "entries", "reduce", "key", "JSON", "stringify", "xs", "sm", "md", "lg", "xl", "xxl"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/@restart/hooks/esm/useBreakpoint.js"], "sourcesContent": ["import useMediaQuery from './useMediaQuery';\nimport { useMemo } from 'react';\n/**\n * Create a responsive hook we a set of breakpoint names and widths.\n * You can use any valid css units as well as a numbers (for pixels).\n *\n * **NOTE:** The object key order is important! it's assumed to be in order from smallest to largest\n *\n * ```ts\n * const useBreakpoint = createBreakpointHook({\n *  xs: 0,\n *  sm: 576,\n *  md: 768,\n *  lg: 992,\n *  xl: 1200,\n * })\n * ```\n *\n * **Watch out!** using string values will sometimes construct media queries using css `calc()` which\n * is NOT supported in media queries by all browsers at the moment. use numbers for\n * the widest range of browser support.\n *\n * @param breakpointValues A object hash of names to breakpoint dimensions\n */\nexport function createBreakpointHook(breakpointValues) {\n  const names = Object.keys(breakpointValues);\n  function and(query, next) {\n    if (query === next) {\n      return next;\n    }\n    return query ? `${query} and ${next}` : next;\n  }\n  function getNext(breakpoint) {\n    return names[Math.min(names.indexOf(breakpoint) + 1, names.length - 1)];\n  }\n  function getMaxQuery(breakpoint) {\n    const next = getNext(breakpoint);\n    let value = breakpointValues[next];\n    if (typeof value === 'number') value = `${value - 0.2}px`;else value = `calc(${value} - 0.2px)`;\n    return `(max-width: ${value})`;\n  }\n  function getMinQuery(breakpoint) {\n    let value = breakpointValues[breakpoint];\n    if (typeof value === 'number') {\n      value = `${value}px`;\n    }\n    return `(min-width: ${value})`;\n  }\n\n  /**\n   * Match a set of breakpoints\n   *\n   * ```tsx\n   * const MidSizeOnly = () => {\n   *   const isMid = useBreakpoint({ lg: 'down', sm: 'up' });\n   *\n   *   if (isMid) return <div>On a Reasonable sized Screen!</div>\n   *   return null;\n   * }\n   * ```\n   * @param breakpointMap An object map of breakpoints and directions, queries are constructed using \"and\" to join\n   * breakpoints together\n   * @param window Optionally specify the target window to match against (useful when rendering into iframes)\n   */\n\n  /**\n   * Match a single breakpoint exactly, up, or down.\n   *\n   * ```tsx\n   * const PhoneOnly = () => {\n   *   const isSmall = useBreakpoint('sm', 'down');\n   *\n   *   if (isSmall) return <div>On a Small Screen!</div>\n   *   return null;\n   * }\n   * ```\n   *\n   * @param breakpoint The breakpoint key\n   * @param direction A direction 'up' for a max, 'down' for min, true to match only the breakpoint\n   * @param window Optionally specify the target window to match against (useful when rendering into iframes)\n   */\n\n  function useBreakpoint(breakpointOrMap, direction, window) {\n    let breakpointMap;\n    if (typeof breakpointOrMap === 'object') {\n      breakpointMap = breakpointOrMap;\n      window = direction;\n      direction = true;\n    } else {\n      direction = direction || true;\n      breakpointMap = {\n        [breakpointOrMap]: direction\n      };\n    }\n    let query = useMemo(() => Object.entries(breakpointMap).reduce((query, [key, direction]) => {\n      if (direction === 'up' || direction === true) {\n        query = and(query, getMinQuery(key));\n      }\n      if (direction === 'down' || direction === true) {\n        query = and(query, getMaxQuery(key));\n      }\n      return query;\n    }, ''), [JSON.stringify(breakpointMap)]);\n    return useMediaQuery(query, window);\n  }\n  return useBreakpoint;\n}\nconst useBreakpoint = createBreakpointHook({\n  xs: 0,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1400\n});\nexport default useBreakpoint;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,SAASC,OAAO,QAAQ,OAAO;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,gBAAgB,EAAE;EACrD,MAAMC,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACH,gBAAgB,CAAC;EAC3C,SAASI,GAAGA,CAACC,KAAK,EAAEC,IAAI,EAAE;IACxB,IAAID,KAAK,KAAKC,IAAI,EAAE;MAClB,OAAOA,IAAI;IACb;IACA,OAAOD,KAAK,GAAG,GAAGA,KAAK,QAAQC,IAAI,EAAE,GAAGA,IAAI;EAC9C;EACA,SAASC,OAAOA,CAACC,UAAU,EAAE;IAC3B,OAAOP,KAAK,CAACQ,IAAI,CAACC,GAAG,CAACT,KAAK,CAACU,OAAO,CAACH,UAAU,CAAC,GAAG,CAAC,EAAEP,KAAK,CAACW,MAAM,GAAG,CAAC,CAAC,CAAC;EACzE;EACA,SAASC,WAAWA,CAACL,UAAU,EAAE;IAC/B,MAAMF,IAAI,GAAGC,OAAO,CAACC,UAAU,CAAC;IAChC,IAAIM,KAAK,GAAGd,gBAAgB,CAACM,IAAI,CAAC;IAClC,IAAI,OAAOQ,KAAK,KAAK,QAAQ,EAAEA,KAAK,GAAG,GAAGA,KAAK,GAAG,GAAG,IAAI,CAAC,KAAKA,KAAK,GAAG,QAAQA,KAAK,WAAW;IAC/F,OAAO,eAAeA,KAAK,GAAG;EAChC;EACA,SAASC,WAAWA,CAACP,UAAU,EAAE;IAC/B,IAAIM,KAAK,GAAGd,gBAAgB,CAACQ,UAAU,CAAC;IACxC,IAAI,OAAOM,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,GAAG,GAAGA,KAAK,IAAI;IACtB;IACA,OAAO,eAAeA,KAAK,GAAG;EAChC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,SAASE,aAAaA,CAACC,eAAe,EAAEC,SAAS,EAAEC,MAAM,EAAE;IACzD,IAAIC,aAAa;IACjB,IAAI,OAAOH,eAAe,KAAK,QAAQ,EAAE;MACvCG,aAAa,GAAGH,eAAe;MAC/BE,MAAM,GAAGD,SAAS;MAClBA,SAAS,GAAG,IAAI;IAClB,CAAC,MAAM;MACLA,SAAS,GAAGA,SAAS,IAAI,IAAI;MAC7BE,aAAa,GAAG;QACd,CAACH,eAAe,GAAGC;MACrB,CAAC;IACH;IACA,IAAIb,KAAK,GAAGP,OAAO,CAAC,MAAMI,MAAM,CAACmB,OAAO,CAACD,aAAa,CAAC,CAACE,MAAM,CAAC,CAACjB,KAAK,EAAE,CAACkB,GAAG,EAAEL,SAAS,CAAC,KAAK;MAC1F,IAAIA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,IAAI,EAAE;QAC5Cb,KAAK,GAAGD,GAAG,CAACC,KAAK,EAAEU,WAAW,CAACQ,GAAG,CAAC,CAAC;MACtC;MACA,IAAIL,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,IAAI,EAAE;QAC9Cb,KAAK,GAAGD,GAAG,CAACC,KAAK,EAAEQ,WAAW,CAACU,GAAG,CAAC,CAAC;MACtC;MACA,OAAOlB,KAAK;IACd,CAAC,EAAE,EAAE,CAAC,EAAE,CAACmB,IAAI,CAACC,SAAS,CAACL,aAAa,CAAC,CAAC,CAAC;IACxC,OAAOvB,aAAa,CAACQ,KAAK,EAAEc,MAAM,CAAC;EACrC;EACA,OAAOH,aAAa;AACtB;AACA,MAAMA,aAAa,GAAGjB,oBAAoB,CAAC;EACzC2B,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,IAAI;EACRC,GAAG,EAAE;AACP,CAAC,CAAC;AACF,eAAef,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}