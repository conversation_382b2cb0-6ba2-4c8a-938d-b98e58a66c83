{"ast": null, "code": "// PDF Parser for CFA Questions and Answers\nimport * as pdfjsLib from 'pdfjs-dist';\n\n// Set worker path - use the worker from the same version\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;\nclass CFAQuestionParser {\n  constructor() {\n    this.questions = [];\n    this.answers = {};\n  }\n  async parseQuestionFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n        fullText += pageText + '\\n\\n';\n      }\n\n      // Debug: Log extracted text\n      console.log('Extracted text length:', fullText.length);\n      console.log('First 2000 characters:', fullText.substring(0, 2000));\n\n      // Parse questions from text\n      const questions = this.extractQuestions(fullText);\n      console.log('Extracted questions:', questions.length);\n      return questions;\n    } catch (error) {\n      console.error('Error parsing question file:', error);\n      throw error;\n    }\n  }\n  async parseAnswerFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n        fullText += pageText + '\\n\\n';\n      }\n      console.log('Answer file text length:', fullText.length);\n      console.log('Answer file preview:', fullText.substring(0, 1000));\n\n      // Parse answers from text\n      const answers = this.extractAnswers(fullText);\n      console.log('Extracted answers:', Object.keys(answers).length);\n      return answers;\n    } catch (error) {\n      console.error('Error parsing answer file:', error);\n      throw error;\n    }\n  }\n  extractQuestions(text) {\n    const questions = [];\n    console.log('Looking for question patterns in text...');\n    console.log('Text length:', text.length);\n\n    // Try simpler approach - split by \"Question #\" and process each block\n    const questionBlocks = text.split(/(?=Question #\\d+)/);\n    console.log(`Split into ${questionBlocks.length} blocks`);\n    for (let i = 0; i < questionBlocks.length; i++) {\n      const block = questionBlocks[i].trim();\n      if (!block || block.length < 20) continue;\n      console.log(`\\n--- Processing block ${i} ---`);\n      console.log('Block start:', block.substring(0, 100));\n\n      // Try to extract question info from block\n      const patterns = [/Question #(\\d+) of (\\d+) Question ID: (\\d+)/, /Question #(\\d+) - (\\d+) of (\\d+) Question ID: (\\d+)/, /Question #(\\d+) of (\\d+)/, /Question #(\\d+)/];\n      let questionInfo = null;\n      for (const pattern of patterns) {\n        const match = block.match(pattern);\n        if (match) {\n          questionInfo = {\n            questionNum: parseInt(match[1]),\n            endNum: match[2] && !isNaN(parseInt(match[2])) ? parseInt(match[2]) : null,\n            totalQ: match[3] && !isNaN(parseInt(match[3])) ? parseInt(match[3]) : 100,\n            questionId: match[4] || 'unknown',\n            isGroup: match[0].includes(' - '),\n            fullMatch: match[0]\n          };\n          break;\n        }\n      }\n      if (!questionInfo) {\n        console.log('No question pattern found in block');\n        continue;\n      }\n      console.log('Found question info:', questionInfo);\n\n      // Extract content after the question header\n      const headerIndex = block.indexOf(questionInfo.fullMatch);\n      const content = block.substring(headerIndex + questionInfo.fullMatch.length).trim();\n      if (questionInfo.isGroup) {\n        // Handle group questions - extract context and individual questions\n        console.log(`Processing group questions ${questionInfo.questionNum}-${questionInfo.endNum}`);\n\n        // For group questions, try to find individual question markers within the content\n        const groupQuestions = this.extractGroupQuestions(content, questionInfo.questionNum, questionInfo.endNum, questionInfo.totalQ, questionInfo.questionId);\n        if (groupQuestions.length > 0) {\n          questions.push(...groupQuestions);\n        } else {\n          // Fallback: treat as single question\n          const questionData = this.parseSingleQuestion(content, questionInfo.questionNum, questionInfo.totalQ, questionInfo.questionId);\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      } else {\n        // Handle single question\n        console.log(`Processing single question ${questionInfo.questionNum}`);\n        const questionData = this.parseSingleQuestion(content, questionInfo.questionNum, questionInfo.totalQ, questionInfo.questionId);\n        if (questionData) {\n          questions.push(questionData);\n          console.log(`Successfully parsed question ${questionInfo.questionNum}`);\n        } else {\n          console.log(`Failed to parse question ${questionInfo.questionNum}`);\n        }\n      }\n    }\n    console.log(`\\n=== EXTRACTION COMPLETE ===`);\n    console.log(`Total questions extracted: ${questions.length}`);\n    console.log('Question numbers:', questions.map(q => q.questionNumber).sort((a, b) => a - b));\n    return questions;\n  }\n  extractQuestionsAlternative(text) {\n    console.log('Using alternative extraction method...');\n    const questions = [];\n\n    // Look for any numbered patterns that might be questions\n    const patterns = [/(\\d+)\\.\\s+([^0-9]+?)(?=\\d+\\.|$)/gs,\n    // 1. Question text\n    /Question\\s+(\\d+)[:\\.]?\\s*([^Q]+?)(?=Question\\s+\\d+|$)/gis // Question 1: text\n    ];\n    for (const pattern of patterns) {\n      const matches = [...text.matchAll(pattern)];\n      console.log(`Alternative pattern found ${matches.length} matches`);\n      for (const match of matches) {\n        const questionNum = parseInt(match[1]);\n        const content = match[2].trim();\n        if (content.length > 20) {\n          // Filter out very short matches\n          const questionData = this.parseSingleQuestion(content, questionNum, 100,\n          // Default total\n          'alt-' + questionNum);\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n      if (questions.length > 0) break; // Use first successful pattern\n    }\n    return questions;\n  }\n  extractGroupQuestions(content, startQ, endQ, totalQ, questionId) {\n    const questions = [];\n    console.log(`Extracting group questions ${startQ}-${endQ || startQ}`);\n    console.log('Group content preview:', content.substring(0, 200));\n\n    // First, try to extract context (text before any question-specific content)\n    let context = '';\n    const contextMatch = content.match(/^(.*?)(?=[A-E]\\)|Question|$)/s);\n    if (contextMatch && contextMatch[1].trim().length > 50) {\n      context = contextMatch[1].trim();\n      console.log('Extracted context:', context.substring(0, 100));\n    }\n\n    // If this is a range (e.g., Question #6-11), try to find individual questions\n    if (endQ && endQ > startQ) {\n      console.log(`Looking for individual questions ${startQ} to ${endQ}`);\n\n      // Try to split content by question numbers\n      for (let qNum = startQ; qNum <= endQ; qNum++) {\n        // Look for patterns like \"6.\", \"Question 6\", etc.\n        const patterns = [new RegExp(`${qNum}\\\\.(.*?)(?=${qNum + 1}\\\\.|$)`, 's'), new RegExp(`Question\\\\s+${qNum}[:\\\\.]?(.*?)(?=Question\\\\s+${qNum + 1}|$)`, 'is')];\n        let questionContent = '';\n        for (const pattern of patterns) {\n          const match = content.match(pattern);\n          if (match) {\n            questionContent = match[1].trim();\n            break;\n          }\n        }\n        if (questionContent) {\n          console.log(`Found content for question ${qNum}`);\n          const questionData = this.parseSingleQuestion(questionContent, qNum, totalQ, `${questionId}-${qNum}`, context);\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n    }\n\n    // If no individual questions found, treat the whole content as one question\n    if (questions.length === 0) {\n      console.log('No individual questions found, treating as single question with context');\n      const questionData = this.parseSingleQuestion(content, startQ, totalQ, questionId, context);\n      if (questionData) {\n        questions.push(questionData);\n      }\n    }\n    console.log(`Extracted ${questions.length} questions from group`);\n    return questions;\n  }\n  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {\n    if (!content.trim()) return null;\n    console.log(`Parsing question ${questionNum}, content length: ${content.length}`);\n    console.log(`Content preview: ${content.substring(0, 200)}...`);\n\n    // Preserve line breaks for better parsing\n    const originalContent = content;\n    let questionText = '';\n    const choices = {};\n\n    // Find all choice markers in the content\n    const choiceMarkers = [];\n    const choicePattern = /([A-E])\\)\\s*/g;\n    let match;\n    while ((match = choicePattern.exec(content)) !== null) {\n      choiceMarkers.push({\n        choice: match[1],\n        index: match.index,\n        fullMatch: match[0]\n      });\n    }\n    console.log(`Found ${choiceMarkers.length} choice markers for question ${questionNum}`);\n    if (choiceMarkers.length >= 2) {\n      // Extract question text (everything before first choice)\n      questionText = content.substring(0, choiceMarkers[0].index).trim();\n\n      // Extract each choice\n      for (let i = 0; i < choiceMarkers.length; i++) {\n        const marker = choiceMarkers[i];\n        const nextMarker = choiceMarkers[i + 1];\n        const startIndex = marker.index + marker.fullMatch.length;\n        const endIndex = nextMarker ? nextMarker.index : content.length;\n        const choiceText = content.substring(startIndex, endIndex).trim();\n        if (choiceText.length > 0) {\n          choices[marker.choice] = choiceText;\n        }\n      }\n    } else {\n      // Fallback: try to parse line by line\n      console.log('Using line-by-line parsing for question', questionNum);\n      const lines = originalContent.split('\\n').map(line => line.trim()).filter(line => line);\n      let currentChoice = null;\n      let choiceText = '';\n      let inChoices = false;\n      for (const line of lines) {\n        // Check if line starts with choice pattern\n        const choiceMatch = line.match(/^([A-E])\\)\\s*(.*)/);\n        if (choiceMatch) {\n          // Save previous choice\n          if (currentChoice) {\n            choices[currentChoice] = choiceText.trim();\n          }\n          currentChoice = choiceMatch[1];\n          choiceText = choiceMatch[2];\n          inChoices = true;\n        } else if (currentChoice && inChoices) {\n          // Continue choice text\n          choiceText += ' ' + line;\n        } else if (!inChoices) {\n          // Question text\n          questionText += ' ' + line;\n        }\n      }\n\n      // Save last choice\n      if (currentChoice) {\n        choices[currentChoice] = choiceText.trim();\n      }\n    }\n\n    // Clean up question text\n    questionText = questionText.replace(/\\s+/g, ' ').trim();\n\n    // Clean up choices\n    Object.keys(choices).forEach(key => {\n      choices[key] = choices[key].replace(/\\s+/g, ' ').trim();\n    });\n    console.log(`Question ${questionNum} parsed:`, {\n      questionTextLength: questionText.length,\n      choicesCount: Object.keys(choices).length,\n      choices: Object.keys(choices)\n    });\n\n    // Validate we have minimum required content\n    if (!questionText.trim() && Object.keys(choices).length === 0) {\n      console.log(`Question ${questionNum}: No content found`);\n      return null;\n    }\n\n    // Ensure we have at least some choices\n    if (Object.keys(choices).length === 0) {\n      console.log(`Question ${questionNum}: No choices found, creating placeholders`);\n      choices['A'] = 'Choice A (parsing failed)';\n      choices['B'] = 'Choice B (parsing failed)';\n      choices['C'] = 'Choice C (parsing failed)';\n    }\n\n    // Ensure we have question text\n    if (!questionText.trim()) {\n      questionText = `Question ${questionNum} (content parsing incomplete)`;\n    }\n    return {\n      questionNumber: questionNum,\n      totalQuestions: totalQ,\n      questionId: questionId,\n      context: context,\n      questionText: questionText,\n      choices: choices,\n      isGroupQuestion: Boolean(context)\n    };\n  }\n  extractAnswers(text) {\n    const answers = {};\n    console.log('Extracting answers from text...');\n\n    // Find all question markers in answer file\n    const questionMarkers = [];\n    const patterns = [/Question #(\\d+) of (\\d+) Question ID: (\\d+)/g, /Question #(\\d+)/g];\n    for (const pattern of patterns) {\n      let match;\n      while ((match = pattern.exec(text)) !== null) {\n        questionMarkers.push({\n          index: match.index,\n          fullMatch: match[0],\n          questionNum: parseInt(match[1])\n        });\n      }\n    }\n    console.log(`Found ${questionMarkers.length} question markers in answer file`);\n\n    // Sort by position\n    questionMarkers.sort((a, b) => a.index - b.index);\n\n    // Extract answer content for each question\n    for (let i = 0; i < questionMarkers.length; i++) {\n      const marker = questionMarkers[i];\n      const nextMarker = questionMarkers[i + 1];\n\n      // Get content from current marker to next marker (or end of text)\n      const startIndex = marker.index + marker.fullMatch.length;\n      const endIndex = nextMarker ? nextMarker.index : text.length;\n      const content = text.substring(startIndex, endIndex).trim();\n      console.log(`Processing answer for question ${marker.questionNum}, content length: ${content.length}`);\n\n      // Find correct answer\n      let correctAnswer = null;\n      const answerPatterns = [/The correct answer is ([A-E])\\./i, /The correct answer is ([A-E])\\s/i, /Correct answer:\\s*([A-E])/i, /Answer:\\s*([A-E])/i, /([A-E])\\s*is correct/i, /Choice\\s*([A-E])\\s*is correct/i];\n      for (const pattern of answerPatterns) {\n        const match = content.match(pattern);\n        if (match) {\n          correctAnswer = match[1].toUpperCase();\n          console.log(`Found correct answer ${correctAnswer} for question ${marker.questionNum}`);\n          break;\n        }\n      }\n\n      // Find explanation - be more flexible\n      let explanation = '';\n      const explanationPatterns = [/Explanation[:\\s]+(.*?)(?=Question #|\\(Module|\\Z)/is, /Explanation[:\\s]+(.*?)(?=\\n\\s*\\n|\\Z)/is, /Explanation[:\\s]+(.*)/is];\n      for (const pattern of explanationPatterns) {\n        const match = content.match(pattern);\n        if (match) {\n          explanation = match[1].trim();\n          // Clean up explanation\n          explanation = explanation.replace(/\\s+/g, ' ');\n          // Remove common suffixes\n          explanation = explanation.replace(/\\(Module.*?\\)$/i, '');\n          explanation = explanation.replace(/\\(LOS.*?\\)$/i, '');\n          explanation = explanation.trim();\n          console.log(`Found explanation for question ${marker.questionNum}, length: ${explanation.length}`);\n          break;\n        }\n      }\n\n      // If no explanation found, try to extract everything after \"Explanation\"\n      if (!explanation && content.toLowerCase().includes('explanation')) {\n        const explIndex = content.toLowerCase().indexOf('explanation');\n        if (explIndex !== -1) {\n          explanation = content.substring(explIndex + 11).trim();\n          explanation = explanation.replace(/\\s+/g, ' ');\n          console.log(`Fallback explanation extraction for question ${marker.questionNum}`);\n        }\n      }\n      if (correctAnswer) {\n        answers[marker.questionNum] = {\n          correctAnswer: correctAnswer,\n          explanation: explanation || 'No explanation available'\n        };\n      } else {\n        console.log(`No correct answer found for question ${marker.questionNum}`);\n      }\n    }\n    console.log(`Extracted answers for ${Object.keys(answers).length} questions`);\n    return answers;\n  }\n  async parseFiles(questionFile, answerFile) {\n    try {\n      const [questions, answers] = await Promise.all([this.parseQuestionFile(questionFile), this.parseAnswerFile(answerFile)]);\n      return {\n        questions,\n        answers\n      };\n    } catch (error) {\n      console.error('Error parsing files:', error);\n      throw error;\n    }\n  }\n}\nexport default CFAQuestionParser;", "map": {"version": 3, "names": ["pdfjsLib", "GlobalWorkerOptions", "workerSrc", "CFAQuestionParser", "constructor", "questions", "answers", "parseQuestionFile", "file", "arrayBuffer", "pdf", "getDocument", "promise", "fullText", "i", "numPages", "page", "getPage", "textContent", "getTextContent", "pageText", "lastY", "item", "items", "Math", "abs", "transform", "str", "console", "log", "length", "substring", "extractQuestions", "error", "parseAnswerFile", "extractAnswers", "Object", "keys", "text", "questionBlocks", "split", "block", "trim", "patterns", "questionInfo", "pattern", "match", "questionNum", "parseInt", "endNum", "isNaN", "totalQ", "questionId", "isGroup", "includes", "fullMatch", "headerIndex", "indexOf", "content", "groupQuestions", "extractGroupQuestions", "push", "questionData", "parseSingleQuestion", "map", "q", "questionNumber", "sort", "a", "b", "extractQuestionsAlternative", "matches", "matchAll", "startQ", "endQ", "context", "contextMatch", "qNum", "RegExp", "questionContent", "originalContent", "questionText", "choices", "choiceMarkers", "choicePattern", "exec", "choice", "index", "marker", "nextM<PERSON><PERSON>", "startIndex", "endIndex", "choiceText", "lines", "line", "filter", "currentChoice", "inChoices", "choiceMatch", "replace", "for<PERSON>ach", "key", "questionTextLength", "choicesCount", "totalQuestions", "isGroupQuestion", "Boolean", "questionMarkers", "<PERSON><PERSON><PERSON><PERSON>", "answerPatterns", "toUpperCase", "explanation", "explanationPatterns", "toLowerCase", "explIndex", "parseFiles", "questionFile", "answerFile", "Promise", "all"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/utils/pdfParser.js"], "sourcesContent": ["// PDF Parser for CFA Questions and Answers\nimport * as pdfjsLib from 'pdfjs-dist';\n\n// Set worker path - use the worker from the same version\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;\n\nclass CFAQuestionParser {\n  constructor() {\n    this.questions = [];\n    this.answers = {};\n  }\n\n  async parseQuestionFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n\n        fullText += pageText + '\\n\\n';\n      }\n\n      // Debug: Log extracted text\n      console.log('Extracted text length:', fullText.length);\n      console.log('First 2000 characters:', fullText.substring(0, 2000));\n\n      // Parse questions from text\n      const questions = this.extractQuestions(fullText);\n      console.log('Extracted questions:', questions.length);\n\n      return questions;\n    } catch (error) {\n      console.error('Error parsing question file:', error);\n      throw error;\n    }\n  }\n\n  async parseAnswerFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n\n        fullText += pageText + '\\n\\n';\n      }\n\n      console.log('Answer file text length:', fullText.length);\n      console.log('Answer file preview:', fullText.substring(0, 1000));\n\n      // Parse answers from text\n      const answers = this.extractAnswers(fullText);\n      console.log('Extracted answers:', Object.keys(answers).length);\n\n      return answers;\n    } catch (error) {\n      console.error('Error parsing answer file:', error);\n      throw error;\n    }\n  }\n\n  extractQuestions(text) {\n    const questions = [];\n\n    console.log('Looking for question patterns in text...');\n    console.log('Text length:', text.length);\n\n    // Try simpler approach - split by \"Question #\" and process each block\n    const questionBlocks = text.split(/(?=Question #\\d+)/);\n    console.log(`Split into ${questionBlocks.length} blocks`);\n\n    for (let i = 0; i < questionBlocks.length; i++) {\n      const block = questionBlocks[i].trim();\n      if (!block || block.length < 20) continue;\n\n      console.log(`\\n--- Processing block ${i} ---`);\n      console.log('Block start:', block.substring(0, 100));\n\n      // Try to extract question info from block\n      const patterns = [\n        /Question #(\\d+) of (\\d+) Question ID: (\\d+)/,\n        /Question #(\\d+) - (\\d+) of (\\d+) Question ID: (\\d+)/,\n        /Question #(\\d+) of (\\d+)/,\n        /Question #(\\d+)/\n      ];\n\n      let questionInfo = null;\n      for (const pattern of patterns) {\n        const match = block.match(pattern);\n        if (match) {\n          questionInfo = {\n            questionNum: parseInt(match[1]),\n            endNum: match[2] && !isNaN(parseInt(match[2])) ? parseInt(match[2]) : null,\n            totalQ: match[3] && !isNaN(parseInt(match[3])) ? parseInt(match[3]) : 100,\n            questionId: match[4] || 'unknown',\n            isGroup: match[0].includes(' - '),\n            fullMatch: match[0]\n          };\n          break;\n        }\n      }\n\n      if (!questionInfo) {\n        console.log('No question pattern found in block');\n        continue;\n      }\n\n      console.log('Found question info:', questionInfo);\n\n      // Extract content after the question header\n      const headerIndex = block.indexOf(questionInfo.fullMatch);\n      const content = block.substring(headerIndex + questionInfo.fullMatch.length).trim();\n\n      if (questionInfo.isGroup) {\n        // Handle group questions - extract context and individual questions\n        console.log(`Processing group questions ${questionInfo.questionNum}-${questionInfo.endNum}`);\n\n        // For group questions, try to find individual question markers within the content\n        const groupQuestions = this.extractGroupQuestions(\n          content,\n          questionInfo.questionNum,\n          questionInfo.endNum,\n          questionInfo.totalQ,\n          questionInfo.questionId\n        );\n\n        if (groupQuestions.length > 0) {\n          questions.push(...groupQuestions);\n        } else {\n          // Fallback: treat as single question\n          const questionData = this.parseSingleQuestion(\n            content,\n            questionInfo.questionNum,\n            questionInfo.totalQ,\n            questionInfo.questionId\n          );\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      } else {\n        // Handle single question\n        console.log(`Processing single question ${questionInfo.questionNum}`);\n\n        const questionData = this.parseSingleQuestion(\n          content,\n          questionInfo.questionNum,\n          questionInfo.totalQ,\n          questionInfo.questionId\n        );\n\n        if (questionData) {\n          questions.push(questionData);\n          console.log(`Successfully parsed question ${questionInfo.questionNum}`);\n        } else {\n          console.log(`Failed to parse question ${questionInfo.questionNum}`);\n        }\n      }\n    }\n\n    console.log(`\\n=== EXTRACTION COMPLETE ===`);\n    console.log(`Total questions extracted: ${questions.length}`);\n    console.log('Question numbers:', questions.map(q => q.questionNumber).sort((a, b) => a - b));\n\n    return questions;\n  }\n\n  extractQuestionsAlternative(text) {\n    console.log('Using alternative extraction method...');\n    const questions = [];\n\n    // Look for any numbered patterns that might be questions\n    const patterns = [\n      /(\\d+)\\.\\s+([^0-9]+?)(?=\\d+\\.|$)/gs,  // 1. Question text\n      /Question\\s+(\\d+)[:\\.]?\\s*([^Q]+?)(?=Question\\s+\\d+|$)/gis,  // Question 1: text\n    ];\n\n    for (const pattern of patterns) {\n      const matches = [...text.matchAll(pattern)];\n      console.log(`Alternative pattern found ${matches.length} matches`);\n\n      for (const match of matches) {\n        const questionNum = parseInt(match[1]);\n        const content = match[2].trim();\n\n        if (content.length > 20) { // Filter out very short matches\n          const questionData = this.parseSingleQuestion(\n            content,\n            questionNum,\n            100, // Default total\n            'alt-' + questionNum\n          );\n\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n\n      if (questions.length > 0) break; // Use first successful pattern\n    }\n\n    return questions;\n  }\n\n  extractGroupQuestions(content, startQ, endQ, totalQ, questionId) {\n    const questions = [];\n\n    console.log(`Extracting group questions ${startQ}-${endQ || startQ}`);\n    console.log('Group content preview:', content.substring(0, 200));\n\n    // First, try to extract context (text before any question-specific content)\n    let context = '';\n    const contextMatch = content.match(/^(.*?)(?=[A-E]\\)|Question|$)/s);\n    if (contextMatch && contextMatch[1].trim().length > 50) {\n      context = contextMatch[1].trim();\n      console.log('Extracted context:', context.substring(0, 100));\n    }\n\n    // If this is a range (e.g., Question #6-11), try to find individual questions\n    if (endQ && endQ > startQ) {\n      console.log(`Looking for individual questions ${startQ} to ${endQ}`);\n\n      // Try to split content by question numbers\n      for (let qNum = startQ; qNum <= endQ; qNum++) {\n        // Look for patterns like \"6.\", \"Question 6\", etc.\n        const patterns = [\n          new RegExp(`${qNum}\\\\.(.*?)(?=${qNum + 1}\\\\.|$)`, 's'),\n          new RegExp(`Question\\\\s+${qNum}[:\\\\.]?(.*?)(?=Question\\\\s+${qNum + 1}|$)`, 'is')\n        ];\n\n        let questionContent = '';\n        for (const pattern of patterns) {\n          const match = content.match(pattern);\n          if (match) {\n            questionContent = match[1].trim();\n            break;\n          }\n        }\n\n        if (questionContent) {\n          console.log(`Found content for question ${qNum}`);\n          const questionData = this.parseSingleQuestion(\n            questionContent,\n            qNum,\n            totalQ,\n            `${questionId}-${qNum}`,\n            context\n          );\n\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n    }\n\n    // If no individual questions found, treat the whole content as one question\n    if (questions.length === 0) {\n      console.log('No individual questions found, treating as single question with context');\n      const questionData = this.parseSingleQuestion(\n        content,\n        startQ,\n        totalQ,\n        questionId,\n        context\n      );\n\n      if (questionData) {\n        questions.push(questionData);\n      }\n    }\n\n    console.log(`Extracted ${questions.length} questions from group`);\n    return questions;\n  }\n\n  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {\n    if (!content.trim()) return null;\n\n    console.log(`Parsing question ${questionNum}, content length: ${content.length}`);\n    console.log(`Content preview: ${content.substring(0, 200)}...`);\n\n    // Preserve line breaks for better parsing\n    const originalContent = content;\n\n    let questionText = '';\n    const choices = {};\n\n    // Find all choice markers in the content\n    const choiceMarkers = [];\n    const choicePattern = /([A-E])\\)\\s*/g;\n    let match;\n\n    while ((match = choicePattern.exec(content)) !== null) {\n      choiceMarkers.push({\n        choice: match[1],\n        index: match.index,\n        fullMatch: match[0]\n      });\n    }\n\n    console.log(`Found ${choiceMarkers.length} choice markers for question ${questionNum}`);\n\n    if (choiceMarkers.length >= 2) {\n      // Extract question text (everything before first choice)\n      questionText = content.substring(0, choiceMarkers[0].index).trim();\n\n      // Extract each choice\n      for (let i = 0; i < choiceMarkers.length; i++) {\n        const marker = choiceMarkers[i];\n        const nextMarker = choiceMarkers[i + 1];\n\n        const startIndex = marker.index + marker.fullMatch.length;\n        const endIndex = nextMarker ? nextMarker.index : content.length;\n        const choiceText = content.substring(startIndex, endIndex).trim();\n\n        if (choiceText.length > 0) {\n          choices[marker.choice] = choiceText;\n        }\n      }\n    } else {\n      // Fallback: try to parse line by line\n      console.log('Using line-by-line parsing for question', questionNum);\n\n      const lines = originalContent.split('\\n').map(line => line.trim()).filter(line => line);\n\n      let currentChoice = null;\n      let choiceText = '';\n      let inChoices = false;\n\n      for (const line of lines) {\n        // Check if line starts with choice pattern\n        const choiceMatch = line.match(/^([A-E])\\)\\s*(.*)/);\n\n        if (choiceMatch) {\n          // Save previous choice\n          if (currentChoice) {\n            choices[currentChoice] = choiceText.trim();\n          }\n\n          currentChoice = choiceMatch[1];\n          choiceText = choiceMatch[2];\n          inChoices = true;\n        } else if (currentChoice && inChoices) {\n          // Continue choice text\n          choiceText += ' ' + line;\n        } else if (!inChoices) {\n          // Question text\n          questionText += ' ' + line;\n        }\n      }\n\n      // Save last choice\n      if (currentChoice) {\n        choices[currentChoice] = choiceText.trim();\n      }\n    }\n\n    // Clean up question text\n    questionText = questionText.replace(/\\s+/g, ' ').trim();\n\n    // Clean up choices\n    Object.keys(choices).forEach(key => {\n      choices[key] = choices[key].replace(/\\s+/g, ' ').trim();\n    });\n\n    console.log(`Question ${questionNum} parsed:`, {\n      questionTextLength: questionText.length,\n      choicesCount: Object.keys(choices).length,\n      choices: Object.keys(choices)\n    });\n\n    // Validate we have minimum required content\n    if (!questionText.trim() && Object.keys(choices).length === 0) {\n      console.log(`Question ${questionNum}: No content found`);\n      return null;\n    }\n\n    // Ensure we have at least some choices\n    if (Object.keys(choices).length === 0) {\n      console.log(`Question ${questionNum}: No choices found, creating placeholders`);\n      choices['A'] = 'Choice A (parsing failed)';\n      choices['B'] = 'Choice B (parsing failed)';\n      choices['C'] = 'Choice C (parsing failed)';\n    }\n\n    // Ensure we have question text\n    if (!questionText.trim()) {\n      questionText = `Question ${questionNum} (content parsing incomplete)`;\n    }\n\n    return {\n      questionNumber: questionNum,\n      totalQuestions: totalQ,\n      questionId: questionId,\n      context: context,\n      questionText: questionText,\n      choices: choices,\n      isGroupQuestion: Boolean(context)\n    };\n  }\n\n  extractAnswers(text) {\n    const answers = {};\n\n    console.log('Extracting answers from text...');\n\n    // Find all question markers in answer file\n    const questionMarkers = [];\n    const patterns = [\n      /Question #(\\d+) of (\\d+) Question ID: (\\d+)/g,\n      /Question #(\\d+)/g\n    ];\n\n    for (const pattern of patterns) {\n      let match;\n      while ((match = pattern.exec(text)) !== null) {\n        questionMarkers.push({\n          index: match.index,\n          fullMatch: match[0],\n          questionNum: parseInt(match[1])\n        });\n      }\n    }\n\n    console.log(`Found ${questionMarkers.length} question markers in answer file`);\n\n    // Sort by position\n    questionMarkers.sort((a, b) => a.index - b.index);\n\n    // Extract answer content for each question\n    for (let i = 0; i < questionMarkers.length; i++) {\n      const marker = questionMarkers[i];\n      const nextMarker = questionMarkers[i + 1];\n\n      // Get content from current marker to next marker (or end of text)\n      const startIndex = marker.index + marker.fullMatch.length;\n      const endIndex = nextMarker ? nextMarker.index : text.length;\n      const content = text.substring(startIndex, endIndex).trim();\n\n      console.log(`Processing answer for question ${marker.questionNum}, content length: ${content.length}`);\n\n      // Find correct answer\n      let correctAnswer = null;\n      const answerPatterns = [\n        /The correct answer is ([A-E])\\./i,\n        /The correct answer is ([A-E])\\s/i,\n        /Correct answer:\\s*([A-E])/i,\n        /Answer:\\s*([A-E])/i,\n        /([A-E])\\s*is correct/i,\n        /Choice\\s*([A-E])\\s*is correct/i\n      ];\n\n      for (const pattern of answerPatterns) {\n        const match = content.match(pattern);\n        if (match) {\n          correctAnswer = match[1].toUpperCase();\n          console.log(`Found correct answer ${correctAnswer} for question ${marker.questionNum}`);\n          break;\n        }\n      }\n\n      // Find explanation - be more flexible\n      let explanation = '';\n      const explanationPatterns = [\n        /Explanation[:\\s]+(.*?)(?=Question #|\\(Module|\\Z)/is,\n        /Explanation[:\\s]+(.*?)(?=\\n\\s*\\n|\\Z)/is,\n        /Explanation[:\\s]+(.*)/is\n      ];\n\n      for (const pattern of explanationPatterns) {\n        const match = content.match(pattern);\n        if (match) {\n          explanation = match[1].trim();\n          // Clean up explanation\n          explanation = explanation.replace(/\\s+/g, ' ');\n          // Remove common suffixes\n          explanation = explanation.replace(/\\(Module.*?\\)$/i, '');\n          explanation = explanation.replace(/\\(LOS.*?\\)$/i, '');\n          explanation = explanation.trim();\n\n          console.log(`Found explanation for question ${marker.questionNum}, length: ${explanation.length}`);\n          break;\n        }\n      }\n\n      // If no explanation found, try to extract everything after \"Explanation\"\n      if (!explanation && content.toLowerCase().includes('explanation')) {\n        const explIndex = content.toLowerCase().indexOf('explanation');\n        if (explIndex !== -1) {\n          explanation = content.substring(explIndex + 11).trim();\n          explanation = explanation.replace(/\\s+/g, ' ');\n          console.log(`Fallback explanation extraction for question ${marker.questionNum}`);\n        }\n      }\n\n      if (correctAnswer) {\n        answers[marker.questionNum] = {\n          correctAnswer: correctAnswer,\n          explanation: explanation || 'No explanation available'\n        };\n      } else {\n        console.log(`No correct answer found for question ${marker.questionNum}`);\n      }\n    }\n\n    console.log(`Extracted answers for ${Object.keys(answers).length} questions`);\n    return answers;\n  }\n\n  async parseFiles(questionFile, answerFile) {\n    try {\n      const [questions, answers] = await Promise.all([\n        this.parseQuestionFile(questionFile),\n        this.parseAnswerFile(answerFile)\n      ]);\n      \n      return { questions, answers };\n    } catch (error) {\n      console.error('Error parsing files:', error);\n      throw error;\n    }\n  }\n}\n\nexport default CFAQuestionParser;\n"], "mappings": "AAAA;AACA,OAAO,KAAKA,QAAQ,MAAM,YAAY;;AAEtC;AACAA,QAAQ,CAACC,mBAAmB,CAACC,SAAS,GAAG,+DAA+D;AAExG,MAAMC,iBAAiB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;EACnB;EAEA,MAAMC,iBAAiBA,CAACC,IAAI,EAAE;IAC5B,IAAI;MACF,MAAMC,WAAW,GAAG,MAAMD,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5C,MAAMC,GAAG,GAAG,MAAMV,QAAQ,CAACW,WAAW,CAACF,WAAW,CAAC,CAACG,OAAO;MAE3D,IAAIC,QAAQ,GAAG,EAAE;;MAEjB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,GAAG,CAACK,QAAQ,EAAED,CAAC,EAAE,EAAE;QACtC,MAAME,IAAI,GAAG,MAAMN,GAAG,CAACO,OAAO,CAACH,CAAC,CAAC;QACjC,MAAMI,WAAW,GAAG,MAAMF,IAAI,CAACG,cAAc,CAAC,CAAC;;QAE/C;QACA,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAIC,KAAK,GAAG,IAAI;QAEhB,KAAK,MAAMC,IAAI,IAAIJ,WAAW,CAACK,KAAK,EAAE;UACpC;UACA,IAAIF,KAAK,KAAK,IAAI,IAAIG,IAAI,CAACC,GAAG,CAACH,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,GAAGL,KAAK,CAAC,GAAG,CAAC,EAAE;YAC7DD,QAAQ,IAAI,IAAI;UAClB;UAEAA,QAAQ,IAAIE,IAAI,CAACK,GAAG,GAAG,GAAG;UAC1BN,KAAK,GAAGC,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC;QAC3B;QAEAb,QAAQ,IAAIO,QAAQ,GAAG,MAAM;MAC/B;;MAEA;MACAQ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEhB,QAAQ,CAACiB,MAAM,CAAC;MACtDF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEhB,QAAQ,CAACkB,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;;MAElE;MACA,MAAM1B,SAAS,GAAG,IAAI,CAAC2B,gBAAgB,CAACnB,QAAQ,CAAC;MACjDe,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAExB,SAAS,CAACyB,MAAM,CAAC;MAErD,OAAOzB,SAAS;IAClB,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;EAEA,MAAMC,eAAeA,CAAC1B,IAAI,EAAE;IAC1B,IAAI;MACF,MAAMC,WAAW,GAAG,MAAMD,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5C,MAAMC,GAAG,GAAG,MAAMV,QAAQ,CAACW,WAAW,CAACF,WAAW,CAAC,CAACG,OAAO;MAE3D,IAAIC,QAAQ,GAAG,EAAE;;MAEjB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,GAAG,CAACK,QAAQ,EAAED,CAAC,EAAE,EAAE;QACtC,MAAME,IAAI,GAAG,MAAMN,GAAG,CAACO,OAAO,CAACH,CAAC,CAAC;QACjC,MAAMI,WAAW,GAAG,MAAMF,IAAI,CAACG,cAAc,CAAC,CAAC;;QAE/C;QACA,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAIC,KAAK,GAAG,IAAI;QAEhB,KAAK,MAAMC,IAAI,IAAIJ,WAAW,CAACK,KAAK,EAAE;UACpC;UACA,IAAIF,KAAK,KAAK,IAAI,IAAIG,IAAI,CAACC,GAAG,CAACH,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,GAAGL,KAAK,CAAC,GAAG,CAAC,EAAE;YAC7DD,QAAQ,IAAI,IAAI;UAClB;UAEAA,QAAQ,IAAIE,IAAI,CAACK,GAAG,GAAG,GAAG;UAC1BN,KAAK,GAAGC,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC;QAC3B;QAEAb,QAAQ,IAAIO,QAAQ,GAAG,MAAM;MAC/B;MAEAQ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEhB,QAAQ,CAACiB,MAAM,CAAC;MACxDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEhB,QAAQ,CAACkB,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;;MAEhE;MACA,MAAMzB,OAAO,GAAG,IAAI,CAAC6B,cAAc,CAACtB,QAAQ,CAAC;MAC7Ce,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,MAAM,CAACC,IAAI,CAAC/B,OAAO,CAAC,CAACwB,MAAM,CAAC;MAE9D,OAAOxB,OAAO;IAChB,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF;EAEAD,gBAAgBA,CAACM,IAAI,EAAE;IACrB,MAAMjC,SAAS,GAAG,EAAE;IAEpBuB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvDD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAES,IAAI,CAACR,MAAM,CAAC;;IAExC;IACA,MAAMS,cAAc,GAAGD,IAAI,CAACE,KAAK,CAAC,mBAAmB,CAAC;IACtDZ,OAAO,CAACC,GAAG,CAAC,cAAcU,cAAc,CAACT,MAAM,SAAS,CAAC;IAEzD,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,cAAc,CAACT,MAAM,EAAEhB,CAAC,EAAE,EAAE;MAC9C,MAAM2B,KAAK,GAAGF,cAAc,CAACzB,CAAC,CAAC,CAAC4B,IAAI,CAAC,CAAC;MACtC,IAAI,CAACD,KAAK,IAAIA,KAAK,CAACX,MAAM,GAAG,EAAE,EAAE;MAEjCF,OAAO,CAACC,GAAG,CAAC,0BAA0Bf,CAAC,MAAM,CAAC;MAC9Cc,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEY,KAAK,CAACV,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;;MAEpD;MACA,MAAMY,QAAQ,GAAG,CACf,6CAA6C,EAC7C,qDAAqD,EACrD,0BAA0B,EAC1B,iBAAiB,CAClB;MAED,IAAIC,YAAY,GAAG,IAAI;MACvB,KAAK,MAAMC,OAAO,IAAIF,QAAQ,EAAE;QAC9B,MAAMG,KAAK,GAAGL,KAAK,CAACK,KAAK,CAACD,OAAO,CAAC;QAClC,IAAIC,KAAK,EAAE;UACTF,YAAY,GAAG;YACbG,WAAW,EAAEC,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/BG,MAAM,EAAEH,KAAK,CAAC,CAAC,CAAC,IAAI,CAACI,KAAK,CAACF,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;YAC1EK,MAAM,EAAEL,KAAK,CAAC,CAAC,CAAC,IAAI,CAACI,KAAK,CAACF,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;YACzEM,UAAU,EAAEN,KAAK,CAAC,CAAC,CAAC,IAAI,SAAS;YACjCO,OAAO,EAAEP,KAAK,CAAC,CAAC,CAAC,CAACQ,QAAQ,CAAC,KAAK,CAAC;YACjCC,SAAS,EAAET,KAAK,CAAC,CAAC;UACpB,CAAC;UACD;QACF;MACF;MAEA,IAAI,CAACF,YAAY,EAAE;QACjBhB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD;MACF;MAEAD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEe,YAAY,CAAC;;MAEjD;MACA,MAAMY,WAAW,GAAGf,KAAK,CAACgB,OAAO,CAACb,YAAY,CAACW,SAAS,CAAC;MACzD,MAAMG,OAAO,GAAGjB,KAAK,CAACV,SAAS,CAACyB,WAAW,GAAGZ,YAAY,CAACW,SAAS,CAACzB,MAAM,CAAC,CAACY,IAAI,CAAC,CAAC;MAEnF,IAAIE,YAAY,CAACS,OAAO,EAAE;QACxB;QACAzB,OAAO,CAACC,GAAG,CAAC,8BAA8Be,YAAY,CAACG,WAAW,IAAIH,YAAY,CAACK,MAAM,EAAE,CAAC;;QAE5F;QACA,MAAMU,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAC/CF,OAAO,EACPd,YAAY,CAACG,WAAW,EACxBH,YAAY,CAACK,MAAM,EACnBL,YAAY,CAACO,MAAM,EACnBP,YAAY,CAACQ,UACf,CAAC;QAED,IAAIO,cAAc,CAAC7B,MAAM,GAAG,CAAC,EAAE;UAC7BzB,SAAS,CAACwD,IAAI,CAAC,GAAGF,cAAc,CAAC;QACnC,CAAC,MAAM;UACL;UACA,MAAMG,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CL,OAAO,EACPd,YAAY,CAACG,WAAW,EACxBH,YAAY,CAACO,MAAM,EACnBP,YAAY,CAACQ,UACf,CAAC;UACD,IAAIU,YAAY,EAAE;YAChBzD,SAAS,CAACwD,IAAI,CAACC,YAAY,CAAC;UAC9B;QACF;MACF,CAAC,MAAM;QACL;QACAlC,OAAO,CAACC,GAAG,CAAC,8BAA8Be,YAAY,CAACG,WAAW,EAAE,CAAC;QAErE,MAAMe,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CL,OAAO,EACPd,YAAY,CAACG,WAAW,EACxBH,YAAY,CAACO,MAAM,EACnBP,YAAY,CAACQ,UACf,CAAC;QAED,IAAIU,YAAY,EAAE;UAChBzD,SAAS,CAACwD,IAAI,CAACC,YAAY,CAAC;UAC5BlC,OAAO,CAACC,GAAG,CAAC,gCAAgCe,YAAY,CAACG,WAAW,EAAE,CAAC;QACzE,CAAC,MAAM;UACLnB,OAAO,CAACC,GAAG,CAAC,4BAA4Be,YAAY,CAACG,WAAW,EAAE,CAAC;QACrE;MACF;IACF;IAEAnB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5CD,OAAO,CAACC,GAAG,CAAC,8BAA8BxB,SAAS,CAACyB,MAAM,EAAE,CAAC;IAC7DF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAExB,SAAS,CAAC2D,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CAAC;IAE5F,OAAOhE,SAAS;EAClB;EAEAiE,2BAA2BA,CAAChC,IAAI,EAAE;IAChCV,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACrD,MAAMxB,SAAS,GAAG,EAAE;;IAEpB;IACA,MAAMsC,QAAQ,GAAG,CACf,mCAAmC;IAAG;IACtC,0DAA0D,CAAG;IAAA,CAC9D;IAED,KAAK,MAAME,OAAO,IAAIF,QAAQ,EAAE;MAC9B,MAAM4B,OAAO,GAAG,CAAC,GAAGjC,IAAI,CAACkC,QAAQ,CAAC3B,OAAO,CAAC,CAAC;MAC3CjB,OAAO,CAACC,GAAG,CAAC,6BAA6B0C,OAAO,CAACzC,MAAM,UAAU,CAAC;MAElE,KAAK,MAAMgB,KAAK,IAAIyB,OAAO,EAAE;QAC3B,MAAMxB,WAAW,GAAGC,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;QACtC,MAAMY,OAAO,GAAGZ,KAAK,CAAC,CAAC,CAAC,CAACJ,IAAI,CAAC,CAAC;QAE/B,IAAIgB,OAAO,CAAC5B,MAAM,GAAG,EAAE,EAAE;UAAE;UACzB,MAAMgC,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CL,OAAO,EACPX,WAAW,EACX,GAAG;UAAE;UACL,MAAM,GAAGA,WACX,CAAC;UAED,IAAIe,YAAY,EAAE;YAChBzD,SAAS,CAACwD,IAAI,CAACC,YAAY,CAAC;UAC9B;QACF;MACF;MAEA,IAAIzD,SAAS,CAACyB,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC;IACnC;IAEA,OAAOzB,SAAS;EAClB;EAEAuD,qBAAqBA,CAACF,OAAO,EAAEe,MAAM,EAAEC,IAAI,EAAEvB,MAAM,EAAEC,UAAU,EAAE;IAC/D,MAAM/C,SAAS,GAAG,EAAE;IAEpBuB,OAAO,CAACC,GAAG,CAAC,8BAA8B4C,MAAM,IAAIC,IAAI,IAAID,MAAM,EAAE,CAAC;IACrE7C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE6B,OAAO,CAAC3B,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;;IAEhE;IACA,IAAI4C,OAAO,GAAG,EAAE;IAChB,MAAMC,YAAY,GAAGlB,OAAO,CAACZ,KAAK,CAAC,+BAA+B,CAAC;IACnE,IAAI8B,YAAY,IAAIA,YAAY,CAAC,CAAC,CAAC,CAAClC,IAAI,CAAC,CAAC,CAACZ,MAAM,GAAG,EAAE,EAAE;MACtD6C,OAAO,GAAGC,YAAY,CAAC,CAAC,CAAC,CAAClC,IAAI,CAAC,CAAC;MAChCd,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE8C,OAAO,CAAC5C,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC9D;;IAEA;IACA,IAAI2C,IAAI,IAAIA,IAAI,GAAGD,MAAM,EAAE;MACzB7C,OAAO,CAACC,GAAG,CAAC,oCAAoC4C,MAAM,OAAOC,IAAI,EAAE,CAAC;;MAEpE;MACA,KAAK,IAAIG,IAAI,GAAGJ,MAAM,EAAEI,IAAI,IAAIH,IAAI,EAAEG,IAAI,EAAE,EAAE;QAC5C;QACA,MAAMlC,QAAQ,GAAG,CACf,IAAImC,MAAM,CAAC,GAAGD,IAAI,cAAcA,IAAI,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EACtD,IAAIC,MAAM,CAAC,eAAeD,IAAI,8BAA8BA,IAAI,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CACjF;QAED,IAAIE,eAAe,GAAG,EAAE;QACxB,KAAK,MAAMlC,OAAO,IAAIF,QAAQ,EAAE;UAC9B,MAAMG,KAAK,GAAGY,OAAO,CAACZ,KAAK,CAACD,OAAO,CAAC;UACpC,IAAIC,KAAK,EAAE;YACTiC,eAAe,GAAGjC,KAAK,CAAC,CAAC,CAAC,CAACJ,IAAI,CAAC,CAAC;YACjC;UACF;QACF;QAEA,IAAIqC,eAAe,EAAE;UACnBnD,OAAO,CAACC,GAAG,CAAC,8BAA8BgD,IAAI,EAAE,CAAC;UACjD,MAAMf,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CgB,eAAe,EACfF,IAAI,EACJ1B,MAAM,EACN,GAAGC,UAAU,IAAIyB,IAAI,EAAE,EACvBF,OACF,CAAC;UAED,IAAIb,YAAY,EAAE;YAChBzD,SAAS,CAACwD,IAAI,CAACC,YAAY,CAAC;UAC9B;QACF;MACF;IACF;;IAEA;IACA,IAAIzD,SAAS,CAACyB,MAAM,KAAK,CAAC,EAAE;MAC1BF,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;MACtF,MAAMiC,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CL,OAAO,EACPe,MAAM,EACNtB,MAAM,EACNC,UAAU,EACVuB,OACF,CAAC;MAED,IAAIb,YAAY,EAAE;QAChBzD,SAAS,CAACwD,IAAI,CAACC,YAAY,CAAC;MAC9B;IACF;IAEAlC,OAAO,CAACC,GAAG,CAAC,aAAaxB,SAAS,CAACyB,MAAM,uBAAuB,CAAC;IACjE,OAAOzB,SAAS;EAClB;EAEA0D,mBAAmBA,CAACL,OAAO,EAAEX,WAAW,EAAEI,MAAM,EAAEC,UAAU,EAAEuB,OAAO,GAAG,EAAE,EAAE;IAC1E,IAAI,CAACjB,OAAO,CAAChB,IAAI,CAAC,CAAC,EAAE,OAAO,IAAI;IAEhCd,OAAO,CAACC,GAAG,CAAC,oBAAoBkB,WAAW,qBAAqBW,OAAO,CAAC5B,MAAM,EAAE,CAAC;IACjFF,OAAO,CAACC,GAAG,CAAC,oBAAoB6B,OAAO,CAAC3B,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;;IAE/D;IACA,MAAMiD,eAAe,GAAGtB,OAAO;IAE/B,IAAIuB,YAAY,GAAG,EAAE;IACrB,MAAMC,OAAO,GAAG,CAAC,CAAC;;IAElB;IACA,MAAMC,aAAa,GAAG,EAAE;IACxB,MAAMC,aAAa,GAAG,eAAe;IACrC,IAAItC,KAAK;IAET,OAAO,CAACA,KAAK,GAAGsC,aAAa,CAACC,IAAI,CAAC3B,OAAO,CAAC,MAAM,IAAI,EAAE;MACrDyB,aAAa,CAACtB,IAAI,CAAC;QACjByB,MAAM,EAAExC,KAAK,CAAC,CAAC,CAAC;QAChByC,KAAK,EAAEzC,KAAK,CAACyC,KAAK;QAClBhC,SAAS,EAAET,KAAK,CAAC,CAAC;MACpB,CAAC,CAAC;IACJ;IAEAlB,OAAO,CAACC,GAAG,CAAC,SAASsD,aAAa,CAACrD,MAAM,gCAAgCiB,WAAW,EAAE,CAAC;IAEvF,IAAIoC,aAAa,CAACrD,MAAM,IAAI,CAAC,EAAE;MAC7B;MACAmD,YAAY,GAAGvB,OAAO,CAAC3B,SAAS,CAAC,CAAC,EAAEoD,aAAa,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,CAAC7C,IAAI,CAAC,CAAC;;MAElE;MACA,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,aAAa,CAACrD,MAAM,EAAEhB,CAAC,EAAE,EAAE;QAC7C,MAAM0E,MAAM,GAAGL,aAAa,CAACrE,CAAC,CAAC;QAC/B,MAAM2E,UAAU,GAAGN,aAAa,CAACrE,CAAC,GAAG,CAAC,CAAC;QAEvC,MAAM4E,UAAU,GAAGF,MAAM,CAACD,KAAK,GAAGC,MAAM,CAACjC,SAAS,CAACzB,MAAM;QACzD,MAAM6D,QAAQ,GAAGF,UAAU,GAAGA,UAAU,CAACF,KAAK,GAAG7B,OAAO,CAAC5B,MAAM;QAC/D,MAAM8D,UAAU,GAAGlC,OAAO,CAAC3B,SAAS,CAAC2D,UAAU,EAAEC,QAAQ,CAAC,CAACjD,IAAI,CAAC,CAAC;QAEjE,IAAIkD,UAAU,CAAC9D,MAAM,GAAG,CAAC,EAAE;UACzBoD,OAAO,CAACM,MAAM,CAACF,MAAM,CAAC,GAAGM,UAAU;QACrC;MACF;IACF,CAAC,MAAM;MACL;MACAhE,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEkB,WAAW,CAAC;MAEnE,MAAM8C,KAAK,GAAGb,eAAe,CAACxC,KAAK,CAAC,IAAI,CAAC,CAACwB,GAAG,CAAC8B,IAAI,IAAIA,IAAI,CAACpD,IAAI,CAAC,CAAC,CAAC,CAACqD,MAAM,CAACD,IAAI,IAAIA,IAAI,CAAC;MAEvF,IAAIE,aAAa,GAAG,IAAI;MACxB,IAAIJ,UAAU,GAAG,EAAE;MACnB,IAAIK,SAAS,GAAG,KAAK;MAErB,KAAK,MAAMH,IAAI,IAAID,KAAK,EAAE;QACxB;QACA,MAAMK,WAAW,GAAGJ,IAAI,CAAChD,KAAK,CAAC,mBAAmB,CAAC;QAEnD,IAAIoD,WAAW,EAAE;UACf;UACA,IAAIF,aAAa,EAAE;YACjBd,OAAO,CAACc,aAAa,CAAC,GAAGJ,UAAU,CAAClD,IAAI,CAAC,CAAC;UAC5C;UAEAsD,aAAa,GAAGE,WAAW,CAAC,CAAC,CAAC;UAC9BN,UAAU,GAAGM,WAAW,CAAC,CAAC,CAAC;UAC3BD,SAAS,GAAG,IAAI;QAClB,CAAC,MAAM,IAAID,aAAa,IAAIC,SAAS,EAAE;UACrC;UACAL,UAAU,IAAI,GAAG,GAAGE,IAAI;QAC1B,CAAC,MAAM,IAAI,CAACG,SAAS,EAAE;UACrB;UACAhB,YAAY,IAAI,GAAG,GAAGa,IAAI;QAC5B;MACF;;MAEA;MACA,IAAIE,aAAa,EAAE;QACjBd,OAAO,CAACc,aAAa,CAAC,GAAGJ,UAAU,CAAClD,IAAI,CAAC,CAAC;MAC5C;IACF;;IAEA;IACAuC,YAAY,GAAGA,YAAY,CAACkB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACzD,IAAI,CAAC,CAAC;;IAEvD;IACAN,MAAM,CAACC,IAAI,CAAC6C,OAAO,CAAC,CAACkB,OAAO,CAACC,GAAG,IAAI;MAClCnB,OAAO,CAACmB,GAAG,CAAC,GAAGnB,OAAO,CAACmB,GAAG,CAAC,CAACF,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACzD,IAAI,CAAC,CAAC;IACzD,CAAC,CAAC;IAEFd,OAAO,CAACC,GAAG,CAAC,YAAYkB,WAAW,UAAU,EAAE;MAC7CuD,kBAAkB,EAAErB,YAAY,CAACnD,MAAM;MACvCyE,YAAY,EAAEnE,MAAM,CAACC,IAAI,CAAC6C,OAAO,CAAC,CAACpD,MAAM;MACzCoD,OAAO,EAAE9C,MAAM,CAACC,IAAI,CAAC6C,OAAO;IAC9B,CAAC,CAAC;;IAEF;IACA,IAAI,CAACD,YAAY,CAACvC,IAAI,CAAC,CAAC,IAAIN,MAAM,CAACC,IAAI,CAAC6C,OAAO,CAAC,CAACpD,MAAM,KAAK,CAAC,EAAE;MAC7DF,OAAO,CAACC,GAAG,CAAC,YAAYkB,WAAW,oBAAoB,CAAC;MACxD,OAAO,IAAI;IACb;;IAEA;IACA,IAAIX,MAAM,CAACC,IAAI,CAAC6C,OAAO,CAAC,CAACpD,MAAM,KAAK,CAAC,EAAE;MACrCF,OAAO,CAACC,GAAG,CAAC,YAAYkB,WAAW,2CAA2C,CAAC;MAC/EmC,OAAO,CAAC,GAAG,CAAC,GAAG,2BAA2B;MAC1CA,OAAO,CAAC,GAAG,CAAC,GAAG,2BAA2B;MAC1CA,OAAO,CAAC,GAAG,CAAC,GAAG,2BAA2B;IAC5C;;IAEA;IACA,IAAI,CAACD,YAAY,CAACvC,IAAI,CAAC,CAAC,EAAE;MACxBuC,YAAY,GAAG,YAAYlC,WAAW,+BAA+B;IACvE;IAEA,OAAO;MACLmB,cAAc,EAAEnB,WAAW;MAC3ByD,cAAc,EAAErD,MAAM;MACtBC,UAAU,EAAEA,UAAU;MACtBuB,OAAO,EAAEA,OAAO;MAChBM,YAAY,EAAEA,YAAY;MAC1BC,OAAO,EAAEA,OAAO;MAChBuB,eAAe,EAAEC,OAAO,CAAC/B,OAAO;IAClC,CAAC;EACH;EAEAxC,cAAcA,CAACG,IAAI,EAAE;IACnB,MAAMhC,OAAO,GAAG,CAAC,CAAC;IAElBsB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;;IAE9C;IACA,MAAM8E,eAAe,GAAG,EAAE;IAC1B,MAAMhE,QAAQ,GAAG,CACf,8CAA8C,EAC9C,kBAAkB,CACnB;IAED,KAAK,MAAME,OAAO,IAAIF,QAAQ,EAAE;MAC9B,IAAIG,KAAK;MACT,OAAO,CAACA,KAAK,GAAGD,OAAO,CAACwC,IAAI,CAAC/C,IAAI,CAAC,MAAM,IAAI,EAAE;QAC5CqE,eAAe,CAAC9C,IAAI,CAAC;UACnB0B,KAAK,EAAEzC,KAAK,CAACyC,KAAK;UAClBhC,SAAS,EAAET,KAAK,CAAC,CAAC,CAAC;UACnBC,WAAW,EAAEC,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC;MACJ;IACF;IAEAlB,OAAO,CAACC,GAAG,CAAC,SAAS8E,eAAe,CAAC7E,MAAM,kCAAkC,CAAC;;IAE9E;IACA6E,eAAe,CAACxC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACmB,KAAK,GAAGlB,CAAC,CAACkB,KAAK,CAAC;;IAEjD;IACA,KAAK,IAAIzE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6F,eAAe,CAAC7E,MAAM,EAAEhB,CAAC,EAAE,EAAE;MAC/C,MAAM0E,MAAM,GAAGmB,eAAe,CAAC7F,CAAC,CAAC;MACjC,MAAM2E,UAAU,GAAGkB,eAAe,CAAC7F,CAAC,GAAG,CAAC,CAAC;;MAEzC;MACA,MAAM4E,UAAU,GAAGF,MAAM,CAACD,KAAK,GAAGC,MAAM,CAACjC,SAAS,CAACzB,MAAM;MACzD,MAAM6D,QAAQ,GAAGF,UAAU,GAAGA,UAAU,CAACF,KAAK,GAAGjD,IAAI,CAACR,MAAM;MAC5D,MAAM4B,OAAO,GAAGpB,IAAI,CAACP,SAAS,CAAC2D,UAAU,EAAEC,QAAQ,CAAC,CAACjD,IAAI,CAAC,CAAC;MAE3Dd,OAAO,CAACC,GAAG,CAAC,kCAAkC2D,MAAM,CAACzC,WAAW,qBAAqBW,OAAO,CAAC5B,MAAM,EAAE,CAAC;;MAEtG;MACA,IAAI8E,aAAa,GAAG,IAAI;MACxB,MAAMC,cAAc,GAAG,CACrB,kCAAkC,EAClC,kCAAkC,EAClC,4BAA4B,EAC5B,oBAAoB,EACpB,uBAAuB,EACvB,gCAAgC,CACjC;MAED,KAAK,MAAMhE,OAAO,IAAIgE,cAAc,EAAE;QACpC,MAAM/D,KAAK,GAAGY,OAAO,CAACZ,KAAK,CAACD,OAAO,CAAC;QACpC,IAAIC,KAAK,EAAE;UACT8D,aAAa,GAAG9D,KAAK,CAAC,CAAC,CAAC,CAACgE,WAAW,CAAC,CAAC;UACtClF,OAAO,CAACC,GAAG,CAAC,wBAAwB+E,aAAa,iBAAiBpB,MAAM,CAACzC,WAAW,EAAE,CAAC;UACvF;QACF;MACF;;MAEA;MACA,IAAIgE,WAAW,GAAG,EAAE;MACpB,MAAMC,mBAAmB,GAAG,CAC1B,oDAAoD,EACpD,wCAAwC,EACxC,yBAAyB,CAC1B;MAED,KAAK,MAAMnE,OAAO,IAAImE,mBAAmB,EAAE;QACzC,MAAMlE,KAAK,GAAGY,OAAO,CAACZ,KAAK,CAACD,OAAO,CAAC;QACpC,IAAIC,KAAK,EAAE;UACTiE,WAAW,GAAGjE,KAAK,CAAC,CAAC,CAAC,CAACJ,IAAI,CAAC,CAAC;UAC7B;UACAqE,WAAW,GAAGA,WAAW,CAACZ,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;UAC9C;UACAY,WAAW,GAAGA,WAAW,CAACZ,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;UACxDY,WAAW,GAAGA,WAAW,CAACZ,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;UACrDY,WAAW,GAAGA,WAAW,CAACrE,IAAI,CAAC,CAAC;UAEhCd,OAAO,CAACC,GAAG,CAAC,kCAAkC2D,MAAM,CAACzC,WAAW,aAAagE,WAAW,CAACjF,MAAM,EAAE,CAAC;UAClG;QACF;MACF;;MAEA;MACA,IAAI,CAACiF,WAAW,IAAIrD,OAAO,CAACuD,WAAW,CAAC,CAAC,CAAC3D,QAAQ,CAAC,aAAa,CAAC,EAAE;QACjE,MAAM4D,SAAS,GAAGxD,OAAO,CAACuD,WAAW,CAAC,CAAC,CAACxD,OAAO,CAAC,aAAa,CAAC;QAC9D,IAAIyD,SAAS,KAAK,CAAC,CAAC,EAAE;UACpBH,WAAW,GAAGrD,OAAO,CAAC3B,SAAS,CAACmF,SAAS,GAAG,EAAE,CAAC,CAACxE,IAAI,CAAC,CAAC;UACtDqE,WAAW,GAAGA,WAAW,CAACZ,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;UAC9CvE,OAAO,CAACC,GAAG,CAAC,gDAAgD2D,MAAM,CAACzC,WAAW,EAAE,CAAC;QACnF;MACF;MAEA,IAAI6D,aAAa,EAAE;QACjBtG,OAAO,CAACkF,MAAM,CAACzC,WAAW,CAAC,GAAG;UAC5B6D,aAAa,EAAEA,aAAa;UAC5BG,WAAW,EAAEA,WAAW,IAAI;QAC9B,CAAC;MACH,CAAC,MAAM;QACLnF,OAAO,CAACC,GAAG,CAAC,wCAAwC2D,MAAM,CAACzC,WAAW,EAAE,CAAC;MAC3E;IACF;IAEAnB,OAAO,CAACC,GAAG,CAAC,yBAAyBO,MAAM,CAACC,IAAI,CAAC/B,OAAO,CAAC,CAACwB,MAAM,YAAY,CAAC;IAC7E,OAAOxB,OAAO;EAChB;EAEA,MAAM6G,UAAUA,CAACC,YAAY,EAAEC,UAAU,EAAE;IACzC,IAAI;MACF,MAAM,CAAChH,SAAS,EAAEC,OAAO,CAAC,GAAG,MAAMgH,OAAO,CAACC,GAAG,CAAC,CAC7C,IAAI,CAAChH,iBAAiB,CAAC6G,YAAY,CAAC,EACpC,IAAI,CAAClF,eAAe,CAACmF,UAAU,CAAC,CACjC,CAAC;MAEF,OAAO;QAAEhH,SAAS;QAAEC;MAAQ,CAAC;IAC/B,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF;AACF;AAEA,eAAe9B,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}