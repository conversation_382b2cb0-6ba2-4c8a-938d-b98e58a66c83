{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Ta\\u0300i lie\\u0323\\u0302u CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/QuestionDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Card, Button, Alert, Badge, Row, Col } from 'react-bootstrap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuestionDisplay = ({\n  question,\n  groupQuestions,\n  answers,\n  userAnswer,\n  showAnswer,\n  onSubmitAnswer,\n  onRetryQuestion\n}) => {\n  _s();\n  var _groupQuestions$, _groupQuestions;\n  const [selectedAnswer, setSelectedAnswer] = useState(userAnswer || '');\n  const handleAnswerSelect = choice => {\n    if (showAnswer) return; // Prevent changing answer after submission\n    setSelectedAnswer(choice);\n  };\n  const handleSubmit = () => {\n    if (!selectedAnswer) return;\n    onSubmitAnswer(question.questionNumber, selectedAnswer);\n  };\n  const getAnswerData = () => {\n    return answers[question.questionNumber] || {};\n  };\n  const isCorrect = () => {\n    const answerData = getAnswerData();\n    return userAnswer === answerData.correctAnswer;\n  };\n  const getChoiceVariant = choice => {\n    if (!showAnswer) {\n      return selectedAnswer === choice ? 'primary' : 'outline-secondary';\n    }\n    const answerData = getAnswerData();\n    if (choice === answerData.correctAnswer) {\n      return 'success';\n    } else if (choice === userAnswer && choice !== answerData.correctAnswer) {\n      return 'danger';\n    }\n    return 'outline-secondary';\n  };\n  const getChoiceIcon = choice => {\n    if (!showAnswer) {\n      return selectedAnswer === choice ? 'fas fa-check-circle' : 'far fa-circle';\n    }\n    const answerData = getAnswerData();\n    if (choice === answerData.correctAnswer) {\n      return 'fas fa-check-circle';\n    } else if (choice === userAnswer && choice !== answerData.correctAnswer) {\n      return 'fas fa-times-circle';\n    }\n    return 'far fa-circle';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"question-display\",\n    children: [question.isGroupQuestion && question.context && /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-4 border-info\",\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        className: \"bg-info text-white\",\n        children: /*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-info-circle me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 15\n          }, this), \"Th\\xF4ng tin chung cho c\\xE2u h\\u1ECFi \", (_groupQuestions$ = groupQuestions[0]) === null || _groupQuestions$ === void 0 ? void 0 : _groupQuestions$.questionNumber, \" - \", (_groupQuestions = groupQuestions[groupQuestions.length - 1]) === null || _groupQuestions === void 0 ? void 0 : _groupQuestions.questionNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"context-content\",\n          style: {\n            whiteSpace: 'pre-line',\n            lineHeight: '1.6'\n          },\n          children: question.context\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-4 shadow\",\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        className: \"d-flex justify-content-between align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"mb-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-question-circle me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), \"C\\xE2u h\\u1ECFi \", question.questionNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Badge, {\n            bg: \"secondary\",\n            className: \"me-2\",\n            children: [\"ID: \", question.questionId]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), question.isGroupQuestion && /*#__PURE__*/_jsxDEV(Badge, {\n            bg: \"info\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-layer-group me-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 17\n            }, this), \"Nh\\xF3m c\\xE2u h\\u1ECFi\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"question-text mb-4\",\n          style: {\n            fontSize: '1.1rem',\n            lineHeight: '1.6'\n          },\n          children: question.questionText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"choices-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-3\",\n            children: \"Ch\\u1ECDn \\u0111\\xE1p \\xE1n:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: Object.entries(question.choices).map(([choice, text]) => /*#__PURE__*/_jsxDEV(Col, {\n              xs: 12,\n              className: \"mb-2\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: getChoiceVariant(choice),\n                className: \"w-100 text-start p-3\",\n                onClick: () => handleAnswerSelect(choice),\n                disabled: showAnswer,\n                style: {\n                  minHeight: '60px',\n                  border: selectedAnswer === choice && !showAnswer ? '2px solid #0d6efd' : undefined\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"me-3 mt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: getChoiceIcon(choice)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 125,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      className: \"me-2\",\n                      children: [choice, \")\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 128,\n                      columnNumber: 25\n                    }, this), text]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 127,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 19\n              }, this)\n            }, choice, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), !showAnswer && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"success\",\n            size: \"lg\",\n            onClick: handleSubmit,\n            disabled: !selectedAnswer,\n            className: \"px-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-check me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this), \"X\\xE1c nh\\u1EADn \\u0111\\xE1p \\xE1n\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: [\"Ho\\u1EB7c nh\\u1EA5n ph\\xEDm \", Object.keys(question.choices).map((choice, index) => `${index + 1} (${choice})`).join(', ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this), showAnswer && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            variant: isCorrect() ? 'success' : 'danger',\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas ${isCorrect() ? 'fa-check-circle' : 'fa-times-circle'} me-2`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: isCorrect() ? 'Chính xác!' : 'Không chính xác'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [\"\\u0110\\xE1p \\xE1n \\u0111\\xFAng: \", getAnswerData().correctAnswer]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this), userAnswer && userAnswer !== getAnswerData().correctAnswer && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-1\",\n                children: [\"B\\u1EA1n \\u0111\\xE3 ch\\u1ECDn: \", userAnswer]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this), getAnswerData().explanation && /*#__PURE__*/_jsxDEV(Card, {\n            className: \"mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n              className: \"bg-light\",\n              children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"mb-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-lightbulb me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 23\n                }, this), \"Gi\\u1EA3i th\\xEDch\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  whiteSpace: 'pre-line',\n                  lineHeight: '1.6'\n                },\n                children: getAnswerData().explanation\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(QuestionDisplay, \"bY9+dTFMyj3mt1tT/hv3/doJV24=\");\n_c = QuestionDisplay;\nexport default QuestionDisplay;\nvar _c;\n$RefreshReg$(_c, \"QuestionDisplay\");", "map": {"version": 3, "names": ["React", "useState", "Card", "<PERSON><PERSON>", "<PERSON><PERSON>", "Badge", "Row", "Col", "jsxDEV", "_jsxDEV", "QuestionDisplay", "question", "groupQuestions", "answers", "userAnswer", "showAnswer", "onSubmitAnswer", "onRetryQuestion", "_s", "_groupQuestions$", "_groupQuestions", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedAnswer", "handleAnswerSelect", "choice", "handleSubmit", "questionNumber", "getAnswerData", "isCorrect", "answerData", "<PERSON><PERSON><PERSON><PERSON>", "getChoiceVariant", "getChoiceIcon", "className", "children", "isGroupQuestion", "context", "Header", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "Body", "style", "whiteSpace", "lineHeight", "bg", "questionId", "fontSize", "questionText", "Object", "entries", "choices", "map", "text", "xs", "variant", "onClick", "disabled", "minHeight", "border", "undefined", "size", "keys", "index", "join", "explanation", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/QuestionDisplay.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { <PERSON>, Button, Alert, Badge, Row, Col } from 'react-bootstrap';\n\nconst QuestionDisplay = ({\n  question,\n  groupQuestions,\n  answers,\n  userAnswer,\n  showAnswer,\n  onSubmitAnswer,\n  onRetryQuestion\n}) => {\n  const [selectedAnswer, setSelectedAnswer] = useState(userAnswer || '');\n\n  const handleAnswerSelect = (choice) => {\n    if (showAnswer) return; // Prevent changing answer after submission\n    setSelectedAnswer(choice);\n  };\n\n  const handleSubmit = () => {\n    if (!selectedAnswer) return;\n    onSubmitAnswer(question.questionNumber, selectedAnswer);\n  };\n\n  const getAnswerData = () => {\n    return answers[question.questionNumber] || {};\n  };\n\n  const isCorrect = () => {\n    const answerData = getAnswerData();\n    return userAnswer === answerData.correctAnswer;\n  };\n\n  const getChoiceVariant = (choice) => {\n    if (!showAnswer) {\n      return selectedAnswer === choice ? 'primary' : 'outline-secondary';\n    }\n    \n    const answerData = getAnswerData();\n    if (choice === answerData.correctAnswer) {\n      return 'success';\n    } else if (choice === userAnswer && choice !== answerData.correctAnswer) {\n      return 'danger';\n    }\n    return 'outline-secondary';\n  };\n\n  const getChoiceIcon = (choice) => {\n    if (!showAnswer) {\n      return selectedAnswer === choice ? 'fas fa-check-circle' : 'far fa-circle';\n    }\n    \n    const answerData = getAnswerData();\n    if (choice === answerData.correctAnswer) {\n      return 'fas fa-check-circle';\n    } else if (choice === userAnswer && choice !== answerData.correctAnswer) {\n      return 'fas fa-times-circle';\n    }\n    return 'far fa-circle';\n  };\n\n  return (\n    <div className=\"question-display\">\n      {/* Context Section (for group questions) */}\n      {question.isGroupQuestion && question.context && (\n        <Card className=\"mb-4 border-info\">\n          <Card.Header className=\"bg-info text-white\">\n            <h6 className=\"mb-0\">\n              <i className=\"fas fa-info-circle me-2\"></i>\n              Thông tin chung cho câu hỏi {groupQuestions[0]?.questionNumber} - {groupQuestions[groupQuestions.length - 1]?.questionNumber}\n            </h6>\n          </Card.Header>\n          <Card.Body>\n            <div className=\"context-content\" style={{ whiteSpace: 'pre-line', lineHeight: '1.6' }}>\n              {question.context}\n            </div>\n          </Card.Body>\n        </Card>\n      )}\n\n      {/* Question Section */}\n      <Card className=\"mb-4 shadow\">\n        <Card.Header className=\"d-flex justify-content-between align-items-center\">\n          <h5 className=\"mb-0\">\n            <i className=\"fas fa-question-circle me-2\"></i>\n            Câu hỏi {question.questionNumber}\n          </h5>\n          <div>\n            <Badge bg=\"secondary\" className=\"me-2\">\n              ID: {question.questionId}\n            </Badge>\n            {question.isGroupQuestion && (\n              <Badge bg=\"info\">\n                <i className=\"fas fa-layer-group me-1\"></i>\n                Nhóm câu hỏi\n              </Badge>\n            )}\n          </div>\n        </Card.Header>\n        \n        <Card.Body>\n          {/* Question Text */}\n          <div className=\"question-text mb-4\" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>\n            {question.questionText}\n          </div>\n\n          {/* Answer Choices */}\n          <div className=\"choices-section\">\n            <h6 className=\"mb-3\">Chọn đáp án:</h6>\n            <Row>\n              {Object.entries(question.choices).map(([choice, text]) => (\n                <Col key={choice} xs={12} className=\"mb-2\">\n                  <Button\n                    variant={getChoiceVariant(choice)}\n                    className=\"w-100 text-start p-3\"\n                    onClick={() => handleAnswerSelect(choice)}\n                    disabled={showAnswer}\n                    style={{ \n                      minHeight: '60px',\n                      border: selectedAnswer === choice && !showAnswer ? '2px solid #0d6efd' : undefined\n                    }}\n                  >\n                    <div className=\"d-flex align-items-start\">\n                      <div className=\"me-3 mt-1\">\n                        <i className={getChoiceIcon(choice)}></i>\n                      </div>\n                      <div>\n                        <strong className=\"me-2\">{choice})</strong>\n                        {text}\n                      </div>\n                    </div>\n                  </Button>\n                </Col>\n              ))}\n            </Row>\n          </div>\n\n          {/* Submit Button */}\n          {!showAnswer && (\n            <div className=\"text-center mt-4\">\n              <Button\n                variant=\"success\"\n                size=\"lg\"\n                onClick={handleSubmit}\n                disabled={!selectedAnswer}\n                className=\"px-5\"\n              >\n                <i className=\"fas fa-check me-2\"></i>\n                Xác nhận đáp án\n              </Button>\n              <div className=\"mt-2\">\n                <small className=\"text-muted\">\n                  Hoặc nhấn phím {Object.keys(question.choices).map((choice, index) => `${index + 1} (${choice})`).join(', ')}\n                </small>\n              </div>\n            </div>\n          )}\n\n          {/* Answer Explanation */}\n          {showAnswer && (\n            <div className=\"mt-4\">\n              <Alert variant={isCorrect() ? 'success' : 'danger'}>\n                <div className=\"d-flex align-items-center mb-2\">\n                  <i className={`fas ${isCorrect() ? 'fa-check-circle' : 'fa-times-circle'} me-2`}></i>\n                  <strong>\n                    {isCorrect() ? 'Chính xác!' : 'Không chính xác'}\n                  </strong>\n                </div>\n                <div>\n                  <strong>Đáp án đúng: {getAnswerData().correctAnswer}</strong>\n                  {userAnswer && userAnswer !== getAnswerData().correctAnswer && (\n                    <div className=\"mt-1\">\n                      Bạn đã chọn: {userAnswer}\n                    </div>\n                  )}\n                </div>\n              </Alert>\n\n              {getAnswerData().explanation && (\n                <Card className=\"mt-3\">\n                  <Card.Header className=\"bg-light\">\n                    <h6 className=\"mb-0\">\n                      <i className=\"fas fa-lightbulb me-2\"></i>\n                      Giải thích\n                    </h6>\n                  </Card.Header>\n                  <Card.Body>\n                    <div style={{ whiteSpace: 'pre-line', lineHeight: '1.6' }}>\n                      {getAnswerData().explanation}\n                    </div>\n                  </Card.Body>\n                </Card>\n              )}\n            </div>\n          )}\n        </Card.Body>\n      </Card>\n    </div>\n  );\n};\n\nexport default QuestionDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,eAAe,GAAGA,CAAC;EACvBC,QAAQ;EACRC,cAAc;EACdC,OAAO;EACPC,UAAU;EACVC,UAAU;EACVC,cAAc;EACdC;AACF,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,eAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAACa,UAAU,IAAI,EAAE,CAAC;EAEtE,MAAMS,kBAAkB,GAAIC,MAAM,IAAK;IACrC,IAAIT,UAAU,EAAE,OAAO,CAAC;IACxBO,iBAAiB,CAACE,MAAM,CAAC;EAC3B,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACJ,cAAc,EAAE;IACrBL,cAAc,CAACL,QAAQ,CAACe,cAAc,EAAEL,cAAc,CAAC;EACzD,CAAC;EAED,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAOd,OAAO,CAACF,QAAQ,CAACe,cAAc,CAAC,IAAI,CAAC,CAAC;EAC/C,CAAC;EAED,MAAME,SAAS,GAAGA,CAAA,KAAM;IACtB,MAAMC,UAAU,GAAGF,aAAa,CAAC,CAAC;IAClC,OAAOb,UAAU,KAAKe,UAAU,CAACC,aAAa;EAChD,CAAC;EAED,MAAMC,gBAAgB,GAAIP,MAAM,IAAK;IACnC,IAAI,CAACT,UAAU,EAAE;MACf,OAAOM,cAAc,KAAKG,MAAM,GAAG,SAAS,GAAG,mBAAmB;IACpE;IAEA,MAAMK,UAAU,GAAGF,aAAa,CAAC,CAAC;IAClC,IAAIH,MAAM,KAAKK,UAAU,CAACC,aAAa,EAAE;MACvC,OAAO,SAAS;IAClB,CAAC,MAAM,IAAIN,MAAM,KAAKV,UAAU,IAAIU,MAAM,KAAKK,UAAU,CAACC,aAAa,EAAE;MACvE,OAAO,QAAQ;IACjB;IACA,OAAO,mBAAmB;EAC5B,CAAC;EAED,MAAME,aAAa,GAAIR,MAAM,IAAK;IAChC,IAAI,CAACT,UAAU,EAAE;MACf,OAAOM,cAAc,KAAKG,MAAM,GAAG,qBAAqB,GAAG,eAAe;IAC5E;IAEA,MAAMK,UAAU,GAAGF,aAAa,CAAC,CAAC;IAClC,IAAIH,MAAM,KAAKK,UAAU,CAACC,aAAa,EAAE;MACvC,OAAO,qBAAqB;IAC9B,CAAC,MAAM,IAAIN,MAAM,KAAKV,UAAU,IAAIU,MAAM,KAAKK,UAAU,CAACC,aAAa,EAAE;MACvE,OAAO,qBAAqB;IAC9B;IACA,OAAO,eAAe;EACxB,CAAC;EAED,oBACErB,OAAA;IAAKwB,SAAS,EAAC,kBAAkB;IAAAC,QAAA,GAE9BvB,QAAQ,CAACwB,eAAe,IAAIxB,QAAQ,CAACyB,OAAO,iBAC3C3B,OAAA,CAACP,IAAI;MAAC+B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAChCzB,OAAA,CAACP,IAAI,CAACmC,MAAM;QAACJ,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACzCzB,OAAA;UAAIwB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAClBzB,OAAA;YAAGwB,SAAS,EAAC;UAAyB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,2CACf,GAAAtB,gBAAA,GAACP,cAAc,CAAC,CAAC,CAAC,cAAAO,gBAAA,uBAAjBA,gBAAA,CAAmBO,cAAc,EAAC,KAAG,GAAAN,eAAA,GAACR,cAAc,CAACA,cAAc,CAAC8B,MAAM,GAAG,CAAC,CAAC,cAAAtB,eAAA,uBAAzCA,eAAA,CAA2CM,cAAc;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1H;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eACdhC,OAAA,CAACP,IAAI,CAACyC,IAAI;QAAAT,QAAA,eACRzB,OAAA;UAAKwB,SAAS,EAAC,iBAAiB;UAACW,KAAK,EAAE;YAAEC,UAAU,EAAE,UAAU;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EACnFvB,QAAQ,CAACyB;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACP,eAGDhC,OAAA,CAACP,IAAI;MAAC+B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC3BzB,OAAA,CAACP,IAAI,CAACmC,MAAM;QAACJ,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBACxEzB,OAAA;UAAIwB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAClBzB,OAAA;YAAGwB,SAAS,EAAC;UAA6B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,oBACvC,EAAC9B,QAAQ,CAACe,cAAc;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACLhC,OAAA;UAAAyB,QAAA,gBACEzB,OAAA,CAACJ,KAAK;YAAC0C,EAAE,EAAC,WAAW;YAACd,SAAS,EAAC,MAAM;YAAAC,QAAA,GAAC,MACjC,EAACvB,QAAQ,CAACqC,UAAU;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,EACP9B,QAAQ,CAACwB,eAAe,iBACvB1B,OAAA,CAACJ,KAAK;YAAC0C,EAAE,EAAC,MAAM;YAAAb,QAAA,gBACdzB,OAAA;cAAGwB,SAAS,EAAC;YAAyB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,2BAE7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEdhC,OAAA,CAACP,IAAI,CAACyC,IAAI;QAAAT,QAAA,gBAERzB,OAAA;UAAKwB,SAAS,EAAC,oBAAoB;UAACW,KAAK,EAAE;YAAEK,QAAQ,EAAE,QAAQ;YAAEH,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAClFvB,QAAQ,CAACuC;QAAY;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAGNhC,OAAA;UAAKwB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BzB,OAAA;YAAIwB,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtChC,OAAA,CAACH,GAAG;YAAA4B,QAAA,EACDiB,MAAM,CAACC,OAAO,CAACzC,QAAQ,CAAC0C,OAAO,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC9B,MAAM,EAAE+B,IAAI,CAAC,kBACnD9C,OAAA,CAACF,GAAG;cAAciD,EAAE,EAAE,EAAG;cAACvB,SAAS,EAAC,MAAM;cAAAC,QAAA,eACxCzB,OAAA,CAACN,MAAM;gBACLsD,OAAO,EAAE1B,gBAAgB,CAACP,MAAM,CAAE;gBAClCS,SAAS,EAAC,sBAAsB;gBAChCyB,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAACC,MAAM,CAAE;gBAC1CmC,QAAQ,EAAE5C,UAAW;gBACrB6B,KAAK,EAAE;kBACLgB,SAAS,EAAE,MAAM;kBACjBC,MAAM,EAAExC,cAAc,KAAKG,MAAM,IAAI,CAACT,UAAU,GAAG,mBAAmB,GAAG+C;gBAC3E,CAAE;gBAAA5B,QAAA,eAEFzB,OAAA;kBAAKwB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCzB,OAAA;oBAAKwB,SAAS,EAAC,WAAW;oBAAAC,QAAA,eACxBzB,OAAA;sBAAGwB,SAAS,EAAED,aAAa,CAACR,MAAM;oBAAE;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACNhC,OAAA;oBAAAyB,QAAA,gBACEzB,OAAA;sBAAQwB,SAAS,EAAC,MAAM;sBAAAC,QAAA,GAAEV,MAAM,EAAC,GAAC;oBAAA;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC1Cc,IAAI;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC,GApBDjB,MAAM;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBX,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAAC1B,UAAU,iBACVN,OAAA;UAAKwB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BzB,OAAA,CAACN,MAAM;YACLsD,OAAO,EAAC,SAAS;YACjBM,IAAI,EAAC,IAAI;YACTL,OAAO,EAAEjC,YAAa;YACtBkC,QAAQ,EAAE,CAACtC,cAAe;YAC1BY,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAEhBzB,OAAA;cAAGwB,SAAS,EAAC;YAAmB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,sCAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThC,OAAA;YAAKwB,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBzB,OAAA;cAAOwB,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,8BACb,EAACiB,MAAM,CAACa,IAAI,CAACrD,QAAQ,CAAC0C,OAAO,CAAC,CAACC,GAAG,CAAC,CAAC9B,MAAM,EAAEyC,KAAK,KAAK,GAAGA,KAAK,GAAG,CAAC,KAAKzC,MAAM,GAAG,CAAC,CAAC0C,IAAI,CAAC,IAAI,CAAC;YAAA;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGA1B,UAAU,iBACTN,OAAA;UAAKwB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBzB,OAAA,CAACL,KAAK;YAACqD,OAAO,EAAE7B,SAAS,CAAC,CAAC,GAAG,SAAS,GAAG,QAAS;YAAAM,QAAA,gBACjDzB,OAAA;cAAKwB,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7CzB,OAAA;gBAAGwB,SAAS,EAAE,OAAOL,SAAS,CAAC,CAAC,GAAG,iBAAiB,GAAG,iBAAiB;cAAQ;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrFhC,OAAA;gBAAAyB,QAAA,EACGN,SAAS,CAAC,CAAC,GAAG,YAAY,GAAG;cAAiB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNhC,OAAA;cAAAyB,QAAA,gBACEzB,OAAA;gBAAAyB,QAAA,GAAQ,kCAAa,EAACP,aAAa,CAAC,CAAC,CAACG,aAAa;cAAA;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,EAC5D3B,UAAU,IAAIA,UAAU,KAAKa,aAAa,CAAC,CAAC,CAACG,aAAa,iBACzDrB,OAAA;gBAAKwB,SAAS,EAAC,MAAM;gBAAAC,QAAA,GAAC,iCACP,EAACpB,UAAU;cAAA;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EAEPd,aAAa,CAAC,CAAC,CAACwC,WAAW,iBAC1B1D,OAAA,CAACP,IAAI;YAAC+B,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACpBzB,OAAA,CAACP,IAAI,CAACmC,MAAM;cAACJ,SAAS,EAAC,UAAU;cAAAC,QAAA,eAC/BzB,OAAA;gBAAIwB,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAClBzB,OAAA;kBAAGwB,SAAS,EAAC;gBAAuB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,sBAE3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACdhC,OAAA,CAACP,IAAI,CAACyC,IAAI;cAAAT,QAAA,eACRzB,OAAA;gBAAKmC,KAAK,EAAE;kBAAEC,UAAU,EAAE,UAAU;kBAAEC,UAAU,EAAE;gBAAM,CAAE;gBAAAZ,QAAA,EACvDP,aAAa,CAAC,CAAC,CAACwC;cAAW;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACvB,EAAA,CApMIR,eAAe;AAAA0D,EAAA,GAAf1D,eAAe;AAsMrB,eAAeA,eAAe;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}