{"ast": null, "code": "import ownerWindow from './ownerWindow';\n/**\n * Returns one or all computed style properties of an element.\n * \n * @param node the element\n * @param psuedoElement the style property\n */\n\nexport default function getComputedStyle(node, psuedoElement) {\n  return ownerWindow(node).getComputedStyle(node, psuedoElement);\n}", "map": {"version": 3, "names": ["ownerWindow", "getComputedStyle", "node", "psuedoElement"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/dom-helpers/esm/getComputedStyle.js"], "sourcesContent": ["import ownerWindow from './ownerWindow';\n/**\n * Returns one or all computed style properties of an element.\n * \n * @param node the element\n * @param psuedoElement the style property\n */\n\nexport default function getComputedStyle(node, psuedoElement) {\n  return ownerWindow(node).getComputedStyle(node, psuedoElement);\n}"], "mappings": "AAAA,OAAOA,WAAW,MAAM,eAAe;AACvC;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,aAAa,EAAE;EAC5D,OAAOH,WAAW,CAACE,IAAI,CAAC,CAACD,gBAAgB,CAACC,IAAI,EAAEC,aAAa,CAAC;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}