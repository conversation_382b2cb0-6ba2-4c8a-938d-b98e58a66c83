{"ast": null, "code": "// PDF Parser for CFA Questions and Answers\nimport * as pdfjsLib from 'pdfjs-dist';\n\n// Set worker path - use the worker from the same version\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;\nclass CFAQuestionParser {\n  constructor() {\n    this.questions = [];\n    this.answers = {};\n  }\n  async parseQuestionFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      let fullText = '';\n\n      // Extract text from all pages\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n        const pageText = textContent.items.map(item => item.str).join(' ');\n        fullText += pageText + '\\n';\n      }\n\n      // Debug: Log extracted text\n      console.log('Extracted text length:', fullText.length);\n      console.log('First 1000 characters:', fullText.substring(0, 1000));\n\n      // Parse questions from text\n      const questions = this.extractQuestions(fullText);\n      console.log('Extracted questions:', questions.length);\n      return questions;\n    } catch (error) {\n      console.error('Error parsing question file:', error);\n      throw error;\n    }\n  }\n  async parseAnswerFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      let fullText = '';\n\n      // Extract text from all pages\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n        const pageText = textContent.items.map(item => item.str).join(' ');\n        fullText += pageText + '\\n';\n      }\n\n      // Parse answers from text\n      const answers = this.extractAnswers(fullText);\n      return answers;\n    } catch (error) {\n      console.error('Error parsing answer file:', error);\n      throw error;\n    }\n  }\n  extractQuestions(text) {\n    const questions = [];\n\n    // Clean text but preserve line breaks for better parsing\n    text = text.replace(/\\s+/g, ' ').trim();\n    console.log('Looking for question patterns in text...');\n\n    // Try multiple patterns for question detection\n    const patterns = [/Question #(\\d+) of (\\d+) Question ID: (\\d+)/g, /Question #(\\d+) - (\\d+) of (\\d+) Question ID: (\\d+)/g, /Question #(\\d+) of (\\d+)/g, /Question #(\\d+)/g];\n    let foundPatterns = [];\n    for (const pattern of patterns) {\n      const matches = [...text.matchAll(pattern)];\n      if (matches.length > 0) {\n        console.log(`Found ${matches.length} matches for pattern:`, pattern.source);\n        foundPatterns.push(...matches);\n      }\n    }\n    if (foundPatterns.length === 0) {\n      console.log('No question patterns found. Trying alternative extraction...');\n      return this.extractQuestionsAlternative(text);\n    }\n\n    // Split by Question # pattern\n    const questionBlocks = text.split(/(?=Question #\\d+)/);\n    console.log(`Split into ${questionBlocks.length} blocks`);\n    let currentContext = '';\n    for (const block of questionBlocks) {\n      if (!block.trim()) continue;\n      console.log('Processing block:', block.substring(0, 100) + '...');\n\n      // Check for single question pattern\n      const singleMatch = block.match(/Question #(\\d+) of (\\d+) Question ID: (\\d+)\\s*(.*)/s);\n\n      // Check for group question pattern\n      const groupMatch = block.match(/Question #(\\d+) - (\\d+) of (\\d+) Question ID: (\\d+)\\s*(.*)/s);\n      if (groupMatch) {\n        const [, startQ, endQ, totalQ,, content] = groupMatch;\n        console.log(`Found group question: ${startQ}-${endQ} of ${totalQ}`);\n\n        // Extract context before the question range\n        const contextMatch = content.match(/^(.*?)(?=Question #\\d+ - \\d+)/s);\n        if (contextMatch) {\n          currentContext = contextMatch[1].trim();\n        }\n\n        // Extract individual questions in the group\n        const groupQuestions = this.extractGroupQuestions(content, parseInt(startQ), parseInt(endQ), parseInt(totalQ), currentContext);\n        questions.push(...groupQuestions);\n      } else if (singleMatch) {\n        const [, questionNum, totalQ, questionId, content] = singleMatch;\n        console.log(`Found single question: ${questionNum} of ${totalQ}`);\n        const questionData = this.parseSingleQuestion(content, parseInt(questionNum), parseInt(totalQ), questionId);\n        if (questionData) {\n          questions.push(questionData);\n        }\n\n        // Reset context for single questions\n        currentContext = '';\n      } else {\n        // Try simpler pattern\n        const simpleMatch = block.match(/Question #(\\d+)\\s*(.*)/s);\n        if (simpleMatch) {\n          const [, questionNum, content] = simpleMatch;\n          console.log(`Found simple question: ${questionNum}`);\n          const questionData = this.parseSingleQuestion(content, parseInt(questionNum), 100,\n          // Default total\n          'unknown');\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n    }\n    console.log(`Extracted ${questions.length} questions total`);\n    return questions;\n  }\n  extractGroupQuestions(content, startQ, endQ, totalQ, context) {\n    const questions = [];\n\n    // Split by individual question patterns within the group\n    const subQuestionPattern = /Question #(\\d+) - \\d+ of \\d+ Question ID: (\\d+)\\s*(.*?)(?=Question #\\d+|$)/gs;\n    let match;\n    while ((match = subQuestionPattern.exec(content)) !== null) {\n      const [, questionNum, questionId, questionContent] = match;\n      const questionData = this.parseSingleQuestion(questionContent, parseInt(questionNum), totalQ, questionId, context);\n      if (questionData) {\n        questions.push(questionData);\n      }\n    }\n    return questions;\n  }\n  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {\n    if (!content.trim()) return null;\n\n    // Split content into lines\n    const lines = content.split('\\n').map(line => line.trim()).filter(line => line);\n    let questionText = '';\n    const choices = {};\n    let currentChoice = null;\n    let choiceText = '';\n    for (const line of lines) {\n      // Check if line starts with choice pattern (A), B), C), etc.\n      const choiceMatch = line.match(/^([A-E])\\)\\s*(.*)/);\n      if (choiceMatch) {\n        // Save previous choice\n        if (currentChoice) {\n          choices[currentChoice] = choiceText.trim();\n        }\n        currentChoice = choiceMatch[1];\n        choiceText = choiceMatch[2];\n      } else if (currentChoice) {\n        // Continue choice text\n        choiceText += ' ' + line;\n      } else {\n        // Question text\n        questionText += ' ' + line;\n      }\n    }\n\n    // Save last choice\n    if (currentChoice) {\n      choices[currentChoice] = choiceText.trim();\n    }\n    if (!questionText.trim() || Object.keys(choices).length === 0) {\n      return null;\n    }\n    return {\n      questionNumber: questionNum,\n      totalQuestions: totalQ,\n      questionId: questionId,\n      context: context,\n      questionText: questionText.trim(),\n      choices: choices,\n      isGroupQuestion: Boolean(context)\n    };\n  }\n  extractAnswers(text) {\n    const answers = {};\n\n    // Clean text\n    text = text.replace(/\\s+/g, ' ').trim();\n\n    // Split by Question # pattern\n    const questionBlocks = text.split(/Question #(\\d+)/);\n    for (let i = 1; i < questionBlocks.length; i += 2) {\n      if (i + 1 < questionBlocks.length) {\n        const questionNum = parseInt(questionBlocks[i]);\n        const content = questionBlocks[i + 1];\n\n        // Find correct answer\n        let correctAnswer = null;\n        const answerPatterns = [/The correct answer is ([A-E])/i, /Correct answer:\\s*([A-E])/i, /Answer:\\s*([A-E])/i, /([A-E])\\s*is correct/i];\n        for (const pattern of answerPatterns) {\n          const match = content.match(pattern);\n          if (match) {\n            correctAnswer = match[1].toUpperCase();\n            break;\n          }\n        }\n\n        // Find explanation\n        let explanation = '';\n        const explanationPatterns = [/Explanation[:\\s]+(.*?)(?=Question #|$)/is, /Explanation[:\\s]+(.*?)(?=\\(Module|$)/is];\n        for (const pattern of explanationPatterns) {\n          const match = content.match(pattern);\n          if (match) {\n            explanation = match[1].trim();\n            break;\n          }\n        }\n        if (correctAnswer) {\n          answers[questionNum] = {\n            correctAnswer: correctAnswer,\n            explanation: explanation\n          };\n        }\n      }\n    }\n    return answers;\n  }\n  async parseFiles(questionFile, answerFile) {\n    try {\n      const [questions, answers] = await Promise.all([this.parseQuestionFile(questionFile), this.parseAnswerFile(answerFile)]);\n      return {\n        questions,\n        answers\n      };\n    } catch (error) {\n      console.error('Error parsing files:', error);\n      throw error;\n    }\n  }\n}\nexport default CFAQuestionParser;", "map": {"version": 3, "names": ["pdfjsLib", "GlobalWorkerOptions", "workerSrc", "CFAQuestionParser", "constructor", "questions", "answers", "parseQuestionFile", "file", "arrayBuffer", "pdf", "getDocument", "promise", "fullText", "i", "numPages", "page", "getPage", "textContent", "getTextContent", "pageText", "items", "map", "item", "str", "join", "console", "log", "length", "substring", "extractQuestions", "error", "parseAnswerFile", "extractAnswers", "text", "replace", "trim", "patterns", "foundPatterns", "pattern", "matches", "matchAll", "source", "push", "extractQuestionsAlternative", "questionBlocks", "split", "currentContext", "block", "singleMatch", "match", "groupMatch", "startQ", "endQ", "totalQ", "content", "contextMatch", "groupQuestions", "extractGroupQuestions", "parseInt", "questionNum", "questionId", "questionData", "parseSingleQuestion", "simpleMatch", "context", "subQuestionPattern", "exec", "questionContent", "lines", "line", "filter", "questionText", "choices", "currentChoice", "choiceText", "choiceMatch", "Object", "keys", "questionNumber", "totalQuestions", "isGroupQuestion", "Boolean", "<PERSON><PERSON><PERSON><PERSON>", "answerPatterns", "toUpperCase", "explanation", "explanationPatterns", "parseFiles", "questionFile", "answerFile", "Promise", "all"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/utils/pdfParser.js"], "sourcesContent": ["// PDF Parser for CFA Questions and Answers\nimport * as pdfjsLib from 'pdfjs-dist';\n\n// Set worker path - use the worker from the same version\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;\n\nclass CFAQuestionParser {\n  constructor() {\n    this.questions = [];\n    this.answers = {};\n  }\n\n  async parseQuestionFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n\n      let fullText = '';\n\n      // Extract text from all pages\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n        const pageText = textContent.items.map(item => item.str).join(' ');\n        fullText += pageText + '\\n';\n      }\n\n      // Debug: Log extracted text\n      console.log('Extracted text length:', fullText.length);\n      console.log('First 1000 characters:', fullText.substring(0, 1000));\n\n      // Parse questions from text\n      const questions = this.extractQuestions(fullText);\n      console.log('Extracted questions:', questions.length);\n\n      return questions;\n    } catch (error) {\n      console.error('Error parsing question file:', error);\n      throw error;\n    }\n  }\n\n  async parseAnswerFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      \n      let fullText = '';\n      \n      // Extract text from all pages\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n        const pageText = textContent.items.map(item => item.str).join(' ');\n        fullText += pageText + '\\n';\n      }\n      \n      // Parse answers from text\n      const answers = this.extractAnswers(fullText);\n      return answers;\n    } catch (error) {\n      console.error('Error parsing answer file:', error);\n      throw error;\n    }\n  }\n\n  extractQuestions(text) {\n    const questions = [];\n\n    // Clean text but preserve line breaks for better parsing\n    text = text.replace(/\\s+/g, ' ').trim();\n\n    console.log('Looking for question patterns in text...');\n\n    // Try multiple patterns for question detection\n    const patterns = [\n      /Question #(\\d+) of (\\d+) Question ID: (\\d+)/g,\n      /Question #(\\d+) - (\\d+) of (\\d+) Question ID: (\\d+)/g,\n      /Question #(\\d+) of (\\d+)/g,\n      /Question #(\\d+)/g\n    ];\n\n    let foundPatterns = [];\n    for (const pattern of patterns) {\n      const matches = [...text.matchAll(pattern)];\n      if (matches.length > 0) {\n        console.log(`Found ${matches.length} matches for pattern:`, pattern.source);\n        foundPatterns.push(...matches);\n      }\n    }\n\n    if (foundPatterns.length === 0) {\n      console.log('No question patterns found. Trying alternative extraction...');\n      return this.extractQuestionsAlternative(text);\n    }\n\n    // Split by Question # pattern\n    const questionBlocks = text.split(/(?=Question #\\d+)/);\n    console.log(`Split into ${questionBlocks.length} blocks`);\n\n    let currentContext = '';\n\n    for (const block of questionBlocks) {\n      if (!block.trim()) continue;\n\n      console.log('Processing block:', block.substring(0, 100) + '...');\n\n      // Check for single question pattern\n      const singleMatch = block.match(/Question #(\\d+) of (\\d+) Question ID: (\\d+)\\s*(.*)/s);\n\n      // Check for group question pattern\n      const groupMatch = block.match(/Question #(\\d+) - (\\d+) of (\\d+) Question ID: (\\d+)\\s*(.*)/s);\n\n      if (groupMatch) {\n        const [, startQ, endQ, totalQ, , content] = groupMatch;\n        console.log(`Found group question: ${startQ}-${endQ} of ${totalQ}`);\n\n        // Extract context before the question range\n        const contextMatch = content.match(/^(.*?)(?=Question #\\d+ - \\d+)/s);\n        if (contextMatch) {\n          currentContext = contextMatch[1].trim();\n        }\n\n        // Extract individual questions in the group\n        const groupQuestions = this.extractGroupQuestions(\n          content,\n          parseInt(startQ),\n          parseInt(endQ),\n          parseInt(totalQ),\n          currentContext\n        );\n\n        questions.push(...groupQuestions);\n\n      } else if (singleMatch) {\n        const [, questionNum, totalQ, questionId, content] = singleMatch;\n        console.log(`Found single question: ${questionNum} of ${totalQ}`);\n\n        const questionData = this.parseSingleQuestion(\n          content,\n          parseInt(questionNum),\n          parseInt(totalQ),\n          questionId\n        );\n\n        if (questionData) {\n          questions.push(questionData);\n        }\n\n        // Reset context for single questions\n        currentContext = '';\n      } else {\n        // Try simpler pattern\n        const simpleMatch = block.match(/Question #(\\d+)\\s*(.*)/s);\n        if (simpleMatch) {\n          const [, questionNum, content] = simpleMatch;\n          console.log(`Found simple question: ${questionNum}`);\n\n          const questionData = this.parseSingleQuestion(\n            content,\n            parseInt(questionNum),\n            100, // Default total\n            'unknown'\n          );\n\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n    }\n\n    console.log(`Extracted ${questions.length} questions total`);\n    return questions;\n  }\n\n  extractGroupQuestions(content, startQ, endQ, totalQ, context) {\n    const questions = [];\n    \n    // Split by individual question patterns within the group\n    const subQuestionPattern = /Question #(\\d+) - \\d+ of \\d+ Question ID: (\\d+)\\s*(.*?)(?=Question #\\d+|$)/gs;\n    let match;\n    \n    while ((match = subQuestionPattern.exec(content)) !== null) {\n      const [, questionNum, questionId, questionContent] = match;\n      \n      const questionData = this.parseSingleQuestion(\n        questionContent,\n        parseInt(questionNum),\n        totalQ,\n        questionId,\n        context\n      );\n      \n      if (questionData) {\n        questions.push(questionData);\n      }\n    }\n    \n    return questions;\n  }\n\n  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {\n    if (!content.trim()) return null;\n    \n    // Split content into lines\n    const lines = content.split('\\n').map(line => line.trim()).filter(line => line);\n    \n    let questionText = '';\n    const choices = {};\n    let currentChoice = null;\n    let choiceText = '';\n    \n    for (const line of lines) {\n      // Check if line starts with choice pattern (A), B), C), etc.\n      const choiceMatch = line.match(/^([A-E])\\)\\s*(.*)/);\n      \n      if (choiceMatch) {\n        // Save previous choice\n        if (currentChoice) {\n          choices[currentChoice] = choiceText.trim();\n        }\n        \n        currentChoice = choiceMatch[1];\n        choiceText = choiceMatch[2];\n      } else if (currentChoice) {\n        // Continue choice text\n        choiceText += ' ' + line;\n      } else {\n        // Question text\n        questionText += ' ' + line;\n      }\n    }\n    \n    // Save last choice\n    if (currentChoice) {\n      choices[currentChoice] = choiceText.trim();\n    }\n    \n    if (!questionText.trim() || Object.keys(choices).length === 0) {\n      return null;\n    }\n    \n    return {\n      questionNumber: questionNum,\n      totalQuestions: totalQ,\n      questionId: questionId,\n      context: context,\n      questionText: questionText.trim(),\n      choices: choices,\n      isGroupQuestion: Boolean(context)\n    };\n  }\n\n  extractAnswers(text) {\n    const answers = {};\n    \n    // Clean text\n    text = text.replace(/\\s+/g, ' ').trim();\n    \n    // Split by Question # pattern\n    const questionBlocks = text.split(/Question #(\\d+)/);\n    \n    for (let i = 1; i < questionBlocks.length; i += 2) {\n      if (i + 1 < questionBlocks.length) {\n        const questionNum = parseInt(questionBlocks[i]);\n        const content = questionBlocks[i + 1];\n        \n        // Find correct answer\n        let correctAnswer = null;\n        const answerPatterns = [\n          /The correct answer is ([A-E])/i,\n          /Correct answer:\\s*([A-E])/i,\n          /Answer:\\s*([A-E])/i,\n          /([A-E])\\s*is correct/i\n        ];\n        \n        for (const pattern of answerPatterns) {\n          const match = content.match(pattern);\n          if (match) {\n            correctAnswer = match[1].toUpperCase();\n            break;\n          }\n        }\n        \n        // Find explanation\n        let explanation = '';\n        const explanationPatterns = [\n          /Explanation[:\\s]+(.*?)(?=Question #|$)/is,\n          /Explanation[:\\s]+(.*?)(?=\\(Module|$)/is\n        ];\n        \n        for (const pattern of explanationPatterns) {\n          const match = content.match(pattern);\n          if (match) {\n            explanation = match[1].trim();\n            break;\n          }\n        }\n        \n        if (correctAnswer) {\n          answers[questionNum] = {\n            correctAnswer: correctAnswer,\n            explanation: explanation\n          };\n        }\n      }\n    }\n    \n    return answers;\n  }\n\n  async parseFiles(questionFile, answerFile) {\n    try {\n      const [questions, answers] = await Promise.all([\n        this.parseQuestionFile(questionFile),\n        this.parseAnswerFile(answerFile)\n      ]);\n      \n      return { questions, answers };\n    } catch (error) {\n      console.error('Error parsing files:', error);\n      throw error;\n    }\n  }\n}\n\nexport default CFAQuestionParser;\n"], "mappings": "AAAA;AACA,OAAO,KAAKA,QAAQ,MAAM,YAAY;;AAEtC;AACAA,QAAQ,CAACC,mBAAmB,CAACC,SAAS,GAAG,+DAA+D;AAExG,MAAMC,iBAAiB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;EACnB;EAEA,MAAMC,iBAAiBA,CAACC,IAAI,EAAE;IAC5B,IAAI;MACF,MAAMC,WAAW,GAAG,MAAMD,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5C,MAAMC,GAAG,GAAG,MAAMV,QAAQ,CAACW,WAAW,CAACF,WAAW,CAAC,CAACG,OAAO;MAE3D,IAAIC,QAAQ,GAAG,EAAE;;MAEjB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,GAAG,CAACK,QAAQ,EAAED,CAAC,EAAE,EAAE;QACtC,MAAME,IAAI,GAAG,MAAMN,GAAG,CAACO,OAAO,CAACH,CAAC,CAAC;QACjC,MAAMI,WAAW,GAAG,MAAMF,IAAI,CAACG,cAAc,CAAC,CAAC;QAC/C,MAAMC,QAAQ,GAAGF,WAAW,CAACG,KAAK,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;QAClEZ,QAAQ,IAAIO,QAAQ,GAAG,IAAI;MAC7B;;MAEA;MACAM,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEd,QAAQ,CAACe,MAAM,CAAC;MACtDF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEd,QAAQ,CAACgB,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;;MAElE;MACA,MAAMxB,SAAS,GAAG,IAAI,CAACyB,gBAAgB,CAACjB,QAAQ,CAAC;MACjDa,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEtB,SAAS,CAACuB,MAAM,CAAC;MAErD,OAAOvB,SAAS;IAClB,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;EAEA,MAAMC,eAAeA,CAACxB,IAAI,EAAE;IAC1B,IAAI;MACF,MAAMC,WAAW,GAAG,MAAMD,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5C,MAAMC,GAAG,GAAG,MAAMV,QAAQ,CAACW,WAAW,CAACF,WAAW,CAAC,CAACG,OAAO;MAE3D,IAAIC,QAAQ,GAAG,EAAE;;MAEjB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,GAAG,CAACK,QAAQ,EAAED,CAAC,EAAE,EAAE;QACtC,MAAME,IAAI,GAAG,MAAMN,GAAG,CAACO,OAAO,CAACH,CAAC,CAAC;QACjC,MAAMI,WAAW,GAAG,MAAMF,IAAI,CAACG,cAAc,CAAC,CAAC;QAC/C,MAAMC,QAAQ,GAAGF,WAAW,CAACG,KAAK,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;QAClEZ,QAAQ,IAAIO,QAAQ,GAAG,IAAI;MAC7B;;MAEA;MACA,MAAMd,OAAO,GAAG,IAAI,CAAC2B,cAAc,CAACpB,QAAQ,CAAC;MAC7C,OAAOP,OAAO;IAChB,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF;EAEAD,gBAAgBA,CAACI,IAAI,EAAE;IACrB,MAAM7B,SAAS,GAAG,EAAE;;IAEpB;IACA6B,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC;IAEvCV,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;IAEvD;IACA,MAAMU,QAAQ,GAAG,CACf,8CAA8C,EAC9C,sDAAsD,EACtD,2BAA2B,EAC3B,kBAAkB,CACnB;IAED,IAAIC,aAAa,GAAG,EAAE;IACtB,KAAK,MAAMC,OAAO,IAAIF,QAAQ,EAAE;MAC9B,MAAMG,OAAO,GAAG,CAAC,GAAGN,IAAI,CAACO,QAAQ,CAACF,OAAO,CAAC,CAAC;MAC3C,IAAIC,OAAO,CAACZ,MAAM,GAAG,CAAC,EAAE;QACtBF,OAAO,CAACC,GAAG,CAAC,SAASa,OAAO,CAACZ,MAAM,uBAAuB,EAAEW,OAAO,CAACG,MAAM,CAAC;QAC3EJ,aAAa,CAACK,IAAI,CAAC,GAAGH,OAAO,CAAC;MAChC;IACF;IAEA,IAAIF,aAAa,CAACV,MAAM,KAAK,CAAC,EAAE;MAC9BF,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;MAC3E,OAAO,IAAI,CAACiB,2BAA2B,CAACV,IAAI,CAAC;IAC/C;;IAEA;IACA,MAAMW,cAAc,GAAGX,IAAI,CAACY,KAAK,CAAC,mBAAmB,CAAC;IACtDpB,OAAO,CAACC,GAAG,CAAC,cAAckB,cAAc,CAACjB,MAAM,SAAS,CAAC;IAEzD,IAAImB,cAAc,GAAG,EAAE;IAEvB,KAAK,MAAMC,KAAK,IAAIH,cAAc,EAAE;MAClC,IAAI,CAACG,KAAK,CAACZ,IAAI,CAAC,CAAC,EAAE;MAEnBV,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEqB,KAAK,CAACnB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;;MAEjE;MACA,MAAMoB,WAAW,GAAGD,KAAK,CAACE,KAAK,CAAC,qDAAqD,CAAC;;MAEtF;MACA,MAAMC,UAAU,GAAGH,KAAK,CAACE,KAAK,CAAC,6DAA6D,CAAC;MAE7F,IAAIC,UAAU,EAAE;QACd,MAAM,GAAGC,MAAM,EAAEC,IAAI,EAAEC,MAAM,GAAIC,OAAO,CAAC,GAAGJ,UAAU;QACtDzB,OAAO,CAACC,GAAG,CAAC,yBAAyByB,MAAM,IAAIC,IAAI,OAAOC,MAAM,EAAE,CAAC;;QAEnE;QACA,MAAME,YAAY,GAAGD,OAAO,CAACL,KAAK,CAAC,gCAAgC,CAAC;QACpE,IAAIM,YAAY,EAAE;UAChBT,cAAc,GAAGS,YAAY,CAAC,CAAC,CAAC,CAACpB,IAAI,CAAC,CAAC;QACzC;;QAEA;QACA,MAAMqB,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAC/CH,OAAO,EACPI,QAAQ,CAACP,MAAM,CAAC,EAChBO,QAAQ,CAACN,IAAI,CAAC,EACdM,QAAQ,CAACL,MAAM,CAAC,EAChBP,cACF,CAAC;QAED1C,SAAS,CAACsC,IAAI,CAAC,GAAGc,cAAc,CAAC;MAEnC,CAAC,MAAM,IAAIR,WAAW,EAAE;QACtB,MAAM,GAAGW,WAAW,EAAEN,MAAM,EAAEO,UAAU,EAAEN,OAAO,CAAC,GAAGN,WAAW;QAChEvB,OAAO,CAACC,GAAG,CAAC,0BAA0BiC,WAAW,OAAON,MAAM,EAAE,CAAC;QAEjE,MAAMQ,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CR,OAAO,EACPI,QAAQ,CAACC,WAAW,CAAC,EACrBD,QAAQ,CAACL,MAAM,CAAC,EAChBO,UACF,CAAC;QAED,IAAIC,YAAY,EAAE;UAChBzD,SAAS,CAACsC,IAAI,CAACmB,YAAY,CAAC;QAC9B;;QAEA;QACAf,cAAc,GAAG,EAAE;MACrB,CAAC,MAAM;QACL;QACA,MAAMiB,WAAW,GAAGhB,KAAK,CAACE,KAAK,CAAC,yBAAyB,CAAC;QAC1D,IAAIc,WAAW,EAAE;UACf,MAAM,GAAGJ,WAAW,EAAEL,OAAO,CAAC,GAAGS,WAAW;UAC5CtC,OAAO,CAACC,GAAG,CAAC,0BAA0BiC,WAAW,EAAE,CAAC;UAEpD,MAAME,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CR,OAAO,EACPI,QAAQ,CAACC,WAAW,CAAC,EACrB,GAAG;UAAE;UACL,SACF,CAAC;UAED,IAAIE,YAAY,EAAE;YAChBzD,SAAS,CAACsC,IAAI,CAACmB,YAAY,CAAC;UAC9B;QACF;MACF;IACF;IAEApC,OAAO,CAACC,GAAG,CAAC,aAAatB,SAAS,CAACuB,MAAM,kBAAkB,CAAC;IAC5D,OAAOvB,SAAS;EAClB;EAEAqD,qBAAqBA,CAACH,OAAO,EAAEH,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEW,OAAO,EAAE;IAC5D,MAAM5D,SAAS,GAAG,EAAE;;IAEpB;IACA,MAAM6D,kBAAkB,GAAG,8EAA8E;IACzG,IAAIhB,KAAK;IAET,OAAO,CAACA,KAAK,GAAGgB,kBAAkB,CAACC,IAAI,CAACZ,OAAO,CAAC,MAAM,IAAI,EAAE;MAC1D,MAAM,GAAGK,WAAW,EAAEC,UAAU,EAAEO,eAAe,CAAC,GAAGlB,KAAK;MAE1D,MAAMY,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CK,eAAe,EACfT,QAAQ,CAACC,WAAW,CAAC,EACrBN,MAAM,EACNO,UAAU,EACVI,OACF,CAAC;MAED,IAAIH,YAAY,EAAE;QAChBzD,SAAS,CAACsC,IAAI,CAACmB,YAAY,CAAC;MAC9B;IACF;IAEA,OAAOzD,SAAS;EAClB;EAEA0D,mBAAmBA,CAACR,OAAO,EAAEK,WAAW,EAAEN,MAAM,EAAEO,UAAU,EAAEI,OAAO,GAAG,EAAE,EAAE;IAC1E,IAAI,CAACV,OAAO,CAACnB,IAAI,CAAC,CAAC,EAAE,OAAO,IAAI;;IAEhC;IACA,MAAMiC,KAAK,GAAGd,OAAO,CAACT,KAAK,CAAC,IAAI,CAAC,CAACxB,GAAG,CAACgD,IAAI,IAAIA,IAAI,CAAClC,IAAI,CAAC,CAAC,CAAC,CAACmC,MAAM,CAACD,IAAI,IAAIA,IAAI,CAAC;IAE/E,IAAIE,YAAY,GAAG,EAAE;IACrB,MAAMC,OAAO,GAAG,CAAC,CAAC;IAClB,IAAIC,aAAa,GAAG,IAAI;IACxB,IAAIC,UAAU,GAAG,EAAE;IAEnB,KAAK,MAAML,IAAI,IAAID,KAAK,EAAE;MACxB;MACA,MAAMO,WAAW,GAAGN,IAAI,CAACpB,KAAK,CAAC,mBAAmB,CAAC;MAEnD,IAAI0B,WAAW,EAAE;QACf;QACA,IAAIF,aAAa,EAAE;UACjBD,OAAO,CAACC,aAAa,CAAC,GAAGC,UAAU,CAACvC,IAAI,CAAC,CAAC;QAC5C;QAEAsC,aAAa,GAAGE,WAAW,CAAC,CAAC,CAAC;QAC9BD,UAAU,GAAGC,WAAW,CAAC,CAAC,CAAC;MAC7B,CAAC,MAAM,IAAIF,aAAa,EAAE;QACxB;QACAC,UAAU,IAAI,GAAG,GAAGL,IAAI;MAC1B,CAAC,MAAM;QACL;QACAE,YAAY,IAAI,GAAG,GAAGF,IAAI;MAC5B;IACF;;IAEA;IACA,IAAII,aAAa,EAAE;MACjBD,OAAO,CAACC,aAAa,CAAC,GAAGC,UAAU,CAACvC,IAAI,CAAC,CAAC;IAC5C;IAEA,IAAI,CAACoC,YAAY,CAACpC,IAAI,CAAC,CAAC,IAAIyC,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,CAAC7C,MAAM,KAAK,CAAC,EAAE;MAC7D,OAAO,IAAI;IACb;IAEA,OAAO;MACLmD,cAAc,EAAEnB,WAAW;MAC3BoB,cAAc,EAAE1B,MAAM;MACtBO,UAAU,EAAEA,UAAU;MACtBI,OAAO,EAAEA,OAAO;MAChBO,YAAY,EAAEA,YAAY,CAACpC,IAAI,CAAC,CAAC;MACjCqC,OAAO,EAAEA,OAAO;MAChBQ,eAAe,EAAEC,OAAO,CAACjB,OAAO;IAClC,CAAC;EACH;EAEAhC,cAAcA,CAACC,IAAI,EAAE;IACnB,MAAM5B,OAAO,GAAG,CAAC,CAAC;;IAElB;IACA4B,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC;;IAEvC;IACA,MAAMS,cAAc,GAAGX,IAAI,CAACY,KAAK,CAAC,iBAAiB,CAAC;IAEpD,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+B,cAAc,CAACjB,MAAM,EAAEd,CAAC,IAAI,CAAC,EAAE;MACjD,IAAIA,CAAC,GAAG,CAAC,GAAG+B,cAAc,CAACjB,MAAM,EAAE;QACjC,MAAMgC,WAAW,GAAGD,QAAQ,CAACd,cAAc,CAAC/B,CAAC,CAAC,CAAC;QAC/C,MAAMyC,OAAO,GAAGV,cAAc,CAAC/B,CAAC,GAAG,CAAC,CAAC;;QAErC;QACA,IAAIqE,aAAa,GAAG,IAAI;QACxB,MAAMC,cAAc,GAAG,CACrB,gCAAgC,EAChC,4BAA4B,EAC5B,oBAAoB,EACpB,uBAAuB,CACxB;QAED,KAAK,MAAM7C,OAAO,IAAI6C,cAAc,EAAE;UACpC,MAAMlC,KAAK,GAAGK,OAAO,CAACL,KAAK,CAACX,OAAO,CAAC;UACpC,IAAIW,KAAK,EAAE;YACTiC,aAAa,GAAGjC,KAAK,CAAC,CAAC,CAAC,CAACmC,WAAW,CAAC,CAAC;YACtC;UACF;QACF;;QAEA;QACA,IAAIC,WAAW,GAAG,EAAE;QACpB,MAAMC,mBAAmB,GAAG,CAC1B,0CAA0C,EAC1C,wCAAwC,CACzC;QAED,KAAK,MAAMhD,OAAO,IAAIgD,mBAAmB,EAAE;UACzC,MAAMrC,KAAK,GAAGK,OAAO,CAACL,KAAK,CAACX,OAAO,CAAC;UACpC,IAAIW,KAAK,EAAE;YACToC,WAAW,GAAGpC,KAAK,CAAC,CAAC,CAAC,CAACd,IAAI,CAAC,CAAC;YAC7B;UACF;QACF;QAEA,IAAI+C,aAAa,EAAE;UACjB7E,OAAO,CAACsD,WAAW,CAAC,GAAG;YACrBuB,aAAa,EAAEA,aAAa;YAC5BG,WAAW,EAAEA;UACf,CAAC;QACH;MACF;IACF;IAEA,OAAOhF,OAAO;EAChB;EAEA,MAAMkF,UAAUA,CAACC,YAAY,EAAEC,UAAU,EAAE;IACzC,IAAI;MACF,MAAM,CAACrF,SAAS,EAAEC,OAAO,CAAC,GAAG,MAAMqF,OAAO,CAACC,GAAG,CAAC,CAC7C,IAAI,CAACrF,iBAAiB,CAACkF,YAAY,CAAC,EACpC,IAAI,CAACzD,eAAe,CAAC0D,UAAU,CAAC,CACjC,CAAC;MAEF,OAAO;QAAErF,SAAS;QAAEC;MAAQ,CAAC;IAC/B,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF;AACF;AAEA,eAAe5B,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}