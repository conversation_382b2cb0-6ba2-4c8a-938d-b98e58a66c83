{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Ta\\u0300i lie\\u0323\\u0302u CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/CFAExamApp.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Container, Row, Col, Alert } from 'react-bootstrap';\nimport FileUploader from './FileUploader';\nimport ExamHeader from './ExamHeader';\nimport QuestionDisplay from './QuestionDisplay';\nimport NavigationControls from './NavigationControls';\nimport CFAQuestionParser from '../utils/pdfParser';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CFAExamApp = () => {\n  _s();\n  // State management\n  const [examState, setExamState] = useState('file-selection'); // 'file-selection', 'exam-loaded', 'exam-started'\n  const [questions, setQuestions] = useState([]);\n  const [answers, setAnswers] = useState({});\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [userAnswers, setUserAnswers] = useState({});\n  const [completedQuestions, setCompletedQuestions] = useState(new Set());\n  const [showAnswer, setShowAnswer] = useState({});\n  const [startTime, setStartTime] = useState(null);\n  const [elapsedTime, setElapsedTime] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [examInfo, setExamInfo] = useState(null);\n\n  // Timer effect\n  useEffect(() => {\n    let interval = null;\n    if (startTime && examState === 'exam-started') {\n      interval = setInterval(() => {\n        setElapsedTime(Date.now() - startTime);\n      }, 1000);\n    }\n    return () => {\n      if (interval) clearInterval(interval);\n    };\n  }, [startTime, examState]);\n\n  // Handle file upload and parsing\n  const handleFilesUploaded = async (questionFile, answerFile) => {\n    setLoading(true);\n    setError(null);\n    try {\n      const parser = new CFAQuestionParser();\n      const {\n        questions: parsedQuestions,\n        answers: parsedAnswers\n      } = await parser.parseFiles(questionFile, answerFile);\n      if (parsedQuestions.length === 0) {\n        throw new Error('Không tìm thấy câu hỏi nào trong file PDF');\n      }\n      setQuestions(parsedQuestions);\n      setAnswers(parsedAnswers);\n      setExamInfo({\n        questionFileName: questionFile.name,\n        answerFileName: answerFile.name,\n        totalQuestions: parsedQuestions.length\n      });\n      setExamState('exam-loaded');\n    } catch (err) {\n      setError(`Lỗi khi xử lý file: ${err.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Start exam\n  const startExam = () => {\n    setStartTime(Date.now());\n    setExamState('exam-started');\n    setCurrentQuestionIndex(0);\n  };\n\n  // Navigate to question\n  const goToQuestion = useCallback(index => {\n    if (index >= 0 && index < questions.length) {\n      setCurrentQuestionIndex(index);\n    }\n  }, [questions.length]);\n\n  // Submit answer\n  const submitAnswer = (questionNumber, selectedAnswer) => {\n    setUserAnswers(prev => ({\n      ...prev,\n      [questionNumber]: selectedAnswer\n    }));\n    setCompletedQuestions(prev => new Set([...prev, questionNumber]));\n    setShowAnswer(prev => ({\n      ...prev,\n      [questionNumber]: true\n    }));\n  };\n\n  // Get current question\n  const getCurrentQuestion = () => {\n    if (questions.length === 0 || currentQuestionIndex >= questions.length) {\n      return null;\n    }\n    return questions[currentQuestionIndex];\n  };\n\n  // Get questions in same group (for group questions)\n  const getGroupQuestions = question => {\n    if (!question || !question.isGroupQuestion) {\n      return [question];\n    }\n    return questions.filter(q => q.isGroupQuestion && q.context === question.context);\n  };\n\n  // Format time\n  const formatTime = milliseconds => {\n    const totalSeconds = Math.floor(milliseconds / 1000);\n    const hours = Math.floor(totalSeconds / 3600);\n    const minutes = Math.floor(totalSeconds % 3600 / 60);\n    const seconds = totalSeconds % 60;\n    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  };\n\n  // Keyboard navigation\n  useEffect(() => {\n    const handleKeyPress = e => {\n      if (examState !== 'exam-started') return;\n      if (e.key === 'ArrowLeft') {\n        goToQuestion(currentQuestionIndex - 1);\n      } else if (e.key === 'ArrowRight') {\n        goToQuestion(currentQuestionIndex + 1);\n      } else if (e.key >= '1' && e.key <= '5') {\n        const choices = ['A', 'B', 'C', 'D', 'E'];\n        const choice = choices[parseInt(e.key) - 1];\n        const currentQuestion = getCurrentQuestion();\n        if (currentQuestion && currentQuestion.choices[choice]) {\n          submitAnswer(currentQuestion.questionNumber, choice);\n        }\n      }\n    };\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, [examState, currentQuestionIndex, goToQuestion]);\n  const currentQuestion = getCurrentQuestion();\n  const groupQuestions = currentQuestion ? getGroupQuestions(currentQuestion) : [];\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    className: \"cfa-exam-app\",\n    children: [/*#__PURE__*/_jsxDEV(ExamHeader, {\n      examState: examState,\n      examInfo: examInfo,\n      currentQuestionNumber: (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.questionNumber) || 0,\n      totalQuestions: questions.length,\n      completedCount: completedQuestions.size,\n      elapsedTime: formatTime(elapsedTime)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mt-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          onClose: () => setError(null),\n          dismissible: true,\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mt-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [examState === 'file-selection' && /*#__PURE__*/_jsxDEV(FileUploader, {\n          onFilesUploaded: handleFilesUploaded,\n          loading: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this), examState === 'exam-loaded' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"S\\u1EB5n s\\xE0ng b\\u1EAFt \\u0111\\u1EA7u l\\xE0m b\\xE0i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\u0110\\xE3 t\\u1EA3i \", questions.length, \" c\\xE2u h\\u1ECFi t\\u1EEB \", examInfo === null || examInfo === void 0 ? void 0 : examInfo.questionFileName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary btn-lg\",\n            onClick: startExam,\n            children: \"B\\u1EAFt \\u0111\\u1EA7u l\\xE0m b\\xE0i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this), examState === 'exam-started' && currentQuestion && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(QuestionDisplay, {\n            question: currentQuestion,\n            groupQuestions: groupQuestions,\n            answers: answers,\n            userAnswer: userAnswers[currentQuestion.questionNumber],\n            showAnswer: showAnswer[currentQuestion.questionNumber],\n            onSubmitAnswer: submitAnswer\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(NavigationControls, {\n            currentIndex: currentQuestionIndex,\n            totalQuestions: questions.length,\n            onNavigate: goToQuestion,\n            questions: questions,\n            completedQuestions: completedQuestions,\n            userAnswers: userAnswers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n};\n_s(CFAExamApp, \"S5quxUyWLmGNnbLX3K6dtScqFfE=\");\n_c = CFAExamApp;\nexport default CFAExamApp;\nvar _c;\n$RefreshReg$(_c, \"CFAExamApp\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Container", "Row", "Col", "<PERSON><PERSON>", "FileUploader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "QuestionDisplay", "NavigationControls", "CFAQuestionParser", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CFAExamApp", "_s", "examState", "setExamState", "questions", "setQuestions", "answers", "setAnswers", "currentQuestionIndex", "setCurrentQuestionIndex", "userAnswers", "setUserAnswers", "completedQuestions", "setCompletedQuestions", "Set", "showAnswer", "setShowAnswer", "startTime", "setStartTime", "elapsedTime", "setElapsedTime", "loading", "setLoading", "error", "setError", "examInfo", "setExamInfo", "interval", "setInterval", "Date", "now", "clearInterval", "handleFilesUploaded", "questionFile", "answerFile", "parser", "parsedQuestions", "parsedAnswers", "parseFiles", "length", "Error", "questionFileName", "name", "answerFileName", "totalQuestions", "err", "message", "startExam", "goToQuestion", "index", "submitAnswer", "questionNumber", "<PERSON><PERSON><PERSON><PERSON>", "prev", "getCurrentQuestion", "getGroupQuestions", "question", "isGroupQuestion", "filter", "q", "context", "formatTime", "milliseconds", "totalSeconds", "Math", "floor", "hours", "minutes", "seconds", "toString", "padStart", "handleKeyPress", "e", "key", "choices", "choice", "parseInt", "currentQuestion", "window", "addEventListener", "removeEventListener", "groupQuestions", "fluid", "className", "children", "currentQuestionNumber", "completedCount", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClose", "dismissible", "onFilesUploaded", "onClick", "userAnswer", "onSubmitAnswer", "currentIndex", "onNavigate", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/CFAExamApp.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { Container, Row, Col, Alert } from 'react-bootstrap';\nimport FileUploader from './FileUploader';\nimport ExamHeader from './ExamHeader';\nimport QuestionDisplay from './QuestionDisplay';\nimport NavigationControls from './NavigationControls';\nimport CFAQuestionParser from '../utils/pdfParser';\n\nconst CFAExamApp = () => {\n  // State management\n  const [examState, setExamState] = useState('file-selection'); // 'file-selection', 'exam-loaded', 'exam-started'\n  const [questions, setQuestions] = useState([]);\n  const [answers, setAnswers] = useState({});\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [userAnswers, setUserAnswers] = useState({});\n  const [completedQuestions, setCompletedQuestions] = useState(new Set());\n  const [showAnswer, setShowAnswer] = useState({});\n  const [startTime, setStartTime] = useState(null);\n  const [elapsedTime, setElapsedTime] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [examInfo, setExamInfo] = useState(null);\n\n  // Timer effect\n  useEffect(() => {\n    let interval = null;\n    if (startTime && examState === 'exam-started') {\n      interval = setInterval(() => {\n        setElapsedTime(Date.now() - startTime);\n      }, 1000);\n    }\n    return () => {\n      if (interval) clearInterval(interval);\n    };\n  }, [startTime, examState]);\n\n  // Handle file upload and parsing\n  const handleFilesUploaded = async (questionFile, answerFile) => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const parser = new CFAQuestionParser();\n      const { questions: parsedQuestions, answers: parsedAnswers } = await parser.parseFiles(questionFile, answerFile);\n      \n      if (parsedQuestions.length === 0) {\n        throw new Error('Không tìm thấy câu hỏi nào trong file PDF');\n      }\n      \n      setQuestions(parsedQuestions);\n      setAnswers(parsedAnswers);\n      setExamInfo({\n        questionFileName: questionFile.name,\n        answerFileName: answerFile.name,\n        totalQuestions: parsedQuestions.length\n      });\n      setExamState('exam-loaded');\n      \n    } catch (err) {\n      setError(`Lỗi khi xử lý file: ${err.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Start exam\n  const startExam = () => {\n    setStartTime(Date.now());\n    setExamState('exam-started');\n    setCurrentQuestionIndex(0);\n  };\n\n  // Navigate to question\n  const goToQuestion = useCallback((index) => {\n    if (index >= 0 && index < questions.length) {\n      setCurrentQuestionIndex(index);\n    }\n  }, [questions.length]);\n\n  // Submit answer\n  const submitAnswer = (questionNumber, selectedAnswer) => {\n    setUserAnswers(prev => ({\n      ...prev,\n      [questionNumber]: selectedAnswer\n    }));\n    \n    setCompletedQuestions(prev => new Set([...prev, questionNumber]));\n    \n    setShowAnswer(prev => ({\n      ...prev,\n      [questionNumber]: true\n    }));\n  };\n\n  // Get current question\n  const getCurrentQuestion = () => {\n    if (questions.length === 0 || currentQuestionIndex >= questions.length) {\n      return null;\n    }\n    return questions[currentQuestionIndex];\n  };\n\n  // Get questions in same group (for group questions)\n  const getGroupQuestions = (question) => {\n    if (!question || !question.isGroupQuestion) {\n      return [question];\n    }\n    \n    return questions.filter(q => \n      q.isGroupQuestion && \n      q.context === question.context\n    );\n  };\n\n  // Format time\n  const formatTime = (milliseconds) => {\n    const totalSeconds = Math.floor(milliseconds / 1000);\n    const hours = Math.floor(totalSeconds / 3600);\n    const minutes = Math.floor((totalSeconds % 3600) / 60);\n    const seconds = totalSeconds % 60;\n    \n    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  };\n\n  // Keyboard navigation\n  useEffect(() => {\n    const handleKeyPress = (e) => {\n      if (examState !== 'exam-started') return;\n      \n      if (e.key === 'ArrowLeft') {\n        goToQuestion(currentQuestionIndex - 1);\n      } else if (e.key === 'ArrowRight') {\n        goToQuestion(currentQuestionIndex + 1);\n      } else if (e.key >= '1' && e.key <= '5') {\n        const choices = ['A', 'B', 'C', 'D', 'E'];\n        const choice = choices[parseInt(e.key) - 1];\n        const currentQuestion = getCurrentQuestion();\n        if (currentQuestion && currentQuestion.choices[choice]) {\n          submitAnswer(currentQuestion.questionNumber, choice);\n        }\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, [examState, currentQuestionIndex, goToQuestion]);\n\n  const currentQuestion = getCurrentQuestion();\n  const groupQuestions = currentQuestion ? getGroupQuestions(currentQuestion) : [];\n\n  return (\n    <Container fluid className=\"cfa-exam-app\">\n      {/* Header */}\n      <ExamHeader \n        examState={examState}\n        examInfo={examInfo}\n        currentQuestionNumber={currentQuestion?.questionNumber || 0}\n        totalQuestions={questions.length}\n        completedCount={completedQuestions.size}\n        elapsedTime={formatTime(elapsedTime)}\n      />\n\n      {/* Error Alert */}\n      {error && (\n        <Row className=\"mt-3\">\n          <Col>\n            <Alert variant=\"danger\" onClose={() => setError(null)} dismissible>\n              {error}\n            </Alert>\n          </Col>\n        </Row>\n      )}\n\n      {/* Main Content */}\n      <Row className=\"mt-3\">\n        <Col>\n          {examState === 'file-selection' && (\n            <FileUploader \n              onFilesUploaded={handleFilesUploaded}\n              loading={loading}\n            />\n          )}\n\n          {examState === 'exam-loaded' && (\n            <div className=\"text-center\">\n              <h3>Sẵn sàng bắt đầu làm bài</h3>\n              <p>Đã tải {questions.length} câu hỏi từ {examInfo?.questionFileName}</p>\n              <button \n                className=\"btn btn-primary btn-lg\"\n                onClick={startExam}\n              >\n                Bắt đầu làm bài\n              </button>\n            </div>\n          )}\n\n          {examState === 'exam-started' && currentQuestion && (\n            <>\n              <QuestionDisplay\n                question={currentQuestion}\n                groupQuestions={groupQuestions}\n                answers={answers}\n                userAnswer={userAnswers[currentQuestion.questionNumber]}\n                showAnswer={showAnswer[currentQuestion.questionNumber]}\n                onSubmitAnswer={submitAnswer}\n              />\n              \n              <NavigationControls\n                currentIndex={currentQuestionIndex}\n                totalQuestions={questions.length}\n                onNavigate={goToQuestion}\n                questions={questions}\n                completedQuestions={completedQuestions}\n                userAnswers={userAnswers}\n              />\n            </>\n          )}\n        </Col>\n      </Row>\n    </Container>\n  );\n};\n\nexport default CFAExamApp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,KAAK,QAAQ,iBAAiB;AAC5D,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,iBAAiB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnD,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACwB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC4B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7B,QAAQ,CAAC,IAAI8B,GAAG,CAAC,CAAC,CAAC;EACvE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACAC,SAAS,CAAC,MAAM;IACd,IAAI0C,QAAQ,GAAG,IAAI;IACnB,IAAIV,SAAS,IAAIf,SAAS,KAAK,cAAc,EAAE;MAC7CyB,QAAQ,GAAGC,WAAW,CAAC,MAAM;QAC3BR,cAAc,CAACS,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGb,SAAS,CAAC;MACxC,CAAC,EAAE,IAAI,CAAC;IACV;IACA,OAAO,MAAM;MACX,IAAIU,QAAQ,EAAEI,aAAa,CAACJ,QAAQ,CAAC;IACvC,CAAC;EACH,CAAC,EAAE,CAACV,SAAS,EAAEf,SAAS,CAAC,CAAC;;EAE1B;EACA,MAAM8B,mBAAmB,GAAG,MAAAA,CAAOC,YAAY,EAAEC,UAAU,KAAK;IAC9DZ,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMW,MAAM,GAAG,IAAIxC,iBAAiB,CAAC,CAAC;MACtC,MAAM;QAAES,SAAS,EAAEgC,eAAe;QAAE9B,OAAO,EAAE+B;MAAc,CAAC,GAAG,MAAMF,MAAM,CAACG,UAAU,CAACL,YAAY,EAAEC,UAAU,CAAC;MAEhH,IAAIE,eAAe,CAACG,MAAM,KAAK,CAAC,EAAE;QAChC,MAAM,IAAIC,KAAK,CAAC,2CAA2C,CAAC;MAC9D;MAEAnC,YAAY,CAAC+B,eAAe,CAAC;MAC7B7B,UAAU,CAAC8B,aAAa,CAAC;MACzBX,WAAW,CAAC;QACVe,gBAAgB,EAAER,YAAY,CAACS,IAAI;QACnCC,cAAc,EAAET,UAAU,CAACQ,IAAI;QAC/BE,cAAc,EAAER,eAAe,CAACG;MAClC,CAAC,CAAC;MACFpC,YAAY,CAAC,aAAa,CAAC;IAE7B,CAAC,CAAC,OAAO0C,GAAG,EAAE;MACZrB,QAAQ,CAAC,uBAAuBqB,GAAG,CAACC,OAAO,EAAE,CAAC;IAChD,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyB,SAAS,GAAGA,CAAA,KAAM;IACtB7B,YAAY,CAACW,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IACxB3B,YAAY,CAAC,cAAc,CAAC;IAC5BM,uBAAuB,CAAC,CAAC,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMuC,YAAY,GAAG9D,WAAW,CAAE+D,KAAK,IAAK;IAC1C,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG7C,SAAS,CAACmC,MAAM,EAAE;MAC1C9B,uBAAuB,CAACwC,KAAK,CAAC;IAChC;EACF,CAAC,EAAE,CAAC7C,SAAS,CAACmC,MAAM,CAAC,CAAC;;EAEtB;EACA,MAAMW,YAAY,GAAGA,CAACC,cAAc,EAAEC,cAAc,KAAK;IACvDzC,cAAc,CAAC0C,IAAI,KAAK;MACtB,GAAGA,IAAI;MACP,CAACF,cAAc,GAAGC;IACpB,CAAC,CAAC,CAAC;IAEHvC,qBAAqB,CAACwC,IAAI,IAAI,IAAIvC,GAAG,CAAC,CAAC,GAAGuC,IAAI,EAAEF,cAAc,CAAC,CAAC,CAAC;IAEjEnC,aAAa,CAACqC,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACF,cAAc,GAAG;IACpB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIlD,SAAS,CAACmC,MAAM,KAAK,CAAC,IAAI/B,oBAAoB,IAAIJ,SAAS,CAACmC,MAAM,EAAE;MACtE,OAAO,IAAI;IACb;IACA,OAAOnC,SAAS,CAACI,oBAAoB,CAAC;EACxC,CAAC;;EAED;EACA,MAAM+C,iBAAiB,GAAIC,QAAQ,IAAK;IACtC,IAAI,CAACA,QAAQ,IAAI,CAACA,QAAQ,CAACC,eAAe,EAAE;MAC1C,OAAO,CAACD,QAAQ,CAAC;IACnB;IAEA,OAAOpD,SAAS,CAACsD,MAAM,CAACC,CAAC,IACvBA,CAAC,CAACF,eAAe,IACjBE,CAAC,CAACC,OAAO,KAAKJ,QAAQ,CAACI,OACzB,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIC,YAAY,IAAK;IACnC,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,IAAI,CAAC;IACpD,MAAMI,KAAK,GAAGF,IAAI,CAACC,KAAK,CAACF,YAAY,GAAG,IAAI,CAAC;IAC7C,MAAMI,OAAO,GAAGH,IAAI,CAACC,KAAK,CAAEF,YAAY,GAAG,IAAI,GAAI,EAAE,CAAC;IACtD,MAAMK,OAAO,GAAGL,YAAY,GAAG,EAAE;IAEjC,OAAO,GAAGG,KAAK,CAACG,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC7H,CAAC;;EAED;EACArF,SAAS,CAAC,MAAM;IACd,MAAMsF,cAAc,GAAIC,CAAC,IAAK;MAC5B,IAAItE,SAAS,KAAK,cAAc,EAAE;MAElC,IAAIsE,CAAC,CAACC,GAAG,KAAK,WAAW,EAAE;QACzBzB,YAAY,CAACxC,oBAAoB,GAAG,CAAC,CAAC;MACxC,CAAC,MAAM,IAAIgE,CAAC,CAACC,GAAG,KAAK,YAAY,EAAE;QACjCzB,YAAY,CAACxC,oBAAoB,GAAG,CAAC,CAAC;MACxC,CAAC,MAAM,IAAIgE,CAAC,CAACC,GAAG,IAAI,GAAG,IAAID,CAAC,CAACC,GAAG,IAAI,GAAG,EAAE;QACvC,MAAMC,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QACzC,MAAMC,MAAM,GAAGD,OAAO,CAACE,QAAQ,CAACJ,CAAC,CAACC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3C,MAAMI,eAAe,GAAGvB,kBAAkB,CAAC,CAAC;QAC5C,IAAIuB,eAAe,IAAIA,eAAe,CAACH,OAAO,CAACC,MAAM,CAAC,EAAE;UACtDzB,YAAY,CAAC2B,eAAe,CAAC1B,cAAc,EAAEwB,MAAM,CAAC;QACtD;MACF;IACF,CAAC;IAEDG,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAER,cAAc,CAAC;IAClD,OAAO,MAAMO,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAET,cAAc,CAAC;EACpE,CAAC,EAAE,CAACrE,SAAS,EAAEM,oBAAoB,EAAEwC,YAAY,CAAC,CAAC;EAEnD,MAAM6B,eAAe,GAAGvB,kBAAkB,CAAC,CAAC;EAC5C,MAAM2B,cAAc,GAAGJ,eAAe,GAAGtB,iBAAiB,CAACsB,eAAe,CAAC,GAAG,EAAE;EAEhF,oBACEhF,OAAA,CAACV,SAAS;IAAC+F,KAAK;IAACC,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAEvCvF,OAAA,CAACL,UAAU;MACTU,SAAS,EAAEA,SAAU;MACrBuB,QAAQ,EAAEA,QAAS;MACnB4D,qBAAqB,EAAE,CAAAR,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE1B,cAAc,KAAI,CAAE;MAC5DP,cAAc,EAAExC,SAAS,CAACmC,MAAO;MACjC+C,cAAc,EAAE1E,kBAAkB,CAAC2E,IAAK;MACxCpE,WAAW,EAAE0C,UAAU,CAAC1C,WAAW;IAAE;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,EAGDpE,KAAK,iBACJ1B,OAAA,CAACT,GAAG;MAAC+F,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBvF,OAAA,CAACR,GAAG;QAAA+F,QAAA,eACFvF,OAAA,CAACP,KAAK;UAACsG,OAAO,EAAC,QAAQ;UAACC,OAAO,EAAEA,CAAA,KAAMrE,QAAQ,CAAC,IAAI,CAAE;UAACsE,WAAW;UAAAV,QAAA,EAC/D7D;QAAK;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD9F,OAAA,CAACT,GAAG;MAAC+F,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBvF,OAAA,CAACR,GAAG;QAAA+F,QAAA,GACDlF,SAAS,KAAK,gBAAgB,iBAC7BL,OAAA,CAACN,YAAY;UACXwG,eAAe,EAAE/D,mBAAoB;UACrCX,OAAO,EAAEA;QAAQ;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CACF,EAEAzF,SAAS,KAAK,aAAa,iBAC1BL,OAAA;UAAKsF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BvF,OAAA;YAAAuF,QAAA,EAAI;UAAwB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjC9F,OAAA;YAAAuF,QAAA,GAAG,sBAAO,EAAChF,SAAS,CAACmC,MAAM,EAAC,2BAAY,EAACd,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEgB,gBAAgB;UAAA;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxE9F,OAAA;YACEsF,SAAS,EAAC,wBAAwB;YAClCa,OAAO,EAAEjD,SAAU;YAAAqC,QAAA,EACpB;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAEAzF,SAAS,KAAK,cAAc,IAAI2E,eAAe,iBAC9ChF,OAAA,CAAAE,SAAA;UAAAqF,QAAA,gBACEvF,OAAA,CAACJ,eAAe;YACd+D,QAAQ,EAAEqB,eAAgB;YAC1BI,cAAc,EAAEA,cAAe;YAC/B3E,OAAO,EAAEA,OAAQ;YACjB2F,UAAU,EAAEvF,WAAW,CAACmE,eAAe,CAAC1B,cAAc,CAAE;YACxDpC,UAAU,EAAEA,UAAU,CAAC8D,eAAe,CAAC1B,cAAc,CAAE;YACvD+C,cAAc,EAAEhD;UAAa;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eAEF9F,OAAA,CAACH,kBAAkB;YACjByG,YAAY,EAAE3F,oBAAqB;YACnCoC,cAAc,EAAExC,SAAS,CAACmC,MAAO;YACjC6D,UAAU,EAAEpD,YAAa;YACzB5C,SAAS,EAAEA,SAAU;YACrBQ,kBAAkB,EAAEA,kBAAmB;YACvCF,WAAW,EAAEA;UAAY;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA,eACF,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAAC1F,EAAA,CArNID,UAAU;AAAAqG,EAAA,GAAVrG,UAAU;AAuNhB,eAAeA,UAAU;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}