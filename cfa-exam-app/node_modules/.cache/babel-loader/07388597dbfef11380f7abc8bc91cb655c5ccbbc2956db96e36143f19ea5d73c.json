{"ast": null, "code": "import addClass from 'dom-helpers/addClass';\nimport css from 'dom-helpers/css';\nimport qsa from 'dom-helpers/querySelectorAll';\nimport removeClass from 'dom-helpers/removeClass';\nimport ModalManager from '@restart/ui/ModalManager';\nconst Selector = {\n  FIXED_CONTENT: '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top',\n  STICKY_CONTENT: '.sticky-top',\n  NAVBAR_TOGGLER: '.navbar-toggler'\n};\nclass BootstrapModalManager extends ModalManager {\n  adjustAndStore(prop, element, adjust) {\n    const actual = element.style[prop];\n    // @ts-expect-error TODO: DOMStringMap and CSSStyleDeclaration aren't strictly compatible\n    element.dataset[prop] = actual;\n    css(element, {\n      [prop]: `${parseFloat(css(element, prop)) + adjust}px`\n    });\n  }\n  restore(prop, element) {\n    const value = element.dataset[prop];\n    if (value !== undefined) {\n      delete element.dataset[prop];\n      css(element, {\n        [prop]: value\n      });\n    }\n  }\n  setContainerStyle(containerState) {\n    super.setContainerStyle(containerState);\n    const container = this.getElement();\n    addClass(container, 'modal-open');\n    if (!containerState.scrollBarWidth) return;\n    const paddingProp = this.isRTL ? 'paddingLeft' : 'paddingRight';\n    const marginProp = this.isRTL ? 'marginLeft' : 'marginRight';\n    qsa(container, Selector.FIXED_CONTENT).forEach(el => this.adjustAndStore(paddingProp, el, containerState.scrollBarWidth));\n    qsa(container, Selector.STICKY_CONTENT).forEach(el => this.adjustAndStore(marginProp, el, -containerState.scrollBarWidth));\n    qsa(container, Selector.NAVBAR_TOGGLER).forEach(el => this.adjustAndStore(marginProp, el, containerState.scrollBarWidth));\n  }\n  removeContainerStyle(containerState) {\n    super.removeContainerStyle(containerState);\n    const container = this.getElement();\n    removeClass(container, 'modal-open');\n    const paddingProp = this.isRTL ? 'paddingLeft' : 'paddingRight';\n    const marginProp = this.isRTL ? 'marginLeft' : 'marginRight';\n    qsa(container, Selector.FIXED_CONTENT).forEach(el => this.restore(paddingProp, el));\n    qsa(container, Selector.STICKY_CONTENT).forEach(el => this.restore(marginProp, el));\n    qsa(container, Selector.NAVBAR_TOGGLER).forEach(el => this.restore(marginProp, el));\n  }\n}\nlet sharedManager;\nexport function getSharedManager(options) {\n  if (!sharedManager) sharedManager = new BootstrapModalManager(options);\n  return sharedManager;\n}\nexport default BootstrapModalManager;", "map": {"version": 3, "names": ["addClass", "css", "qsa", "removeClass", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Selector", "FIXED_CONTENT", "STICKY_CONTENT", "NAVBAR_TOGGLER", "BootstrapModalManager", "adjustAndStore", "prop", "element", "adjust", "actual", "style", "dataset", "parseFloat", "restore", "value", "undefined", "setContainerStyle", "containerState", "container", "getElement", "scrollBarWidth", "paddingProp", "isRTL", "marginProp", "for<PERSON>ach", "el", "removeContainerStyle", "sharedManager", "getSharedManager", "options"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/react-bootstrap/esm/BootstrapModalManager.js"], "sourcesContent": ["import addClass from 'dom-helpers/addClass';\nimport css from 'dom-helpers/css';\nimport qsa from 'dom-helpers/querySelectorAll';\nimport removeClass from 'dom-helpers/removeClass';\nimport ModalManager from '@restart/ui/ModalManager';\nconst Selector = {\n  FIXED_CONTENT: '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top',\n  STICKY_CONTENT: '.sticky-top',\n  NAVBAR_TOGGLER: '.navbar-toggler'\n};\nclass BootstrapModalManager extends ModalManager {\n  adjustAndStore(prop, element, adjust) {\n    const actual = element.style[prop];\n    // @ts-expect-error TODO: DOMStringMap and CSSStyleDeclaration aren't strictly compatible\n    element.dataset[prop] = actual;\n    css(element, {\n      [prop]: `${parseFloat(css(element, prop)) + adjust}px`\n    });\n  }\n  restore(prop, element) {\n    const value = element.dataset[prop];\n    if (value !== undefined) {\n      delete element.dataset[prop];\n      css(element, {\n        [prop]: value\n      });\n    }\n  }\n  setContainerStyle(containerState) {\n    super.setContainerStyle(containerState);\n    const container = this.getElement();\n    addClass(container, 'modal-open');\n    if (!containerState.scrollBarWidth) return;\n    const paddingProp = this.isRTL ? 'paddingLeft' : 'paddingRight';\n    const marginProp = this.isRTL ? 'marginLeft' : 'marginRight';\n    qsa(container, Selector.FIXED_CONTENT).forEach(el => this.adjustAndStore(paddingProp, el, containerState.scrollBarWidth));\n    qsa(container, Selector.STICKY_CONTENT).forEach(el => this.adjustAndStore(marginProp, el, -containerState.scrollBarWidth));\n    qsa(container, Selector.NAVBAR_TOGGLER).forEach(el => this.adjustAndStore(marginProp, el, containerState.scrollBarWidth));\n  }\n  removeContainerStyle(containerState) {\n    super.removeContainerStyle(containerState);\n    const container = this.getElement();\n    removeClass(container, 'modal-open');\n    const paddingProp = this.isRTL ? 'paddingLeft' : 'paddingRight';\n    const marginProp = this.isRTL ? 'marginLeft' : 'marginRight';\n    qsa(container, Selector.FIXED_CONTENT).forEach(el => this.restore(paddingProp, el));\n    qsa(container, Selector.STICKY_CONTENT).forEach(el => this.restore(marginProp, el));\n    qsa(container, Selector.NAVBAR_TOGGLER).forEach(el => this.restore(marginProp, el));\n  }\n}\nlet sharedManager;\nexport function getSharedManager(options) {\n  if (!sharedManager) sharedManager = new BootstrapModalManager(options);\n  return sharedManager;\n}\nexport default BootstrapModalManager;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,GAAG,MAAM,iBAAiB;AACjC,OAAOC,GAAG,MAAM,8BAA8B;AAC9C,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,YAAY,MAAM,0BAA0B;AACnD,MAAMC,QAAQ,GAAG;EACfC,aAAa,EAAE,mDAAmD;EAClEC,cAAc,EAAE,aAAa;EAC7BC,cAAc,EAAE;AAClB,CAAC;AACD,MAAMC,qBAAqB,SAASL,YAAY,CAAC;EAC/CM,cAAcA,CAACC,IAAI,EAAEC,OAAO,EAAEC,MAAM,EAAE;IACpC,MAAMC,MAAM,GAAGF,OAAO,CAACG,KAAK,CAACJ,IAAI,CAAC;IAClC;IACAC,OAAO,CAACI,OAAO,CAACL,IAAI,CAAC,GAAGG,MAAM;IAC9Bb,GAAG,CAACW,OAAO,EAAE;MACX,CAACD,IAAI,GAAG,GAAGM,UAAU,CAAChB,GAAG,CAACW,OAAO,EAAED,IAAI,CAAC,CAAC,GAAGE,MAAM;IACpD,CAAC,CAAC;EACJ;EACAK,OAAOA,CAACP,IAAI,EAAEC,OAAO,EAAE;IACrB,MAAMO,KAAK,GAAGP,OAAO,CAACI,OAAO,CAACL,IAAI,CAAC;IACnC,IAAIQ,KAAK,KAAKC,SAAS,EAAE;MACvB,OAAOR,OAAO,CAACI,OAAO,CAACL,IAAI,CAAC;MAC5BV,GAAG,CAACW,OAAO,EAAE;QACX,CAACD,IAAI,GAAGQ;MACV,CAAC,CAAC;IACJ;EACF;EACAE,iBAAiBA,CAACC,cAAc,EAAE;IAChC,KAAK,CAACD,iBAAiB,CAACC,cAAc,CAAC;IACvC,MAAMC,SAAS,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IACnCxB,QAAQ,CAACuB,SAAS,EAAE,YAAY,CAAC;IACjC,IAAI,CAACD,cAAc,CAACG,cAAc,EAAE;IACpC,MAAMC,WAAW,GAAG,IAAI,CAACC,KAAK,GAAG,aAAa,GAAG,cAAc;IAC/D,MAAMC,UAAU,GAAG,IAAI,CAACD,KAAK,GAAG,YAAY,GAAG,aAAa;IAC5DzB,GAAG,CAACqB,SAAS,EAAElB,QAAQ,CAACC,aAAa,CAAC,CAACuB,OAAO,CAACC,EAAE,IAAI,IAAI,CAACpB,cAAc,CAACgB,WAAW,EAAEI,EAAE,EAAER,cAAc,CAACG,cAAc,CAAC,CAAC;IACzHvB,GAAG,CAACqB,SAAS,EAAElB,QAAQ,CAACE,cAAc,CAAC,CAACsB,OAAO,CAACC,EAAE,IAAI,IAAI,CAACpB,cAAc,CAACkB,UAAU,EAAEE,EAAE,EAAE,CAACR,cAAc,CAACG,cAAc,CAAC,CAAC;IAC1HvB,GAAG,CAACqB,SAAS,EAAElB,QAAQ,CAACG,cAAc,CAAC,CAACqB,OAAO,CAACC,EAAE,IAAI,IAAI,CAACpB,cAAc,CAACkB,UAAU,EAAEE,EAAE,EAAER,cAAc,CAACG,cAAc,CAAC,CAAC;EAC3H;EACAM,oBAAoBA,CAACT,cAAc,EAAE;IACnC,KAAK,CAACS,oBAAoB,CAACT,cAAc,CAAC;IAC1C,MAAMC,SAAS,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IACnCrB,WAAW,CAACoB,SAAS,EAAE,YAAY,CAAC;IACpC,MAAMG,WAAW,GAAG,IAAI,CAACC,KAAK,GAAG,aAAa,GAAG,cAAc;IAC/D,MAAMC,UAAU,GAAG,IAAI,CAACD,KAAK,GAAG,YAAY,GAAG,aAAa;IAC5DzB,GAAG,CAACqB,SAAS,EAAElB,QAAQ,CAACC,aAAa,CAAC,CAACuB,OAAO,CAACC,EAAE,IAAI,IAAI,CAACZ,OAAO,CAACQ,WAAW,EAAEI,EAAE,CAAC,CAAC;IACnF5B,GAAG,CAACqB,SAAS,EAAElB,QAAQ,CAACE,cAAc,CAAC,CAACsB,OAAO,CAACC,EAAE,IAAI,IAAI,CAACZ,OAAO,CAACU,UAAU,EAAEE,EAAE,CAAC,CAAC;IACnF5B,GAAG,CAACqB,SAAS,EAAElB,QAAQ,CAACG,cAAc,CAAC,CAACqB,OAAO,CAACC,EAAE,IAAI,IAAI,CAACZ,OAAO,CAACU,UAAU,EAAEE,EAAE,CAAC,CAAC;EACrF;AACF;AACA,IAAIE,aAAa;AACjB,OAAO,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EACxC,IAAI,CAACF,aAAa,EAAEA,aAAa,GAAG,IAAIvB,qBAAqB,CAACyB,OAAO,CAAC;EACtE,OAAOF,aAAa;AACtB;AACA,eAAevB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}