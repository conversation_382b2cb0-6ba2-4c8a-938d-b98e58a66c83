{"ast": null, "code": "import classNames from 'classnames';\nimport css from 'dom-helpers/css';\nimport React, { useMemo } from 'react';\nimport { ENTERED, ENTERING, EXITED, EXITING } from 'react-transition-group/Transition';\nimport { getChildRef } from '@restart/ui/utils';\nimport transitionEndListener from './transitionEndListener';\nimport createChainedFunction from './createChainedFunction';\nimport triggerBrowserReflow from './triggerBrowserReflow';\nimport TransitionWrapper from './TransitionWrapper';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst MARGINS = {\n  height: ['marginTop', 'marginBottom'],\n  width: ['marginLeft', 'marginRight']\n};\nfunction getDefaultDimensionValue(dimension, elem) {\n  const offset = `offset${dimension[0].toUpperCase()}${dimension.slice(1)}`;\n  const value = elem[offset];\n  const margins = MARGINS[dimension];\n  return value +\n  // @ts-expect-error TODO\n  parseInt(css(elem, margins[0]), 10) +\n  // @ts-expect-error TODO\n  parseInt(css(elem, margins[1]), 10);\n}\nconst collapseStyles = {\n  [EXITED]: 'collapse',\n  [EXITING]: 'collapsing',\n  [ENTERING]: 'collapsing',\n  [ENTERED]: 'collapse show'\n};\nconst Collapse = /*#__PURE__*/React.forwardRef(({\n  onEnter,\n  onEntering,\n  onEntered,\n  onExit,\n  onExiting,\n  className,\n  children,\n  dimension = 'height',\n  in: inProp = false,\n  timeout = 300,\n  mountOnEnter = false,\n  unmountOnExit = false,\n  appear = false,\n  getDimensionValue = getDefaultDimensionValue,\n  ...props\n}, ref) => {\n  /* Compute dimension */\n  const computedDimension = typeof dimension === 'function' ? dimension() : dimension;\n\n  /* -- Expanding -- */\n  const handleEnter = useMemo(() => createChainedFunction(elem => {\n    elem.style[computedDimension] = '0';\n  }, onEnter), [computedDimension, onEnter]);\n  const handleEntering = useMemo(() => createChainedFunction(elem => {\n    const scroll = `scroll${computedDimension[0].toUpperCase()}${computedDimension.slice(1)}`;\n    elem.style[computedDimension] = `${elem[scroll]}px`;\n  }, onEntering), [computedDimension, onEntering]);\n  const handleEntered = useMemo(() => createChainedFunction(elem => {\n    elem.style[computedDimension] = null;\n  }, onEntered), [computedDimension, onEntered]);\n\n  /* -- Collapsing -- */\n  const handleExit = useMemo(() => createChainedFunction(elem => {\n    elem.style[computedDimension] = `${getDimensionValue(computedDimension, elem)}px`;\n    triggerBrowserReflow(elem);\n  }, onExit), [onExit, getDimensionValue, computedDimension]);\n  const handleExiting = useMemo(() => createChainedFunction(elem => {\n    elem.style[computedDimension] = null;\n  }, onExiting), [computedDimension, onExiting]);\n  return /*#__PURE__*/_jsx(TransitionWrapper, {\n    ref: ref,\n    addEndListener: transitionEndListener,\n    ...props,\n    \"aria-expanded\": props.role ? inProp : null,\n    onEnter: handleEnter,\n    onEntering: handleEntering,\n    onEntered: handleEntered,\n    onExit: handleExit,\n    onExiting: handleExiting,\n    childRef: getChildRef(children),\n    in: inProp,\n    timeout: timeout,\n    mountOnEnter: mountOnEnter,\n    unmountOnExit: unmountOnExit,\n    appear: appear,\n    children: (state, innerProps) => /*#__PURE__*/React.cloneElement(children, {\n      ...innerProps,\n      className: classNames(className, children.props.className, collapseStyles[state], computedDimension === 'width' && 'collapse-horizontal')\n    })\n  });\n});\nCollapse.displayName = 'Collapse';\nexport default Collapse;", "map": {"version": 3, "names": ["classNames", "css", "React", "useMemo", "ENTERED", "ENTERING", "EXITED", "EXITING", "getChildRef", "transitionEndListener", "createChainedFunction", "triggerBrowserReflow", "TransitionWrapper", "jsx", "_jsx", "MARGINS", "height", "width", "getDefaultDimensionValue", "dimension", "elem", "offset", "toUpperCase", "slice", "value", "margins", "parseInt", "collapseStyles", "Collapse", "forwardRef", "onEnter", "onEntering", "onEntered", "onExit", "onExiting", "className", "children", "in", "inProp", "timeout", "mountOnEnter", "unmountOnExit", "appear", "getDimensionValue", "props", "ref", "computedDimension", "handleEnter", "style", "handleEntering", "scroll", "handleEntered", "handleExit", "handleExiting", "addEndListener", "role", "childRef", "state", "innerProps", "cloneElement", "displayName"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/react-bootstrap/esm/Collapse.js"], "sourcesContent": ["import classNames from 'classnames';\nimport css from 'dom-helpers/css';\nimport React, { useMemo } from 'react';\nimport { ENTERED, ENTERING, EXITED, EXITING } from 'react-transition-group/Transition';\nimport { getChildRef } from '@restart/ui/utils';\nimport transitionEndListener from './transitionEndListener';\nimport createChainedFunction from './createChainedFunction';\nimport triggerBrowserReflow from './triggerBrowserReflow';\nimport TransitionWrapper from './TransitionWrapper';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst MARGINS = {\n  height: ['marginTop', 'marginBottom'],\n  width: ['marginLeft', 'marginRight']\n};\nfunction getDefaultDimensionValue(dimension, elem) {\n  const offset = `offset${dimension[0].toUpperCase()}${dimension.slice(1)}`;\n  const value = elem[offset];\n  const margins = MARGINS[dimension];\n  return value +\n  // @ts-expect-error TODO\n  parseInt(css(elem, margins[0]), 10) +\n  // @ts-expect-error TODO\n  parseInt(css(elem, margins[1]), 10);\n}\nconst collapseStyles = {\n  [EXITED]: 'collapse',\n  [EXITING]: 'collapsing',\n  [ENTERING]: 'collapsing',\n  [ENTERED]: 'collapse show'\n};\nconst Collapse = /*#__PURE__*/React.forwardRef(({\n  onEnter,\n  onEntering,\n  onEntered,\n  onExit,\n  onExiting,\n  className,\n  children,\n  dimension = 'height',\n  in: inProp = false,\n  timeout = 300,\n  mountOnEnter = false,\n  unmountOnExit = false,\n  appear = false,\n  getDimensionValue = getDefaultDimensionValue,\n  ...props\n}, ref) => {\n  /* Compute dimension */\n  const computedDimension = typeof dimension === 'function' ? dimension() : dimension;\n\n  /* -- Expanding -- */\n  const handleEnter = useMemo(() => createChainedFunction(elem => {\n    elem.style[computedDimension] = '0';\n  }, onEnter), [computedDimension, onEnter]);\n  const handleEntering = useMemo(() => createChainedFunction(elem => {\n    const scroll = `scroll${computedDimension[0].toUpperCase()}${computedDimension.slice(1)}`;\n    elem.style[computedDimension] = `${elem[scroll]}px`;\n  }, onEntering), [computedDimension, onEntering]);\n  const handleEntered = useMemo(() => createChainedFunction(elem => {\n    elem.style[computedDimension] = null;\n  }, onEntered), [computedDimension, onEntered]);\n\n  /* -- Collapsing -- */\n  const handleExit = useMemo(() => createChainedFunction(elem => {\n    elem.style[computedDimension] = `${getDimensionValue(computedDimension, elem)}px`;\n    triggerBrowserReflow(elem);\n  }, onExit), [onExit, getDimensionValue, computedDimension]);\n  const handleExiting = useMemo(() => createChainedFunction(elem => {\n    elem.style[computedDimension] = null;\n  }, onExiting), [computedDimension, onExiting]);\n  return /*#__PURE__*/_jsx(TransitionWrapper, {\n    ref: ref,\n    addEndListener: transitionEndListener,\n    ...props,\n    \"aria-expanded\": props.role ? inProp : null,\n    onEnter: handleEnter,\n    onEntering: handleEntering,\n    onEntered: handleEntered,\n    onExit: handleExit,\n    onExiting: handleExiting,\n    childRef: getChildRef(children),\n    in: inProp,\n    timeout: timeout,\n    mountOnEnter: mountOnEnter,\n    unmountOnExit: unmountOnExit,\n    appear: appear,\n    children: (state, innerProps) => /*#__PURE__*/React.cloneElement(children, {\n      ...innerProps,\n      className: classNames(className, children.props.className, collapseStyles[state], computedDimension === 'width' && 'collapse-horizontal')\n    })\n  });\n});\nCollapse.displayName = 'Collapse';\nexport default Collapse;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,GAAG,MAAM,iBAAiB;AACjC,OAAOC,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,SAASC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,QAAQ,mCAAmC;AACtF,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,OAAO,GAAG;EACdC,MAAM,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC;EACrCC,KAAK,EAAE,CAAC,YAAY,EAAE,aAAa;AACrC,CAAC;AACD,SAASC,wBAAwBA,CAACC,SAAS,EAAEC,IAAI,EAAE;EACjD,MAAMC,MAAM,GAAG,SAASF,SAAS,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,GAAGH,SAAS,CAACI,KAAK,CAAC,CAAC,CAAC,EAAE;EACzE,MAAMC,KAAK,GAAGJ,IAAI,CAACC,MAAM,CAAC;EAC1B,MAAMI,OAAO,GAAGV,OAAO,CAACI,SAAS,CAAC;EAClC,OAAOK,KAAK;EACZ;EACAE,QAAQ,CAACzB,GAAG,CAACmB,IAAI,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACnC;EACAC,QAAQ,CAACzB,GAAG,CAACmB,IAAI,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;AACrC;AACA,MAAME,cAAc,GAAG;EACrB,CAACrB,MAAM,GAAG,UAAU;EACpB,CAACC,OAAO,GAAG,YAAY;EACvB,CAACF,QAAQ,GAAG,YAAY;EACxB,CAACD,OAAO,GAAG;AACb,CAAC;AACD,MAAMwB,QAAQ,GAAG,aAAa1B,KAAK,CAAC2B,UAAU,CAAC,CAAC;EAC9CC,OAAO;EACPC,UAAU;EACVC,SAAS;EACTC,MAAM;EACNC,SAAS;EACTC,SAAS;EACTC,QAAQ;EACRjB,SAAS,GAAG,QAAQ;EACpBkB,EAAE,EAAEC,MAAM,GAAG,KAAK;EAClBC,OAAO,GAAG,GAAG;EACbC,YAAY,GAAG,KAAK;EACpBC,aAAa,GAAG,KAAK;EACrBC,MAAM,GAAG,KAAK;EACdC,iBAAiB,GAAGzB,wBAAwB;EAC5C,GAAG0B;AACL,CAAC,EAAEC,GAAG,KAAK;EACT;EACA,MAAMC,iBAAiB,GAAG,OAAO3B,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC,CAAC,GAAGA,SAAS;;EAEnF;EACA,MAAM4B,WAAW,GAAG5C,OAAO,CAAC,MAAMO,qBAAqB,CAACU,IAAI,IAAI;IAC9DA,IAAI,CAAC4B,KAAK,CAACF,iBAAiB,CAAC,GAAG,GAAG;EACrC,CAAC,EAAEhB,OAAO,CAAC,EAAE,CAACgB,iBAAiB,EAAEhB,OAAO,CAAC,CAAC;EAC1C,MAAMmB,cAAc,GAAG9C,OAAO,CAAC,MAAMO,qBAAqB,CAACU,IAAI,IAAI;IACjE,MAAM8B,MAAM,GAAG,SAASJ,iBAAiB,CAAC,CAAC,CAAC,CAACxB,WAAW,CAAC,CAAC,GAAGwB,iBAAiB,CAACvB,KAAK,CAAC,CAAC,CAAC,EAAE;IACzFH,IAAI,CAAC4B,KAAK,CAACF,iBAAiB,CAAC,GAAG,GAAG1B,IAAI,CAAC8B,MAAM,CAAC,IAAI;EACrD,CAAC,EAAEnB,UAAU,CAAC,EAAE,CAACe,iBAAiB,EAAEf,UAAU,CAAC,CAAC;EAChD,MAAMoB,aAAa,GAAGhD,OAAO,CAAC,MAAMO,qBAAqB,CAACU,IAAI,IAAI;IAChEA,IAAI,CAAC4B,KAAK,CAACF,iBAAiB,CAAC,GAAG,IAAI;EACtC,CAAC,EAAEd,SAAS,CAAC,EAAE,CAACc,iBAAiB,EAAEd,SAAS,CAAC,CAAC;;EAE9C;EACA,MAAMoB,UAAU,GAAGjD,OAAO,CAAC,MAAMO,qBAAqB,CAACU,IAAI,IAAI;IAC7DA,IAAI,CAAC4B,KAAK,CAACF,iBAAiB,CAAC,GAAG,GAAGH,iBAAiB,CAACG,iBAAiB,EAAE1B,IAAI,CAAC,IAAI;IACjFT,oBAAoB,CAACS,IAAI,CAAC;EAC5B,CAAC,EAAEa,MAAM,CAAC,EAAE,CAACA,MAAM,EAAEU,iBAAiB,EAAEG,iBAAiB,CAAC,CAAC;EAC3D,MAAMO,aAAa,GAAGlD,OAAO,CAAC,MAAMO,qBAAqB,CAACU,IAAI,IAAI;IAChEA,IAAI,CAAC4B,KAAK,CAACF,iBAAiB,CAAC,GAAG,IAAI;EACtC,CAAC,EAAEZ,SAAS,CAAC,EAAE,CAACY,iBAAiB,EAAEZ,SAAS,CAAC,CAAC;EAC9C,OAAO,aAAapB,IAAI,CAACF,iBAAiB,EAAE;IAC1CiC,GAAG,EAAEA,GAAG;IACRS,cAAc,EAAE7C,qBAAqB;IACrC,GAAGmC,KAAK;IACR,eAAe,EAAEA,KAAK,CAACW,IAAI,GAAGjB,MAAM,GAAG,IAAI;IAC3CR,OAAO,EAAEiB,WAAW;IACpBhB,UAAU,EAAEkB,cAAc;IAC1BjB,SAAS,EAAEmB,aAAa;IACxBlB,MAAM,EAAEmB,UAAU;IAClBlB,SAAS,EAAEmB,aAAa;IACxBG,QAAQ,EAAEhD,WAAW,CAAC4B,QAAQ,CAAC;IAC/BC,EAAE,EAAEC,MAAM;IACVC,OAAO,EAAEA,OAAO;IAChBC,YAAY,EAAEA,YAAY;IAC1BC,aAAa,EAAEA,aAAa;IAC5BC,MAAM,EAAEA,MAAM;IACdN,QAAQ,EAAEA,CAACqB,KAAK,EAAEC,UAAU,KAAK,aAAaxD,KAAK,CAACyD,YAAY,CAACvB,QAAQ,EAAE;MACzE,GAAGsB,UAAU;MACbvB,SAAS,EAAEnC,UAAU,CAACmC,SAAS,EAAEC,QAAQ,CAACQ,KAAK,CAACT,SAAS,EAAER,cAAc,CAAC8B,KAAK,CAAC,EAAEX,iBAAiB,KAAK,OAAO,IAAI,qBAAqB;IAC1I,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFlB,QAAQ,CAACgC,WAAW,GAAG,UAAU;AACjC,eAAehC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}