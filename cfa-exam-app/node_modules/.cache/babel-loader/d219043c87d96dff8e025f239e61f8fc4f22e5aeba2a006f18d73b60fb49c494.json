{"ast": null, "code": "// PDF Parser for CFA Questions and Answers\nimport * as pdfjsLib from 'pdfjs-dist';\n\n// Set worker path - use the worker from the same version\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;\nclass CFAQuestionParser {\n  constructor() {\n    this.questions = [];\n    this.answers = {};\n  }\n  async parseQuestionFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n        fullText += pageText + '\\n\\n';\n      }\n\n      // Debug: Log extracted text\n      console.log('Extracted text length:', fullText.length);\n      console.log('First 2000 characters:', fullText.substring(0, 2000));\n\n      // Parse questions from text\n      const questions = this.extractQuestions(fullText);\n      console.log('Extracted questions:', questions.length);\n      return questions;\n    } catch (error) {\n      console.error('Error parsing question file:', error);\n      throw error;\n    }\n  }\n  async parseAnswerFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n        fullText += pageText + '\\n\\n';\n      }\n      console.log('Answer file text length:', fullText.length);\n      console.log('Answer file preview:', fullText.substring(0, 1000));\n\n      // Parse answers from text\n      const answers = this.extractAnswers(fullText);\n      console.log('Extracted answers:', Object.keys(answers).length);\n      return answers;\n    } catch (error) {\n      console.error('Error parsing answer file:', error);\n      throw error;\n    }\n  }\n  extractQuestions(text) {\n    const questions = [];\n    console.log('Looking for question patterns in text...');\n    console.log('Text length:', text.length);\n\n    // Try simpler approach - split by \"Question #\" and process each block\n    const questionBlocks = text.split(/(?=Question #\\d+)/);\n    console.log(`Split into ${questionBlocks.length} blocks`);\n    for (let i = 0; i < questionBlocks.length; i++) {\n      const block = questionBlocks[i].trim();\n      if (!block || block.length < 20) continue;\n      console.log(`\\n--- Processing block ${i} ---`);\n      console.log('Block start:', block.substring(0, 100));\n\n      // Try to extract question info from block\n      const patterns = [/Question #(\\d+) of (\\d+) Question ID: (\\d+)/, /Question #(\\d+) - (\\d+) of (\\d+) Question ID: (\\d+)/, /Question #(\\d+) of (\\d+)/, /Question #(\\d+)/];\n      let questionInfo = null;\n      for (const pattern of patterns) {\n        const match = block.match(pattern);\n        if (match) {\n          questionInfo = {\n            questionNum: parseInt(match[1]),\n            endNum: match[2] && !isNaN(parseInt(match[2])) ? parseInt(match[2]) : null,\n            totalQ: match[3] && !isNaN(parseInt(match[3])) ? parseInt(match[3]) : 100,\n            questionId: match[4] || 'unknown',\n            isGroup: match[0].includes(' - '),\n            fullMatch: match[0]\n          };\n          break;\n        }\n      }\n      if (!questionInfo) {\n        console.log('No question pattern found in block');\n        continue;\n      }\n      console.log('Found question info:', questionInfo);\n\n      // Extract content after the question header\n      const headerIndex = block.indexOf(questionInfo.fullMatch);\n      const content = block.substring(headerIndex + questionInfo.fullMatch.length).trim();\n      if (questionInfo.isGroup) {\n        // Handle group questions - extract context and individual questions\n        console.log(`Processing group questions ${questionInfo.questionNum}-${questionInfo.endNum}`);\n\n        // For group questions, try to find individual question markers within the content\n        const groupQuestions = this.extractGroupQuestions(content, questionInfo.questionNum, questionInfo.endNum, questionInfo.totalQ, questionInfo.questionId);\n        if (groupQuestions.length > 0) {\n          questions.push(...groupQuestions);\n        } else {\n          // Fallback: treat as single question\n          const questionData = this.parseSingleQuestion(content, questionInfo.questionNum, questionInfo.totalQ, questionInfo.questionId);\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      } else {\n        // Handle single question\n        console.log(`Processing single question ${questionInfo.questionNum}`);\n        const questionData = this.parseSingleQuestion(content, questionInfo.questionNum, questionInfo.totalQ, questionInfo.questionId);\n        if (questionData) {\n          questions.push(questionData);\n          console.log(`Successfully parsed question ${questionInfo.questionNum}`);\n        } else {\n          console.log(`Failed to parse question ${questionInfo.questionNum}`);\n        }\n      }\n    }\n    console.log(`\\n=== EXTRACTION COMPLETE ===`);\n    console.log(`Total questions extracted: ${questions.length}`);\n    console.log('Question numbers:', questions.map(q => q.questionNumber).sort((a, b) => a - b));\n    return questions;\n  }\n  extractQuestionsAlternative(text) {\n    console.log('Using alternative extraction method...');\n    const questions = [];\n\n    // Look for any numbered patterns that might be questions\n    const patterns = [/(\\d+)\\.\\s+([^0-9]+?)(?=\\d+\\.|$)/gs,\n    // 1. Question text\n    /Question\\s+(\\d+)[:\\.]?\\s*([^Q]+?)(?=Question\\s+\\d+|$)/gis // Question 1: text\n    ];\n    for (const pattern of patterns) {\n      const matches = [...text.matchAll(pattern)];\n      console.log(`Alternative pattern found ${matches.length} matches`);\n      for (const match of matches) {\n        const questionNum = parseInt(match[1]);\n        const content = match[2].trim();\n        if (content.length > 20) {\n          // Filter out very short matches\n          const questionData = this.parseSingleQuestion(content, questionNum, 100,\n          // Default total\n          'alt-' + questionNum);\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n      if (questions.length > 0) break; // Use first successful pattern\n    }\n    return questions;\n  }\n  extractGroupQuestions(content, startQ, endQ, totalQ, questionId) {\n    const questions = [];\n    console.log(`Extracting group questions ${startQ}-${endQ || startQ}`);\n    console.log('Group content preview:', content.substring(0, 200));\n\n    // First, try to extract context (text before any question-specific content)\n    let context = '';\n    const contextMatch = content.match(/^(.*?)(?=[A-E]\\)|Question|$)/s);\n    if (contextMatch && contextMatch[1].trim().length > 50) {\n      context = contextMatch[1].trim();\n      console.log('Extracted context:', context.substring(0, 100));\n    }\n\n    // If this is a range (e.g., Question #6-11), try to find individual questions\n    if (endQ && endQ > startQ) {\n      console.log(`Looking for individual questions ${startQ} to ${endQ}`);\n\n      // Try to split content by question numbers\n      for (let qNum = startQ; qNum <= endQ; qNum++) {\n        // Look for patterns like \"6.\", \"Question 6\", etc.\n        const patterns = [new RegExp(`${qNum}\\\\.(.*?)(?=${qNum + 1}\\\\.|$)`, 's'), new RegExp(`Question\\\\s+${qNum}[:\\\\.]?(.*?)(?=Question\\\\s+${qNum + 1}|$)`, 'is')];\n        let questionContent = '';\n        for (const pattern of patterns) {\n          const match = content.match(pattern);\n          if (match) {\n            questionContent = match[1].trim();\n            break;\n          }\n        }\n        if (questionContent) {\n          console.log(`Found content for question ${qNum}`);\n          const questionData = this.parseSingleQuestion(questionContent, qNum, totalQ, `${questionId}-${qNum}`, context);\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n    }\n\n    // If no individual questions found, treat the whole content as one question\n    if (questions.length === 0) {\n      console.log('No individual questions found, treating as single question with context');\n      const questionData = this.parseSingleQuestion(content, startQ, totalQ, questionId, context);\n      if (questionData) {\n        questions.push(questionData);\n      }\n    }\n    console.log(`Extracted ${questions.length} questions from group`);\n    return questions;\n  }\n  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {\n    if (!content.trim()) return null;\n    console.log(`\\n=== Parsing Question ${questionNum} ===`);\n    console.log(`Content length: ${content.length}`);\n    console.log(`Content preview: ${content.substring(0, 300)}...`);\n    let questionText = '';\n    const choices = {};\n\n    // Method 1: Look for choice patterns A) B) C) D) E)\n    const choicePattern = /([A-E])\\)\\s*([^A-E]*?)(?=\\s*[A-E]\\)|$)/g;\n    const choiceMatches = [...content.matchAll(choicePattern)];\n    console.log(`Found ${choiceMatches.length} choice matches`);\n    if (choiceMatches.length >= 3) {\n      // Extract question text (everything before first choice)\n      const firstChoiceIndex = choiceMatches[0].index;\n      questionText = content.substring(0, firstChoiceIndex).trim();\n\n      // Extract choices\n      choiceMatches.forEach(match => {\n        const choice = match[1];\n        const text = match[2].trim();\n        if (text.length > 0) {\n          choices[choice] = text;\n        }\n      });\n      console.log(`Method 1 success: ${Object.keys(choices).length} choices extracted`);\n    } else {\n      // Method 2: Line-by-line parsing with better logic\n      console.log('Using Method 2: Line-by-line parsing');\n      const lines = content.split(/\\n/).map(line => line.trim()).filter(line => line.length > 0);\n      let currentChoice = null;\n      let choiceText = '';\n      let questionLines = [];\n      let foundFirstChoice = false;\n      for (const line of lines) {\n        // Check for choice pattern at start of line\n        const choiceMatch = line.match(/^([A-E])\\)\\s*(.*)/);\n        if (choiceMatch) {\n          // Save previous choice if exists\n          if (currentChoice && choiceText.trim()) {\n            choices[currentChoice] = choiceText.trim();\n          }\n          currentChoice = choiceMatch[1];\n          choiceText = choiceMatch[2];\n          foundFirstChoice = true;\n        } else if (currentChoice && foundFirstChoice) {\n          // Continue current choice text\n          choiceText += ' ' + line;\n        } else if (!foundFirstChoice) {\n          // Still in question text area\n          questionLines.push(line);\n        }\n      }\n\n      // Save last choice\n      if (currentChoice && choiceText.trim()) {\n        choices[currentChoice] = choiceText.trim();\n      }\n      questionText = questionLines.join(' ').trim();\n      console.log(`Method 2 result: ${Object.keys(choices).length} choices, question length: ${questionText.length}`);\n    }\n\n    // Method 3: If still no good results, try more aggressive parsing\n    if (Object.keys(choices).length < 3 && content.length > 100) {\n      console.log('Using Method 3: Aggressive parsing');\n\n      // Look for any A) B) C) patterns anywhere in text\n      const aggressivePattern = /([A-E])\\)\\s*([^A-E\\n]{10,200}?)(?=\\s*[A-E]\\)|$)/g;\n      const aggressiveMatches = [...content.matchAll(aggressivePattern)];\n      if (aggressiveMatches.length >= 3) {\n        // Clear previous results\n        Object.keys(choices).forEach(key => delete choices[key]);\n\n        // Extract question text (everything before first choice)\n        const firstChoiceIndex = aggressiveMatches[0].index;\n        questionText = content.substring(0, firstChoiceIndex).trim();\n\n        // Extract choices\n        aggressiveMatches.forEach(match => {\n          const choice = match[1];\n          const text = match[2].trim();\n          choices[choice] = text;\n        });\n        console.log(`Method 3 success: ${Object.keys(choices).length} choices extracted`);\n      }\n    }\n\n    // Clean up and validate\n    questionText = questionText.replace(/\\s+/g, ' ').trim();\n    Object.keys(choices).forEach(key => {\n      choices[key] = choices[key].replace(/\\s+/g, ' ').trim();\n      // Remove empty choices\n      if (!choices[key]) {\n        delete choices[key];\n      }\n    });\n    console.log(`Final result for Question ${questionNum}:`);\n    console.log(`- Question text: ${questionText.length} chars`);\n    console.log(`- Choices: ${Object.keys(choices).join(', ')}`);\n    console.log(`- Context: ${context ? 'Yes' : 'No'}`);\n\n    // Validation and fallbacks\n    if (!questionText && Object.keys(choices).length === 0) {\n      console.log(`Question ${questionNum}: Complete parsing failure`);\n      return null;\n    }\n\n    // Ensure minimum choices\n    const expectedChoices = ['A', 'B', 'C', 'D', 'E'];\n    const missingChoices = expectedChoices.filter(c => !choices[c]);\n    if (missingChoices.length > 2) {\n      console.log(`Question ${questionNum}: Missing too many choices, adding placeholders`);\n      missingChoices.forEach(choice => {\n        choices[choice] = `Choice ${choice} (not found in PDF)`;\n      });\n    }\n\n    // Ensure question text\n    if (!questionText.trim()) {\n      questionText = `Question ${questionNum} content (parsing incomplete - check PDF)`;\n    }\n    return {\n      questionNumber: questionNum,\n      totalQuestions: totalQ,\n      questionId: questionId,\n      context: context,\n      questionText: questionText,\n      choices: choices,\n      isGroupQuestion: Boolean(context)\n    };\n  }\n  extractAnswers(text) {\n    const answers = {};\n    console.log('Extracting answers from text...');\n\n    // Find all question markers in answer file\n    const questionMarkers = [];\n    const patterns = [/Question #(\\d+) of (\\d+) Question ID: (\\d+)/g, /Question #(\\d+)/g];\n    for (const pattern of patterns) {\n      let match;\n      while ((match = pattern.exec(text)) !== null) {\n        questionMarkers.push({\n          index: match.index,\n          fullMatch: match[0],\n          questionNum: parseInt(match[1])\n        });\n      }\n    }\n    console.log(`Found ${questionMarkers.length} question markers in answer file`);\n\n    // Sort by position\n    questionMarkers.sort((a, b) => a.index - b.index);\n\n    // Extract answer content for each question\n    for (let i = 0; i < questionMarkers.length; i++) {\n      const marker = questionMarkers[i];\n      const nextMarker = questionMarkers[i + 1];\n\n      // Get content from current marker to next marker (or end of text)\n      const startIndex = marker.index + marker.fullMatch.length;\n      const endIndex = nextMarker ? nextMarker.index : text.length;\n      const content = text.substring(startIndex, endIndex).trim();\n      console.log(`Processing answer for question ${marker.questionNum}, content length: ${content.length}`);\n\n      // Find correct answer\n      let correctAnswer = null;\n      const answerPatterns = [/The correct answer is ([A-E])\\./i, /The correct answer is ([A-E])\\s/i, /Correct answer:\\s*([A-E])/i, /Answer:\\s*([A-E])/i, /([A-E])\\s*is correct/i, /Choice\\s*([A-E])\\s*is correct/i];\n      for (const pattern of answerPatterns) {\n        const match = content.match(pattern);\n        if (match) {\n          correctAnswer = match[1].toUpperCase();\n          console.log(`Found correct answer ${correctAnswer} for question ${marker.questionNum}`);\n          break;\n        }\n      }\n\n      // Find explanation - be more flexible and comprehensive\n      let explanation = '';\n      console.log(`Looking for explanation in question ${marker.questionNum} content...`);\n      console.log(`Content preview: ${content.substring(0, 500)}...`);\n\n      // Multiple patterns to find explanation\n      const explanationPatterns = [/Explanation[:\\s]*\\n?(.*?)(?=Question #|$)/is, /Explanation[:\\s]+(.*?)(?=\\(Module|$)/is, /Explanation[:\\s]+(.*?)(?=\\n\\s*\\n|$)/is, /Explanation[:\\s]+(.*)/is];\n      for (let i = 0; i < explanationPatterns.length; i++) {\n        const pattern = explanationPatterns[i];\n        const match = content.match(pattern);\n        if (match && match[1].trim().length > 10) {\n          explanation = match[1].trim();\n          console.log(`Found explanation using pattern ${i + 1} for question ${marker.questionNum}`);\n          break;\n        }\n      }\n\n      // If no explanation found with patterns, try manual extraction\n      if (!explanation) {\n        const explKeywords = ['explanation', 'Explanation', 'EXPLANATION'];\n        for (const keyword of explKeywords) {\n          const explIndex = content.indexOf(keyword);\n          if (explIndex !== -1) {\n            // Extract everything after the keyword\n            let afterExpl = content.substring(explIndex + keyword.length).trim();\n\n            // Remove leading colons, spaces, newlines\n            afterExpl = afterExpl.replace(/^[:\\s\\n]+/, '');\n\n            // Take everything until next question or end\n            const nextQuestionIndex = afterExpl.search(/Question #\\d+/i);\n            if (nextQuestionIndex !== -1) {\n              afterExpl = afterExpl.substring(0, nextQuestionIndex);\n            }\n            if (afterExpl.length > 20) {\n              explanation = afterExpl.trim();\n              console.log(`Manual explanation extraction for question ${marker.questionNum}`);\n              break;\n            }\n          }\n        }\n      }\n\n      // Clean up explanation\n      if (explanation) {\n        // Normalize whitespace\n        explanation = explanation.replace(/\\s+/g, ' ');\n\n        // Remove common suffixes and prefixes\n        explanation = explanation.replace(/^\\s*[:\\-\\s]+/, ''); // Remove leading colons/dashes\n        explanation = explanation.replace(/\\(Module.*?\\)$/i, '');\n        explanation = explanation.replace(/\\(LOS.*?\\)$/i, '');\n        explanation = explanation.replace(/\\(Reading.*?\\)$/i, '');\n\n        // Remove trailing page numbers or references\n        explanation = explanation.replace(/\\s+\\d+\\s*$/, '');\n        explanation = explanation.trim();\n        console.log(`Cleaned explanation for question ${marker.questionNum}, final length: ${explanation.length}`);\n        console.log(`Explanation preview: ${explanation.substring(0, 100)}...`);\n      } else {\n        console.log(`No explanation found for question ${marker.questionNum}`);\n      }\n      if (correctAnswer) {\n        answers[marker.questionNum] = {\n          correctAnswer: correctAnswer,\n          explanation: explanation || 'No explanation available'\n        };\n      } else {\n        console.log(`No correct answer found for question ${marker.questionNum}`);\n      }\n    }\n    console.log(`Extracted answers for ${Object.keys(answers).length} questions`);\n    return answers;\n  }\n  async parseFiles(questionFile, answerFile) {\n    try {\n      const [questions, answers] = await Promise.all([this.parseQuestionFile(questionFile), this.parseAnswerFile(answerFile)]);\n      return {\n        questions,\n        answers\n      };\n    } catch (error) {\n      console.error('Error parsing files:', error);\n      throw error;\n    }\n  }\n}\nexport default CFAQuestionParser;", "map": {"version": 3, "names": ["pdfjsLib", "GlobalWorkerOptions", "workerSrc", "CFAQuestionParser", "constructor", "questions", "answers", "parseQuestionFile", "file", "arrayBuffer", "pdf", "getDocument", "promise", "fullText", "i", "numPages", "page", "getPage", "textContent", "getTextContent", "pageText", "lastY", "item", "items", "Math", "abs", "transform", "str", "console", "log", "length", "substring", "extractQuestions", "error", "parseAnswerFile", "extractAnswers", "Object", "keys", "text", "questionBlocks", "split", "block", "trim", "patterns", "questionInfo", "pattern", "match", "questionNum", "parseInt", "endNum", "isNaN", "totalQ", "questionId", "isGroup", "includes", "fullMatch", "headerIndex", "indexOf", "content", "groupQuestions", "extractGroupQuestions", "push", "questionData", "parseSingleQuestion", "map", "q", "questionNumber", "sort", "a", "b", "extractQuestionsAlternative", "matches", "matchAll", "startQ", "endQ", "context", "contextMatch", "qNum", "RegExp", "questionContent", "questionText", "choices", "choicePattern", "choiceMatches", "firstChoiceIndex", "index", "for<PERSON>ach", "choice", "lines", "line", "filter", "currentChoice", "choiceText", "questionLines", "foundFirstChoice", "choiceMatch", "join", "aggressivePattern", "aggressiveMatches", "key", "replace", "expectedChoices", "missingChoices", "c", "totalQuestions", "isGroupQuestion", "Boolean", "questionMarkers", "exec", "marker", "nextM<PERSON><PERSON>", "startIndex", "endIndex", "<PERSON><PERSON><PERSON><PERSON>", "answerPatterns", "toUpperCase", "explanation", "explanationPatterns", "explKeywords", "keyword", "explIndex", "afterExpl", "nextQuestionIndex", "search", "parseFiles", "questionFile", "answerFile", "Promise", "all"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/utils/pdfParser.js"], "sourcesContent": ["// PDF Parser for CFA Questions and Answers\nimport * as pdfjsLib from 'pdfjs-dist';\n\n// Set worker path - use the worker from the same version\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;\n\nclass CFAQuestionParser {\n  constructor() {\n    this.questions = [];\n    this.answers = {};\n  }\n\n  async parseQuestionFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n\n        fullText += pageText + '\\n\\n';\n      }\n\n      // Debug: Log extracted text\n      console.log('Extracted text length:', fullText.length);\n      console.log('First 2000 characters:', fullText.substring(0, 2000));\n\n      // Parse questions from text\n      const questions = this.extractQuestions(fullText);\n      console.log('Extracted questions:', questions.length);\n\n      return questions;\n    } catch (error) {\n      console.error('Error parsing question file:', error);\n      throw error;\n    }\n  }\n\n  async parseAnswerFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n\n        fullText += pageText + '\\n\\n';\n      }\n\n      console.log('Answer file text length:', fullText.length);\n      console.log('Answer file preview:', fullText.substring(0, 1000));\n\n      // Parse answers from text\n      const answers = this.extractAnswers(fullText);\n      console.log('Extracted answers:', Object.keys(answers).length);\n\n      return answers;\n    } catch (error) {\n      console.error('Error parsing answer file:', error);\n      throw error;\n    }\n  }\n\n  extractQuestions(text) {\n    const questions = [];\n\n    console.log('Looking for question patterns in text...');\n    console.log('Text length:', text.length);\n\n    // Try simpler approach - split by \"Question #\" and process each block\n    const questionBlocks = text.split(/(?=Question #\\d+)/);\n    console.log(`Split into ${questionBlocks.length} blocks`);\n\n    for (let i = 0; i < questionBlocks.length; i++) {\n      const block = questionBlocks[i].trim();\n      if (!block || block.length < 20) continue;\n\n      console.log(`\\n--- Processing block ${i} ---`);\n      console.log('Block start:', block.substring(0, 100));\n\n      // Try to extract question info from block\n      const patterns = [\n        /Question #(\\d+) of (\\d+) Question ID: (\\d+)/,\n        /Question #(\\d+) - (\\d+) of (\\d+) Question ID: (\\d+)/,\n        /Question #(\\d+) of (\\d+)/,\n        /Question #(\\d+)/\n      ];\n\n      let questionInfo = null;\n      for (const pattern of patterns) {\n        const match = block.match(pattern);\n        if (match) {\n          questionInfo = {\n            questionNum: parseInt(match[1]),\n            endNum: match[2] && !isNaN(parseInt(match[2])) ? parseInt(match[2]) : null,\n            totalQ: match[3] && !isNaN(parseInt(match[3])) ? parseInt(match[3]) : 100,\n            questionId: match[4] || 'unknown',\n            isGroup: match[0].includes(' - '),\n            fullMatch: match[0]\n          };\n          break;\n        }\n      }\n\n      if (!questionInfo) {\n        console.log('No question pattern found in block');\n        continue;\n      }\n\n      console.log('Found question info:', questionInfo);\n\n      // Extract content after the question header\n      const headerIndex = block.indexOf(questionInfo.fullMatch);\n      const content = block.substring(headerIndex + questionInfo.fullMatch.length).trim();\n\n      if (questionInfo.isGroup) {\n        // Handle group questions - extract context and individual questions\n        console.log(`Processing group questions ${questionInfo.questionNum}-${questionInfo.endNum}`);\n\n        // For group questions, try to find individual question markers within the content\n        const groupQuestions = this.extractGroupQuestions(\n          content,\n          questionInfo.questionNum,\n          questionInfo.endNum,\n          questionInfo.totalQ,\n          questionInfo.questionId\n        );\n\n        if (groupQuestions.length > 0) {\n          questions.push(...groupQuestions);\n        } else {\n          // Fallback: treat as single question\n          const questionData = this.parseSingleQuestion(\n            content,\n            questionInfo.questionNum,\n            questionInfo.totalQ,\n            questionInfo.questionId\n          );\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      } else {\n        // Handle single question\n        console.log(`Processing single question ${questionInfo.questionNum}`);\n\n        const questionData = this.parseSingleQuestion(\n          content,\n          questionInfo.questionNum,\n          questionInfo.totalQ,\n          questionInfo.questionId\n        );\n\n        if (questionData) {\n          questions.push(questionData);\n          console.log(`Successfully parsed question ${questionInfo.questionNum}`);\n        } else {\n          console.log(`Failed to parse question ${questionInfo.questionNum}`);\n        }\n      }\n    }\n\n    console.log(`\\n=== EXTRACTION COMPLETE ===`);\n    console.log(`Total questions extracted: ${questions.length}`);\n    console.log('Question numbers:', questions.map(q => q.questionNumber).sort((a, b) => a - b));\n\n    return questions;\n  }\n\n  extractQuestionsAlternative(text) {\n    console.log('Using alternative extraction method...');\n    const questions = [];\n\n    // Look for any numbered patterns that might be questions\n    const patterns = [\n      /(\\d+)\\.\\s+([^0-9]+?)(?=\\d+\\.|$)/gs,  // 1. Question text\n      /Question\\s+(\\d+)[:\\.]?\\s*([^Q]+?)(?=Question\\s+\\d+|$)/gis,  // Question 1: text\n    ];\n\n    for (const pattern of patterns) {\n      const matches = [...text.matchAll(pattern)];\n      console.log(`Alternative pattern found ${matches.length} matches`);\n\n      for (const match of matches) {\n        const questionNum = parseInt(match[1]);\n        const content = match[2].trim();\n\n        if (content.length > 20) { // Filter out very short matches\n          const questionData = this.parseSingleQuestion(\n            content,\n            questionNum,\n            100, // Default total\n            'alt-' + questionNum\n          );\n\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n\n      if (questions.length > 0) break; // Use first successful pattern\n    }\n\n    return questions;\n  }\n\n  extractGroupQuestions(content, startQ, endQ, totalQ, questionId) {\n    const questions = [];\n\n    console.log(`Extracting group questions ${startQ}-${endQ || startQ}`);\n    console.log('Group content preview:', content.substring(0, 200));\n\n    // First, try to extract context (text before any question-specific content)\n    let context = '';\n    const contextMatch = content.match(/^(.*?)(?=[A-E]\\)|Question|$)/s);\n    if (contextMatch && contextMatch[1].trim().length > 50) {\n      context = contextMatch[1].trim();\n      console.log('Extracted context:', context.substring(0, 100));\n    }\n\n    // If this is a range (e.g., Question #6-11), try to find individual questions\n    if (endQ && endQ > startQ) {\n      console.log(`Looking for individual questions ${startQ} to ${endQ}`);\n\n      // Try to split content by question numbers\n      for (let qNum = startQ; qNum <= endQ; qNum++) {\n        // Look for patterns like \"6.\", \"Question 6\", etc.\n        const patterns = [\n          new RegExp(`${qNum}\\\\.(.*?)(?=${qNum + 1}\\\\.|$)`, 's'),\n          new RegExp(`Question\\\\s+${qNum}[:\\\\.]?(.*?)(?=Question\\\\s+${qNum + 1}|$)`, 'is')\n        ];\n\n        let questionContent = '';\n        for (const pattern of patterns) {\n          const match = content.match(pattern);\n          if (match) {\n            questionContent = match[1].trim();\n            break;\n          }\n        }\n\n        if (questionContent) {\n          console.log(`Found content for question ${qNum}`);\n          const questionData = this.parseSingleQuestion(\n            questionContent,\n            qNum,\n            totalQ,\n            `${questionId}-${qNum}`,\n            context\n          );\n\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n    }\n\n    // If no individual questions found, treat the whole content as one question\n    if (questions.length === 0) {\n      console.log('No individual questions found, treating as single question with context');\n      const questionData = this.parseSingleQuestion(\n        content,\n        startQ,\n        totalQ,\n        questionId,\n        context\n      );\n\n      if (questionData) {\n        questions.push(questionData);\n      }\n    }\n\n    console.log(`Extracted ${questions.length} questions from group`);\n    return questions;\n  }\n\n  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {\n    if (!content.trim()) return null;\n\n    console.log(`\\n=== Parsing Question ${questionNum} ===`);\n    console.log(`Content length: ${content.length}`);\n    console.log(`Content preview: ${content.substring(0, 300)}...`);\n\n    let questionText = '';\n    const choices = {};\n\n    // Method 1: Look for choice patterns A) B) C) D) E)\n    const choicePattern = /([A-E])\\)\\s*([^A-E]*?)(?=\\s*[A-E]\\)|$)/g;\n    const choiceMatches = [...content.matchAll(choicePattern)];\n\n    console.log(`Found ${choiceMatches.length} choice matches`);\n\n    if (choiceMatches.length >= 3) {\n      // Extract question text (everything before first choice)\n      const firstChoiceIndex = choiceMatches[0].index;\n      questionText = content.substring(0, firstChoiceIndex).trim();\n\n      // Extract choices\n      choiceMatches.forEach(match => {\n        const choice = match[1];\n        const text = match[2].trim();\n        if (text.length > 0) {\n          choices[choice] = text;\n        }\n      });\n\n      console.log(`Method 1 success: ${Object.keys(choices).length} choices extracted`);\n    } else {\n      // Method 2: Line-by-line parsing with better logic\n      console.log('Using Method 2: Line-by-line parsing');\n\n      const lines = content.split(/\\n/).map(line => line.trim()).filter(line => line.length > 0);\n\n      let currentChoice = null;\n      let choiceText = '';\n      let questionLines = [];\n      let foundFirstChoice = false;\n\n      for (const line of lines) {\n        // Check for choice pattern at start of line\n        const choiceMatch = line.match(/^([A-E])\\)\\s*(.*)/);\n\n        if (choiceMatch) {\n          // Save previous choice if exists\n          if (currentChoice && choiceText.trim()) {\n            choices[currentChoice] = choiceText.trim();\n          }\n\n          currentChoice = choiceMatch[1];\n          choiceText = choiceMatch[2];\n          foundFirstChoice = true;\n\n        } else if (currentChoice && foundFirstChoice) {\n          // Continue current choice text\n          choiceText += ' ' + line;\n\n        } else if (!foundFirstChoice) {\n          // Still in question text area\n          questionLines.push(line);\n        }\n      }\n\n      // Save last choice\n      if (currentChoice && choiceText.trim()) {\n        choices[currentChoice] = choiceText.trim();\n      }\n\n      questionText = questionLines.join(' ').trim();\n      console.log(`Method 2 result: ${Object.keys(choices).length} choices, question length: ${questionText.length}`);\n    }\n\n    // Method 3: If still no good results, try more aggressive parsing\n    if (Object.keys(choices).length < 3 && content.length > 100) {\n      console.log('Using Method 3: Aggressive parsing');\n\n      // Look for any A) B) C) patterns anywhere in text\n      const aggressivePattern = /([A-E])\\)\\s*([^A-E\\n]{10,200}?)(?=\\s*[A-E]\\)|$)/g;\n      const aggressiveMatches = [...content.matchAll(aggressivePattern)];\n\n      if (aggressiveMatches.length >= 3) {\n        // Clear previous results\n        Object.keys(choices).forEach(key => delete choices[key]);\n\n        // Extract question text (everything before first choice)\n        const firstChoiceIndex = aggressiveMatches[0].index;\n        questionText = content.substring(0, firstChoiceIndex).trim();\n\n        // Extract choices\n        aggressiveMatches.forEach(match => {\n          const choice = match[1];\n          const text = match[2].trim();\n          choices[choice] = text;\n        });\n\n        console.log(`Method 3 success: ${Object.keys(choices).length} choices extracted`);\n      }\n    }\n\n    // Clean up and validate\n    questionText = questionText.replace(/\\s+/g, ' ').trim();\n    Object.keys(choices).forEach(key => {\n      choices[key] = choices[key].replace(/\\s+/g, ' ').trim();\n      // Remove empty choices\n      if (!choices[key]) {\n        delete choices[key];\n      }\n    });\n\n    console.log(`Final result for Question ${questionNum}:`);\n    console.log(`- Question text: ${questionText.length} chars`);\n    console.log(`- Choices: ${Object.keys(choices).join(', ')}`);\n    console.log(`- Context: ${context ? 'Yes' : 'No'}`);\n\n    // Validation and fallbacks\n    if (!questionText && Object.keys(choices).length === 0) {\n      console.log(`Question ${questionNum}: Complete parsing failure`);\n      return null;\n    }\n\n    // Ensure minimum choices\n    const expectedChoices = ['A', 'B', 'C', 'D', 'E'];\n    const missingChoices = expectedChoices.filter(c => !choices[c]);\n\n    if (missingChoices.length > 2) {\n      console.log(`Question ${questionNum}: Missing too many choices, adding placeholders`);\n      missingChoices.forEach(choice => {\n        choices[choice] = `Choice ${choice} (not found in PDF)`;\n      });\n    }\n\n    // Ensure question text\n    if (!questionText.trim()) {\n      questionText = `Question ${questionNum} content (parsing incomplete - check PDF)`;\n    }\n\n    return {\n      questionNumber: questionNum,\n      totalQuestions: totalQ,\n      questionId: questionId,\n      context: context,\n      questionText: questionText,\n      choices: choices,\n      isGroupQuestion: Boolean(context)\n    };\n  }\n\n  extractAnswers(text) {\n    const answers = {};\n\n    console.log('Extracting answers from text...');\n\n    // Find all question markers in answer file\n    const questionMarkers = [];\n    const patterns = [\n      /Question #(\\d+) of (\\d+) Question ID: (\\d+)/g,\n      /Question #(\\d+)/g\n    ];\n\n    for (const pattern of patterns) {\n      let match;\n      while ((match = pattern.exec(text)) !== null) {\n        questionMarkers.push({\n          index: match.index,\n          fullMatch: match[0],\n          questionNum: parseInt(match[1])\n        });\n      }\n    }\n\n    console.log(`Found ${questionMarkers.length} question markers in answer file`);\n\n    // Sort by position\n    questionMarkers.sort((a, b) => a.index - b.index);\n\n    // Extract answer content for each question\n    for (let i = 0; i < questionMarkers.length; i++) {\n      const marker = questionMarkers[i];\n      const nextMarker = questionMarkers[i + 1];\n\n      // Get content from current marker to next marker (or end of text)\n      const startIndex = marker.index + marker.fullMatch.length;\n      const endIndex = nextMarker ? nextMarker.index : text.length;\n      const content = text.substring(startIndex, endIndex).trim();\n\n      console.log(`Processing answer for question ${marker.questionNum}, content length: ${content.length}`);\n\n      // Find correct answer\n      let correctAnswer = null;\n      const answerPatterns = [\n        /The correct answer is ([A-E])\\./i,\n        /The correct answer is ([A-E])\\s/i,\n        /Correct answer:\\s*([A-E])/i,\n        /Answer:\\s*([A-E])/i,\n        /([A-E])\\s*is correct/i,\n        /Choice\\s*([A-E])\\s*is correct/i\n      ];\n\n      for (const pattern of answerPatterns) {\n        const match = content.match(pattern);\n        if (match) {\n          correctAnswer = match[1].toUpperCase();\n          console.log(`Found correct answer ${correctAnswer} for question ${marker.questionNum}`);\n          break;\n        }\n      }\n\n      // Find explanation - be more flexible and comprehensive\n      let explanation = '';\n\n      console.log(`Looking for explanation in question ${marker.questionNum} content...`);\n      console.log(`Content preview: ${content.substring(0, 500)}...`);\n\n      // Multiple patterns to find explanation\n      const explanationPatterns = [\n        /Explanation[:\\s]*\\n?(.*?)(?=Question #|$)/is,\n        /Explanation[:\\s]+(.*?)(?=\\(Module|$)/is,\n        /Explanation[:\\s]+(.*?)(?=\\n\\s*\\n|$)/is,\n        /Explanation[:\\s]+(.*)/is\n      ];\n\n      for (let i = 0; i < explanationPatterns.length; i++) {\n        const pattern = explanationPatterns[i];\n        const match = content.match(pattern);\n        if (match && match[1].trim().length > 10) {\n          explanation = match[1].trim();\n          console.log(`Found explanation using pattern ${i + 1} for question ${marker.questionNum}`);\n          break;\n        }\n      }\n\n      // If no explanation found with patterns, try manual extraction\n      if (!explanation) {\n        const explKeywords = ['explanation', 'Explanation', 'EXPLANATION'];\n        for (const keyword of explKeywords) {\n          const explIndex = content.indexOf(keyword);\n          if (explIndex !== -1) {\n            // Extract everything after the keyword\n            let afterExpl = content.substring(explIndex + keyword.length).trim();\n\n            // Remove leading colons, spaces, newlines\n            afterExpl = afterExpl.replace(/^[:\\s\\n]+/, '');\n\n            // Take everything until next question or end\n            const nextQuestionIndex = afterExpl.search(/Question #\\d+/i);\n            if (nextQuestionIndex !== -1) {\n              afterExpl = afterExpl.substring(0, nextQuestionIndex);\n            }\n\n            if (afterExpl.length > 20) {\n              explanation = afterExpl.trim();\n              console.log(`Manual explanation extraction for question ${marker.questionNum}`);\n              break;\n            }\n          }\n        }\n      }\n\n      // Clean up explanation\n      if (explanation) {\n        // Normalize whitespace\n        explanation = explanation.replace(/\\s+/g, ' ');\n\n        // Remove common suffixes and prefixes\n        explanation = explanation.replace(/^\\s*[:\\-\\s]+/, ''); // Remove leading colons/dashes\n        explanation = explanation.replace(/\\(Module.*?\\)$/i, '');\n        explanation = explanation.replace(/\\(LOS.*?\\)$/i, '');\n        explanation = explanation.replace(/\\(Reading.*?\\)$/i, '');\n\n        // Remove trailing page numbers or references\n        explanation = explanation.replace(/\\s+\\d+\\s*$/, '');\n\n        explanation = explanation.trim();\n\n        console.log(`Cleaned explanation for question ${marker.questionNum}, final length: ${explanation.length}`);\n        console.log(`Explanation preview: ${explanation.substring(0, 100)}...`);\n      } else {\n        console.log(`No explanation found for question ${marker.questionNum}`);\n      }\n\n      if (correctAnswer) {\n        answers[marker.questionNum] = {\n          correctAnswer: correctAnswer,\n          explanation: explanation || 'No explanation available'\n        };\n      } else {\n        console.log(`No correct answer found for question ${marker.questionNum}`);\n      }\n    }\n\n    console.log(`Extracted answers for ${Object.keys(answers).length} questions`);\n    return answers;\n  }\n\n  async parseFiles(questionFile, answerFile) {\n    try {\n      const [questions, answers] = await Promise.all([\n        this.parseQuestionFile(questionFile),\n        this.parseAnswerFile(answerFile)\n      ]);\n      \n      return { questions, answers };\n    } catch (error) {\n      console.error('Error parsing files:', error);\n      throw error;\n    }\n  }\n}\n\nexport default CFAQuestionParser;\n"], "mappings": "AAAA;AACA,OAAO,KAAKA,QAAQ,MAAM,YAAY;;AAEtC;AACAA,QAAQ,CAACC,mBAAmB,CAACC,SAAS,GAAG,+DAA+D;AAExG,MAAMC,iBAAiB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;EACnB;EAEA,MAAMC,iBAAiBA,CAACC,IAAI,EAAE;IAC5B,IAAI;MACF,MAAMC,WAAW,GAAG,MAAMD,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5C,MAAMC,GAAG,GAAG,MAAMV,QAAQ,CAACW,WAAW,CAACF,WAAW,CAAC,CAACG,OAAO;MAE3D,IAAIC,QAAQ,GAAG,EAAE;;MAEjB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,GAAG,CAACK,QAAQ,EAAED,CAAC,EAAE,EAAE;QACtC,MAAME,IAAI,GAAG,MAAMN,GAAG,CAACO,OAAO,CAACH,CAAC,CAAC;QACjC,MAAMI,WAAW,GAAG,MAAMF,IAAI,CAACG,cAAc,CAAC,CAAC;;QAE/C;QACA,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAIC,KAAK,GAAG,IAAI;QAEhB,KAAK,MAAMC,IAAI,IAAIJ,WAAW,CAACK,KAAK,EAAE;UACpC;UACA,IAAIF,KAAK,KAAK,IAAI,IAAIG,IAAI,CAACC,GAAG,CAACH,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,GAAGL,KAAK,CAAC,GAAG,CAAC,EAAE;YAC7DD,QAAQ,IAAI,IAAI;UAClB;UAEAA,QAAQ,IAAIE,IAAI,CAACK,GAAG,GAAG,GAAG;UAC1BN,KAAK,GAAGC,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC;QAC3B;QAEAb,QAAQ,IAAIO,QAAQ,GAAG,MAAM;MAC/B;;MAEA;MACAQ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEhB,QAAQ,CAACiB,MAAM,CAAC;MACtDF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEhB,QAAQ,CAACkB,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;;MAElE;MACA,MAAM1B,SAAS,GAAG,IAAI,CAAC2B,gBAAgB,CAACnB,QAAQ,CAAC;MACjDe,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAExB,SAAS,CAACyB,MAAM,CAAC;MAErD,OAAOzB,SAAS;IAClB,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;EAEA,MAAMC,eAAeA,CAAC1B,IAAI,EAAE;IAC1B,IAAI;MACF,MAAMC,WAAW,GAAG,MAAMD,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5C,MAAMC,GAAG,GAAG,MAAMV,QAAQ,CAACW,WAAW,CAACF,WAAW,CAAC,CAACG,OAAO;MAE3D,IAAIC,QAAQ,GAAG,EAAE;;MAEjB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,GAAG,CAACK,QAAQ,EAAED,CAAC,EAAE,EAAE;QACtC,MAAME,IAAI,GAAG,MAAMN,GAAG,CAACO,OAAO,CAACH,CAAC,CAAC;QACjC,MAAMI,WAAW,GAAG,MAAMF,IAAI,CAACG,cAAc,CAAC,CAAC;;QAE/C;QACA,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAIC,KAAK,GAAG,IAAI;QAEhB,KAAK,MAAMC,IAAI,IAAIJ,WAAW,CAACK,KAAK,EAAE;UACpC;UACA,IAAIF,KAAK,KAAK,IAAI,IAAIG,IAAI,CAACC,GAAG,CAACH,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,GAAGL,KAAK,CAAC,GAAG,CAAC,EAAE;YAC7DD,QAAQ,IAAI,IAAI;UAClB;UAEAA,QAAQ,IAAIE,IAAI,CAACK,GAAG,GAAG,GAAG;UAC1BN,KAAK,GAAGC,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC;QAC3B;QAEAb,QAAQ,IAAIO,QAAQ,GAAG,MAAM;MAC/B;MAEAQ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEhB,QAAQ,CAACiB,MAAM,CAAC;MACxDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEhB,QAAQ,CAACkB,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;;MAEhE;MACA,MAAMzB,OAAO,GAAG,IAAI,CAAC6B,cAAc,CAACtB,QAAQ,CAAC;MAC7Ce,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,MAAM,CAACC,IAAI,CAAC/B,OAAO,CAAC,CAACwB,MAAM,CAAC;MAE9D,OAAOxB,OAAO;IAChB,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF;EAEAD,gBAAgBA,CAACM,IAAI,EAAE;IACrB,MAAMjC,SAAS,GAAG,EAAE;IAEpBuB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvDD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAES,IAAI,CAACR,MAAM,CAAC;;IAExC;IACA,MAAMS,cAAc,GAAGD,IAAI,CAACE,KAAK,CAAC,mBAAmB,CAAC;IACtDZ,OAAO,CAACC,GAAG,CAAC,cAAcU,cAAc,CAACT,MAAM,SAAS,CAAC;IAEzD,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,cAAc,CAACT,MAAM,EAAEhB,CAAC,EAAE,EAAE;MAC9C,MAAM2B,KAAK,GAAGF,cAAc,CAACzB,CAAC,CAAC,CAAC4B,IAAI,CAAC,CAAC;MACtC,IAAI,CAACD,KAAK,IAAIA,KAAK,CAACX,MAAM,GAAG,EAAE,EAAE;MAEjCF,OAAO,CAACC,GAAG,CAAC,0BAA0Bf,CAAC,MAAM,CAAC;MAC9Cc,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEY,KAAK,CAACV,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;;MAEpD;MACA,MAAMY,QAAQ,GAAG,CACf,6CAA6C,EAC7C,qDAAqD,EACrD,0BAA0B,EAC1B,iBAAiB,CAClB;MAED,IAAIC,YAAY,GAAG,IAAI;MACvB,KAAK,MAAMC,OAAO,IAAIF,QAAQ,EAAE;QAC9B,MAAMG,KAAK,GAAGL,KAAK,CAACK,KAAK,CAACD,OAAO,CAAC;QAClC,IAAIC,KAAK,EAAE;UACTF,YAAY,GAAG;YACbG,WAAW,EAAEC,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/BG,MAAM,EAAEH,KAAK,CAAC,CAAC,CAAC,IAAI,CAACI,KAAK,CAACF,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;YAC1EK,MAAM,EAAEL,KAAK,CAAC,CAAC,CAAC,IAAI,CAACI,KAAK,CAACF,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;YACzEM,UAAU,EAAEN,KAAK,CAAC,CAAC,CAAC,IAAI,SAAS;YACjCO,OAAO,EAAEP,KAAK,CAAC,CAAC,CAAC,CAACQ,QAAQ,CAAC,KAAK,CAAC;YACjCC,SAAS,EAAET,KAAK,CAAC,CAAC;UACpB,CAAC;UACD;QACF;MACF;MAEA,IAAI,CAACF,YAAY,EAAE;QACjBhB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD;MACF;MAEAD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEe,YAAY,CAAC;;MAEjD;MACA,MAAMY,WAAW,GAAGf,KAAK,CAACgB,OAAO,CAACb,YAAY,CAACW,SAAS,CAAC;MACzD,MAAMG,OAAO,GAAGjB,KAAK,CAACV,SAAS,CAACyB,WAAW,GAAGZ,YAAY,CAACW,SAAS,CAACzB,MAAM,CAAC,CAACY,IAAI,CAAC,CAAC;MAEnF,IAAIE,YAAY,CAACS,OAAO,EAAE;QACxB;QACAzB,OAAO,CAACC,GAAG,CAAC,8BAA8Be,YAAY,CAACG,WAAW,IAAIH,YAAY,CAACK,MAAM,EAAE,CAAC;;QAE5F;QACA,MAAMU,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAC/CF,OAAO,EACPd,YAAY,CAACG,WAAW,EACxBH,YAAY,CAACK,MAAM,EACnBL,YAAY,CAACO,MAAM,EACnBP,YAAY,CAACQ,UACf,CAAC;QAED,IAAIO,cAAc,CAAC7B,MAAM,GAAG,CAAC,EAAE;UAC7BzB,SAAS,CAACwD,IAAI,CAAC,GAAGF,cAAc,CAAC;QACnC,CAAC,MAAM;UACL;UACA,MAAMG,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CL,OAAO,EACPd,YAAY,CAACG,WAAW,EACxBH,YAAY,CAACO,MAAM,EACnBP,YAAY,CAACQ,UACf,CAAC;UACD,IAAIU,YAAY,EAAE;YAChBzD,SAAS,CAACwD,IAAI,CAACC,YAAY,CAAC;UAC9B;QACF;MACF,CAAC,MAAM;QACL;QACAlC,OAAO,CAACC,GAAG,CAAC,8BAA8Be,YAAY,CAACG,WAAW,EAAE,CAAC;QAErE,MAAMe,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CL,OAAO,EACPd,YAAY,CAACG,WAAW,EACxBH,YAAY,CAACO,MAAM,EACnBP,YAAY,CAACQ,UACf,CAAC;QAED,IAAIU,YAAY,EAAE;UAChBzD,SAAS,CAACwD,IAAI,CAACC,YAAY,CAAC;UAC5BlC,OAAO,CAACC,GAAG,CAAC,gCAAgCe,YAAY,CAACG,WAAW,EAAE,CAAC;QACzE,CAAC,MAAM;UACLnB,OAAO,CAACC,GAAG,CAAC,4BAA4Be,YAAY,CAACG,WAAW,EAAE,CAAC;QACrE;MACF;IACF;IAEAnB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5CD,OAAO,CAACC,GAAG,CAAC,8BAA8BxB,SAAS,CAACyB,MAAM,EAAE,CAAC;IAC7DF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAExB,SAAS,CAAC2D,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CAAC;IAE5F,OAAOhE,SAAS;EAClB;EAEAiE,2BAA2BA,CAAChC,IAAI,EAAE;IAChCV,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACrD,MAAMxB,SAAS,GAAG,EAAE;;IAEpB;IACA,MAAMsC,QAAQ,GAAG,CACf,mCAAmC;IAAG;IACtC,0DAA0D,CAAG;IAAA,CAC9D;IAED,KAAK,MAAME,OAAO,IAAIF,QAAQ,EAAE;MAC9B,MAAM4B,OAAO,GAAG,CAAC,GAAGjC,IAAI,CAACkC,QAAQ,CAAC3B,OAAO,CAAC,CAAC;MAC3CjB,OAAO,CAACC,GAAG,CAAC,6BAA6B0C,OAAO,CAACzC,MAAM,UAAU,CAAC;MAElE,KAAK,MAAMgB,KAAK,IAAIyB,OAAO,EAAE;QAC3B,MAAMxB,WAAW,GAAGC,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;QACtC,MAAMY,OAAO,GAAGZ,KAAK,CAAC,CAAC,CAAC,CAACJ,IAAI,CAAC,CAAC;QAE/B,IAAIgB,OAAO,CAAC5B,MAAM,GAAG,EAAE,EAAE;UAAE;UACzB,MAAMgC,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CL,OAAO,EACPX,WAAW,EACX,GAAG;UAAE;UACL,MAAM,GAAGA,WACX,CAAC;UAED,IAAIe,YAAY,EAAE;YAChBzD,SAAS,CAACwD,IAAI,CAACC,YAAY,CAAC;UAC9B;QACF;MACF;MAEA,IAAIzD,SAAS,CAACyB,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC;IACnC;IAEA,OAAOzB,SAAS;EAClB;EAEAuD,qBAAqBA,CAACF,OAAO,EAAEe,MAAM,EAAEC,IAAI,EAAEvB,MAAM,EAAEC,UAAU,EAAE;IAC/D,MAAM/C,SAAS,GAAG,EAAE;IAEpBuB,OAAO,CAACC,GAAG,CAAC,8BAA8B4C,MAAM,IAAIC,IAAI,IAAID,MAAM,EAAE,CAAC;IACrE7C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE6B,OAAO,CAAC3B,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;;IAEhE;IACA,IAAI4C,OAAO,GAAG,EAAE;IAChB,MAAMC,YAAY,GAAGlB,OAAO,CAACZ,KAAK,CAAC,+BAA+B,CAAC;IACnE,IAAI8B,YAAY,IAAIA,YAAY,CAAC,CAAC,CAAC,CAAClC,IAAI,CAAC,CAAC,CAACZ,MAAM,GAAG,EAAE,EAAE;MACtD6C,OAAO,GAAGC,YAAY,CAAC,CAAC,CAAC,CAAClC,IAAI,CAAC,CAAC;MAChCd,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE8C,OAAO,CAAC5C,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC9D;;IAEA;IACA,IAAI2C,IAAI,IAAIA,IAAI,GAAGD,MAAM,EAAE;MACzB7C,OAAO,CAACC,GAAG,CAAC,oCAAoC4C,MAAM,OAAOC,IAAI,EAAE,CAAC;;MAEpE;MACA,KAAK,IAAIG,IAAI,GAAGJ,MAAM,EAAEI,IAAI,IAAIH,IAAI,EAAEG,IAAI,EAAE,EAAE;QAC5C;QACA,MAAMlC,QAAQ,GAAG,CACf,IAAImC,MAAM,CAAC,GAAGD,IAAI,cAAcA,IAAI,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EACtD,IAAIC,MAAM,CAAC,eAAeD,IAAI,8BAA8BA,IAAI,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CACjF;QAED,IAAIE,eAAe,GAAG,EAAE;QACxB,KAAK,MAAMlC,OAAO,IAAIF,QAAQ,EAAE;UAC9B,MAAMG,KAAK,GAAGY,OAAO,CAACZ,KAAK,CAACD,OAAO,CAAC;UACpC,IAAIC,KAAK,EAAE;YACTiC,eAAe,GAAGjC,KAAK,CAAC,CAAC,CAAC,CAACJ,IAAI,CAAC,CAAC;YACjC;UACF;QACF;QAEA,IAAIqC,eAAe,EAAE;UACnBnD,OAAO,CAACC,GAAG,CAAC,8BAA8BgD,IAAI,EAAE,CAAC;UACjD,MAAMf,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CgB,eAAe,EACfF,IAAI,EACJ1B,MAAM,EACN,GAAGC,UAAU,IAAIyB,IAAI,EAAE,EACvBF,OACF,CAAC;UAED,IAAIb,YAAY,EAAE;YAChBzD,SAAS,CAACwD,IAAI,CAACC,YAAY,CAAC;UAC9B;QACF;MACF;IACF;;IAEA;IACA,IAAIzD,SAAS,CAACyB,MAAM,KAAK,CAAC,EAAE;MAC1BF,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;MACtF,MAAMiC,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CL,OAAO,EACPe,MAAM,EACNtB,MAAM,EACNC,UAAU,EACVuB,OACF,CAAC;MAED,IAAIb,YAAY,EAAE;QAChBzD,SAAS,CAACwD,IAAI,CAACC,YAAY,CAAC;MAC9B;IACF;IAEAlC,OAAO,CAACC,GAAG,CAAC,aAAaxB,SAAS,CAACyB,MAAM,uBAAuB,CAAC;IACjE,OAAOzB,SAAS;EAClB;EAEA0D,mBAAmBA,CAACL,OAAO,EAAEX,WAAW,EAAEI,MAAM,EAAEC,UAAU,EAAEuB,OAAO,GAAG,EAAE,EAAE;IAC1E,IAAI,CAACjB,OAAO,CAAChB,IAAI,CAAC,CAAC,EAAE,OAAO,IAAI;IAEhCd,OAAO,CAACC,GAAG,CAAC,0BAA0BkB,WAAW,MAAM,CAAC;IACxDnB,OAAO,CAACC,GAAG,CAAC,mBAAmB6B,OAAO,CAAC5B,MAAM,EAAE,CAAC;IAChDF,OAAO,CAACC,GAAG,CAAC,oBAAoB6B,OAAO,CAAC3B,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;IAE/D,IAAIiD,YAAY,GAAG,EAAE;IACrB,MAAMC,OAAO,GAAG,CAAC,CAAC;;IAElB;IACA,MAAMC,aAAa,GAAG,yCAAyC;IAC/D,MAAMC,aAAa,GAAG,CAAC,GAAGzB,OAAO,CAACc,QAAQ,CAACU,aAAa,CAAC,CAAC;IAE1DtD,OAAO,CAACC,GAAG,CAAC,SAASsD,aAAa,CAACrD,MAAM,iBAAiB,CAAC;IAE3D,IAAIqD,aAAa,CAACrD,MAAM,IAAI,CAAC,EAAE;MAC7B;MACA,MAAMsD,gBAAgB,GAAGD,aAAa,CAAC,CAAC,CAAC,CAACE,KAAK;MAC/CL,YAAY,GAAGtB,OAAO,CAAC3B,SAAS,CAAC,CAAC,EAAEqD,gBAAgB,CAAC,CAAC1C,IAAI,CAAC,CAAC;;MAE5D;MACAyC,aAAa,CAACG,OAAO,CAACxC,KAAK,IAAI;QAC7B,MAAMyC,MAAM,GAAGzC,KAAK,CAAC,CAAC,CAAC;QACvB,MAAMR,IAAI,GAAGQ,KAAK,CAAC,CAAC,CAAC,CAACJ,IAAI,CAAC,CAAC;QAC5B,IAAIJ,IAAI,CAACR,MAAM,GAAG,CAAC,EAAE;UACnBmD,OAAO,CAACM,MAAM,CAAC,GAAGjD,IAAI;QACxB;MACF,CAAC,CAAC;MAEFV,OAAO,CAACC,GAAG,CAAC,qBAAqBO,MAAM,CAACC,IAAI,CAAC4C,OAAO,CAAC,CAACnD,MAAM,oBAAoB,CAAC;IACnF,CAAC,MAAM;MACL;MACAF,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MAEnD,MAAM2D,KAAK,GAAG9B,OAAO,CAAClB,KAAK,CAAC,IAAI,CAAC,CAACwB,GAAG,CAACyB,IAAI,IAAIA,IAAI,CAAC/C,IAAI,CAAC,CAAC,CAAC,CAACgD,MAAM,CAACD,IAAI,IAAIA,IAAI,CAAC3D,MAAM,GAAG,CAAC,CAAC;MAE1F,IAAI6D,aAAa,GAAG,IAAI;MACxB,IAAIC,UAAU,GAAG,EAAE;MACnB,IAAIC,aAAa,GAAG,EAAE;MACtB,IAAIC,gBAAgB,GAAG,KAAK;MAE5B,KAAK,MAAML,IAAI,IAAID,KAAK,EAAE;QACxB;QACA,MAAMO,WAAW,GAAGN,IAAI,CAAC3C,KAAK,CAAC,mBAAmB,CAAC;QAEnD,IAAIiD,WAAW,EAAE;UACf;UACA,IAAIJ,aAAa,IAAIC,UAAU,CAAClD,IAAI,CAAC,CAAC,EAAE;YACtCuC,OAAO,CAACU,aAAa,CAAC,GAAGC,UAAU,CAAClD,IAAI,CAAC,CAAC;UAC5C;UAEAiD,aAAa,GAAGI,WAAW,CAAC,CAAC,CAAC;UAC9BH,UAAU,GAAGG,WAAW,CAAC,CAAC,CAAC;UAC3BD,gBAAgB,GAAG,IAAI;QAEzB,CAAC,MAAM,IAAIH,aAAa,IAAIG,gBAAgB,EAAE;UAC5C;UACAF,UAAU,IAAI,GAAG,GAAGH,IAAI;QAE1B,CAAC,MAAM,IAAI,CAACK,gBAAgB,EAAE;UAC5B;UACAD,aAAa,CAAChC,IAAI,CAAC4B,IAAI,CAAC;QAC1B;MACF;;MAEA;MACA,IAAIE,aAAa,IAAIC,UAAU,CAAClD,IAAI,CAAC,CAAC,EAAE;QACtCuC,OAAO,CAACU,aAAa,CAAC,GAAGC,UAAU,CAAClD,IAAI,CAAC,CAAC;MAC5C;MAEAsC,YAAY,GAAGa,aAAa,CAACG,IAAI,CAAC,GAAG,CAAC,CAACtD,IAAI,CAAC,CAAC;MAC7Cd,OAAO,CAACC,GAAG,CAAC,oBAAoBO,MAAM,CAACC,IAAI,CAAC4C,OAAO,CAAC,CAACnD,MAAM,8BAA8BkD,YAAY,CAAClD,MAAM,EAAE,CAAC;IACjH;;IAEA;IACA,IAAIM,MAAM,CAACC,IAAI,CAAC4C,OAAO,CAAC,CAACnD,MAAM,GAAG,CAAC,IAAI4B,OAAO,CAAC5B,MAAM,GAAG,GAAG,EAAE;MAC3DF,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;;MAEjD;MACA,MAAMoE,iBAAiB,GAAG,kDAAkD;MAC5E,MAAMC,iBAAiB,GAAG,CAAC,GAAGxC,OAAO,CAACc,QAAQ,CAACyB,iBAAiB,CAAC,CAAC;MAElE,IAAIC,iBAAiB,CAACpE,MAAM,IAAI,CAAC,EAAE;QACjC;QACAM,MAAM,CAACC,IAAI,CAAC4C,OAAO,CAAC,CAACK,OAAO,CAACa,GAAG,IAAI,OAAOlB,OAAO,CAACkB,GAAG,CAAC,CAAC;;QAExD;QACA,MAAMf,gBAAgB,GAAGc,iBAAiB,CAAC,CAAC,CAAC,CAACb,KAAK;QACnDL,YAAY,GAAGtB,OAAO,CAAC3B,SAAS,CAAC,CAAC,EAAEqD,gBAAgB,CAAC,CAAC1C,IAAI,CAAC,CAAC;;QAE5D;QACAwD,iBAAiB,CAACZ,OAAO,CAACxC,KAAK,IAAI;UACjC,MAAMyC,MAAM,GAAGzC,KAAK,CAAC,CAAC,CAAC;UACvB,MAAMR,IAAI,GAAGQ,KAAK,CAAC,CAAC,CAAC,CAACJ,IAAI,CAAC,CAAC;UAC5BuC,OAAO,CAACM,MAAM,CAAC,GAAGjD,IAAI;QACxB,CAAC,CAAC;QAEFV,OAAO,CAACC,GAAG,CAAC,qBAAqBO,MAAM,CAACC,IAAI,CAAC4C,OAAO,CAAC,CAACnD,MAAM,oBAAoB,CAAC;MACnF;IACF;;IAEA;IACAkD,YAAY,GAAGA,YAAY,CAACoB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC1D,IAAI,CAAC,CAAC;IACvDN,MAAM,CAACC,IAAI,CAAC4C,OAAO,CAAC,CAACK,OAAO,CAACa,GAAG,IAAI;MAClClB,OAAO,CAACkB,GAAG,CAAC,GAAGlB,OAAO,CAACkB,GAAG,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC1D,IAAI,CAAC,CAAC;MACvD;MACA,IAAI,CAACuC,OAAO,CAACkB,GAAG,CAAC,EAAE;QACjB,OAAOlB,OAAO,CAACkB,GAAG,CAAC;MACrB;IACF,CAAC,CAAC;IAEFvE,OAAO,CAACC,GAAG,CAAC,6BAA6BkB,WAAW,GAAG,CAAC;IACxDnB,OAAO,CAACC,GAAG,CAAC,oBAAoBmD,YAAY,CAAClD,MAAM,QAAQ,CAAC;IAC5DF,OAAO,CAACC,GAAG,CAAC,cAAcO,MAAM,CAACC,IAAI,CAAC4C,OAAO,CAAC,CAACe,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC5DpE,OAAO,CAACC,GAAG,CAAC,cAAc8C,OAAO,GAAG,KAAK,GAAG,IAAI,EAAE,CAAC;;IAEnD;IACA,IAAI,CAACK,YAAY,IAAI5C,MAAM,CAACC,IAAI,CAAC4C,OAAO,CAAC,CAACnD,MAAM,KAAK,CAAC,EAAE;MACtDF,OAAO,CAACC,GAAG,CAAC,YAAYkB,WAAW,4BAA4B,CAAC;MAChE,OAAO,IAAI;IACb;;IAEA;IACA,MAAMsD,eAAe,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACjD,MAAMC,cAAc,GAAGD,eAAe,CAACX,MAAM,CAACa,CAAC,IAAI,CAACtB,OAAO,CAACsB,CAAC,CAAC,CAAC;IAE/D,IAAID,cAAc,CAACxE,MAAM,GAAG,CAAC,EAAE;MAC7BF,OAAO,CAACC,GAAG,CAAC,YAAYkB,WAAW,iDAAiD,CAAC;MACrFuD,cAAc,CAAChB,OAAO,CAACC,MAAM,IAAI;QAC/BN,OAAO,CAACM,MAAM,CAAC,GAAG,UAAUA,MAAM,qBAAqB;MACzD,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAACP,YAAY,CAACtC,IAAI,CAAC,CAAC,EAAE;MACxBsC,YAAY,GAAG,YAAYjC,WAAW,2CAA2C;IACnF;IAEA,OAAO;MACLmB,cAAc,EAAEnB,WAAW;MAC3ByD,cAAc,EAAErD,MAAM;MACtBC,UAAU,EAAEA,UAAU;MACtBuB,OAAO,EAAEA,OAAO;MAChBK,YAAY,EAAEA,YAAY;MAC1BC,OAAO,EAAEA,OAAO;MAChBwB,eAAe,EAAEC,OAAO,CAAC/B,OAAO;IAClC,CAAC;EACH;EAEAxC,cAAcA,CAACG,IAAI,EAAE;IACnB,MAAMhC,OAAO,GAAG,CAAC,CAAC;IAElBsB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;;IAE9C;IACA,MAAM8E,eAAe,GAAG,EAAE;IAC1B,MAAMhE,QAAQ,GAAG,CACf,8CAA8C,EAC9C,kBAAkB,CACnB;IAED,KAAK,MAAME,OAAO,IAAIF,QAAQ,EAAE;MAC9B,IAAIG,KAAK;MACT,OAAO,CAACA,KAAK,GAAGD,OAAO,CAAC+D,IAAI,CAACtE,IAAI,CAAC,MAAM,IAAI,EAAE;QAC5CqE,eAAe,CAAC9C,IAAI,CAAC;UACnBwB,KAAK,EAAEvC,KAAK,CAACuC,KAAK;UAClB9B,SAAS,EAAET,KAAK,CAAC,CAAC,CAAC;UACnBC,WAAW,EAAEC,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC;MACJ;IACF;IAEAlB,OAAO,CAACC,GAAG,CAAC,SAAS8E,eAAe,CAAC7E,MAAM,kCAAkC,CAAC;;IAE9E;IACA6E,eAAe,CAACxC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACiB,KAAK,GAAGhB,CAAC,CAACgB,KAAK,CAAC;;IAEjD;IACA,KAAK,IAAIvE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6F,eAAe,CAAC7E,MAAM,EAAEhB,CAAC,EAAE,EAAE;MAC/C,MAAM+F,MAAM,GAAGF,eAAe,CAAC7F,CAAC,CAAC;MACjC,MAAMgG,UAAU,GAAGH,eAAe,CAAC7F,CAAC,GAAG,CAAC,CAAC;;MAEzC;MACA,MAAMiG,UAAU,GAAGF,MAAM,CAACxB,KAAK,GAAGwB,MAAM,CAACtD,SAAS,CAACzB,MAAM;MACzD,MAAMkF,QAAQ,GAAGF,UAAU,GAAGA,UAAU,CAACzB,KAAK,GAAG/C,IAAI,CAACR,MAAM;MAC5D,MAAM4B,OAAO,GAAGpB,IAAI,CAACP,SAAS,CAACgF,UAAU,EAAEC,QAAQ,CAAC,CAACtE,IAAI,CAAC,CAAC;MAE3Dd,OAAO,CAACC,GAAG,CAAC,kCAAkCgF,MAAM,CAAC9D,WAAW,qBAAqBW,OAAO,CAAC5B,MAAM,EAAE,CAAC;;MAEtG;MACA,IAAImF,aAAa,GAAG,IAAI;MACxB,MAAMC,cAAc,GAAG,CACrB,kCAAkC,EAClC,kCAAkC,EAClC,4BAA4B,EAC5B,oBAAoB,EACpB,uBAAuB,EACvB,gCAAgC,CACjC;MAED,KAAK,MAAMrE,OAAO,IAAIqE,cAAc,EAAE;QACpC,MAAMpE,KAAK,GAAGY,OAAO,CAACZ,KAAK,CAACD,OAAO,CAAC;QACpC,IAAIC,KAAK,EAAE;UACTmE,aAAa,GAAGnE,KAAK,CAAC,CAAC,CAAC,CAACqE,WAAW,CAAC,CAAC;UACtCvF,OAAO,CAACC,GAAG,CAAC,wBAAwBoF,aAAa,iBAAiBJ,MAAM,CAAC9D,WAAW,EAAE,CAAC;UACvF;QACF;MACF;;MAEA;MACA,IAAIqE,WAAW,GAAG,EAAE;MAEpBxF,OAAO,CAACC,GAAG,CAAC,uCAAuCgF,MAAM,CAAC9D,WAAW,aAAa,CAAC;MACnFnB,OAAO,CAACC,GAAG,CAAC,oBAAoB6B,OAAO,CAAC3B,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;;MAE/D;MACA,MAAMsF,mBAAmB,GAAG,CAC1B,6CAA6C,EAC7C,wCAAwC,EACxC,uCAAuC,EACvC,yBAAyB,CAC1B;MAED,KAAK,IAAIvG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuG,mBAAmB,CAACvF,MAAM,EAAEhB,CAAC,EAAE,EAAE;QACnD,MAAM+B,OAAO,GAAGwE,mBAAmB,CAACvG,CAAC,CAAC;QACtC,MAAMgC,KAAK,GAAGY,OAAO,CAACZ,KAAK,CAACD,OAAO,CAAC;QACpC,IAAIC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACJ,IAAI,CAAC,CAAC,CAACZ,MAAM,GAAG,EAAE,EAAE;UACxCsF,WAAW,GAAGtE,KAAK,CAAC,CAAC,CAAC,CAACJ,IAAI,CAAC,CAAC;UAC7Bd,OAAO,CAACC,GAAG,CAAC,mCAAmCf,CAAC,GAAG,CAAC,iBAAiB+F,MAAM,CAAC9D,WAAW,EAAE,CAAC;UAC1F;QACF;MACF;;MAEA;MACA,IAAI,CAACqE,WAAW,EAAE;QAChB,MAAME,YAAY,GAAG,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;QAClE,KAAK,MAAMC,OAAO,IAAID,YAAY,EAAE;UAClC,MAAME,SAAS,GAAG9D,OAAO,CAACD,OAAO,CAAC8D,OAAO,CAAC;UAC1C,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;YACpB;YACA,IAAIC,SAAS,GAAG/D,OAAO,CAAC3B,SAAS,CAACyF,SAAS,GAAGD,OAAO,CAACzF,MAAM,CAAC,CAACY,IAAI,CAAC,CAAC;;YAEpE;YACA+E,SAAS,GAAGA,SAAS,CAACrB,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;;YAE9C;YACA,MAAMsB,iBAAiB,GAAGD,SAAS,CAACE,MAAM,CAAC,gBAAgB,CAAC;YAC5D,IAAID,iBAAiB,KAAK,CAAC,CAAC,EAAE;cAC5BD,SAAS,GAAGA,SAAS,CAAC1F,SAAS,CAAC,CAAC,EAAE2F,iBAAiB,CAAC;YACvD;YAEA,IAAID,SAAS,CAAC3F,MAAM,GAAG,EAAE,EAAE;cACzBsF,WAAW,GAAGK,SAAS,CAAC/E,IAAI,CAAC,CAAC;cAC9Bd,OAAO,CAACC,GAAG,CAAC,8CAA8CgF,MAAM,CAAC9D,WAAW,EAAE,CAAC;cAC/E;YACF;UACF;QACF;MACF;;MAEA;MACA,IAAIqE,WAAW,EAAE;QACf;QACAA,WAAW,GAAGA,WAAW,CAAChB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;;QAE9C;QACAgB,WAAW,GAAGA,WAAW,CAAChB,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC;QACvDgB,WAAW,GAAGA,WAAW,CAAChB,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;QACxDgB,WAAW,GAAGA,WAAW,CAAChB,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;QACrDgB,WAAW,GAAGA,WAAW,CAAChB,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;;QAEzD;QACAgB,WAAW,GAAGA,WAAW,CAAChB,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;QAEnDgB,WAAW,GAAGA,WAAW,CAAC1E,IAAI,CAAC,CAAC;QAEhCd,OAAO,CAACC,GAAG,CAAC,oCAAoCgF,MAAM,CAAC9D,WAAW,mBAAmBqE,WAAW,CAACtF,MAAM,EAAE,CAAC;QAC1GF,OAAO,CAACC,GAAG,CAAC,wBAAwBuF,WAAW,CAACrF,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;MACzE,CAAC,MAAM;QACLH,OAAO,CAACC,GAAG,CAAC,qCAAqCgF,MAAM,CAAC9D,WAAW,EAAE,CAAC;MACxE;MAEA,IAAIkE,aAAa,EAAE;QACjB3G,OAAO,CAACuG,MAAM,CAAC9D,WAAW,CAAC,GAAG;UAC5BkE,aAAa,EAAEA,aAAa;UAC5BG,WAAW,EAAEA,WAAW,IAAI;QAC9B,CAAC;MACH,CAAC,MAAM;QACLxF,OAAO,CAACC,GAAG,CAAC,wCAAwCgF,MAAM,CAAC9D,WAAW,EAAE,CAAC;MAC3E;IACF;IAEAnB,OAAO,CAACC,GAAG,CAAC,yBAAyBO,MAAM,CAACC,IAAI,CAAC/B,OAAO,CAAC,CAACwB,MAAM,YAAY,CAAC;IAC7E,OAAOxB,OAAO;EAChB;EAEA,MAAMsH,UAAUA,CAACC,YAAY,EAAEC,UAAU,EAAE;IACzC,IAAI;MACF,MAAM,CAACzH,SAAS,EAAEC,OAAO,CAAC,GAAG,MAAMyH,OAAO,CAACC,GAAG,CAAC,CAC7C,IAAI,CAACzH,iBAAiB,CAACsH,YAAY,CAAC,EACpC,IAAI,CAAC3F,eAAe,CAAC4F,UAAU,CAAC,CACjC,CAAC;MAEF,OAAO;QAAEzH,SAAS;QAAEC;MAAQ,CAAC;IAC/B,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF;AACF;AAEA,eAAe9B,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}