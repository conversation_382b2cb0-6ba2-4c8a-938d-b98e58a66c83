{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "map": {"version": 3, "names": ["React", "classNames", "useBootstrapPrefix", "divWithClassName", "jsx", "_jsx", "DivStyledAsH6", "CardSubtitle", "forwardRef", "className", "bsPrefix", "as", "Component", "props", "ref", "displayName"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/react-bootstrap/esm/CardSubtitle.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAGH,gBAAgB,CAAC,IAAI,CAAC;AAC5C,MAAMI,YAAY,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAC;EAClDC,SAAS;EACTC,QAAQ;EACRC,EAAE,EAAEC,SAAS,GAAGN,aAAa;EAC7B,GAAGO;AACL,CAAC,EAAEC,GAAG,KAAK;EACTJ,QAAQ,GAAGR,kBAAkB,CAACQ,QAAQ,EAAE,eAAe,CAAC;EACxD,OAAO,aAAaL,IAAI,CAACO,SAAS,EAAE;IAClCE,GAAG,EAAEA,GAAG;IACRL,SAAS,EAAER,UAAU,CAACQ,SAAS,EAAEC,QAAQ,CAAC;IAC1C,GAAGG;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,YAAY,CAACQ,WAAW,GAAG,cAAc;AACzC,eAAeR,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}