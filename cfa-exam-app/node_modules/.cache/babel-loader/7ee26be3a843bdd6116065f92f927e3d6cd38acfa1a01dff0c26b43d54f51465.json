{"ast": null, "code": "import * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport BaseTabs from '@restart/ui/Tabs';\nimport Nav from './Nav';\nimport NavLink from './NavLink';\nimport NavItem from './NavItem';\nimport TabContent from './TabContent';\nimport TabPane from './TabPane';\nimport { forEach, map } from './ElementChildren';\nimport getTabTransitionComponent from './getTabTransitionComponent';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction getDefaultActiveKey(children) {\n  let defaultActiveKey;\n  forEach(children, child => {\n    if (defaultActiveKey == null) {\n      defaultActiveKey = child.props.eventKey;\n    }\n  });\n  return defaultActiveKey;\n}\nfunction renderTab(child) {\n  const {\n    title,\n    eventKey,\n    disabled,\n    tabClassName,\n    tabAttrs,\n    id\n  } = child.props;\n  if (title == null) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(NavItem, {\n    as: \"li\",\n    role: \"presentation\",\n    children: /*#__PURE__*/_jsx(NavLink, {\n      as: \"button\",\n      type: \"button\",\n      eventKey: eventKey,\n      disabled: disabled,\n      id: id,\n      className: tabClassName,\n      ...tabAttrs,\n      children: title\n    })\n  });\n}\nconst Tabs = props => {\n  const {\n    id,\n    onSelect,\n    transition,\n    mountOnEnter = false,\n    unmountOnExit = false,\n    variant = 'tabs',\n    children,\n    activeKey = getDefaultActiveKey(children),\n    ...controlledProps\n  } = useUncontrolled(props, {\n    activeKey: 'onSelect'\n  });\n  return /*#__PURE__*/_jsxs(BaseTabs, {\n    id: id,\n    activeKey: activeKey,\n    onSelect: onSelect,\n    transition: getTabTransitionComponent(transition),\n    mountOnEnter: mountOnEnter,\n    unmountOnExit: unmountOnExit,\n    children: [/*#__PURE__*/_jsx(Nav, {\n      id: id,\n      ...controlledProps,\n      role: \"tablist\",\n      as: \"ul\",\n      variant: variant,\n      children: map(children, renderTab)\n    }), /*#__PURE__*/_jsx(TabContent, {\n      children: map(children, child => {\n        const childProps = {\n          ...child.props\n        };\n        delete childProps.title;\n        delete childProps.disabled;\n        delete childProps.tabClassName;\n        delete childProps.tabAttrs;\n        return /*#__PURE__*/_jsx(TabPane, {\n          ...childProps\n        });\n      })\n    })]\n  });\n};\nTabs.displayName = 'Tabs';\nexport default Tabs;", "map": {"version": 3, "names": ["React", "useUncontrolled", "BaseTabs", "Nav", "NavLink", "NavItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabPane", "for<PERSON>ach", "map", "getTabTransitionComponent", "jsx", "_jsx", "jsxs", "_jsxs", "getDefaultActiveKey", "children", "defaultActiveKey", "child", "props", "eventKey", "renderTab", "title", "disabled", "tabClassName", "tabAttrs", "id", "as", "role", "type", "className", "Tabs", "onSelect", "transition", "mountOnEnter", "unmountOnExit", "variant", "active<PERSON><PERSON>", "controlledProps", "childProps", "displayName"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/react-bootstrap/esm/Tabs.js"], "sourcesContent": ["import * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport BaseTabs from '@restart/ui/Tabs';\nimport Nav from './Nav';\nimport NavLink from './NavLink';\nimport NavItem from './NavItem';\nimport TabContent from './TabContent';\nimport TabPane from './TabPane';\nimport { forEach, map } from './ElementChildren';\nimport getTabTransitionComponent from './getTabTransitionComponent';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction getDefaultActiveKey(children) {\n  let defaultActiveKey;\n  forEach(children, child => {\n    if (defaultActiveKey == null) {\n      defaultActiveKey = child.props.eventKey;\n    }\n  });\n  return defaultActiveKey;\n}\nfunction renderTab(child) {\n  const {\n    title,\n    eventKey,\n    disabled,\n    tabClassName,\n    tabAttrs,\n    id\n  } = child.props;\n  if (title == null) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(NavItem, {\n    as: \"li\",\n    role: \"presentation\",\n    children: /*#__PURE__*/_jsx(NavLink, {\n      as: \"button\",\n      type: \"button\",\n      eventKey: eventKey,\n      disabled: disabled,\n      id: id,\n      className: tabClassName,\n      ...tabAttrs,\n      children: title\n    })\n  });\n}\nconst Tabs = props => {\n  const {\n    id,\n    onSelect,\n    transition,\n    mountOnEnter = false,\n    unmountOnExit = false,\n    variant = 'tabs',\n    children,\n    activeKey = getDefaultActiveKey(children),\n    ...controlledProps\n  } = useUncontrolled(props, {\n    activeKey: 'onSelect'\n  });\n  return /*#__PURE__*/_jsxs(BaseTabs, {\n    id: id,\n    activeKey: activeKey,\n    onSelect: onSelect,\n    transition: getTabTransitionComponent(transition),\n    mountOnEnter: mountOnEnter,\n    unmountOnExit: unmountOnExit,\n    children: [/*#__PURE__*/_jsx(Nav, {\n      id: id,\n      ...controlledProps,\n      role: \"tablist\",\n      as: \"ul\",\n      variant: variant,\n      children: map(children, renderTab)\n    }), /*#__PURE__*/_jsx(TabContent, {\n      children: map(children, child => {\n        const childProps = {\n          ...child.props\n        };\n        delete childProps.title;\n        delete childProps.disabled;\n        delete childProps.tabClassName;\n        delete childProps.tabAttrs;\n        return /*#__PURE__*/_jsx(TabPane, {\n          ...childProps\n        });\n      })\n    })]\n  });\n};\nTabs.displayName = 'Tabs';\nexport default Tabs;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,gBAAgB;AAChD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,OAAO,EAAEC,GAAG,QAAQ,mBAAmB;AAChD,OAAOC,yBAAyB,MAAM,6BAA6B;AACnE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,SAASC,mBAAmBA,CAACC,QAAQ,EAAE;EACrC,IAAIC,gBAAgB;EACpBT,OAAO,CAACQ,QAAQ,EAAEE,KAAK,IAAI;IACzB,IAAID,gBAAgB,IAAI,IAAI,EAAE;MAC5BA,gBAAgB,GAAGC,KAAK,CAACC,KAAK,CAACC,QAAQ;IACzC;EACF,CAAC,CAAC;EACF,OAAOH,gBAAgB;AACzB;AACA,SAASI,SAASA,CAACH,KAAK,EAAE;EACxB,MAAM;IACJI,KAAK;IACLF,QAAQ;IACRG,QAAQ;IACRC,YAAY;IACZC,QAAQ;IACRC;EACF,CAAC,GAAGR,KAAK,CAACC,KAAK;EACf,IAAIG,KAAK,IAAI,IAAI,EAAE;IACjB,OAAO,IAAI;EACb;EACA,OAAO,aAAaV,IAAI,CAACP,OAAO,EAAE;IAChCsB,EAAE,EAAE,IAAI;IACRC,IAAI,EAAE,cAAc;IACpBZ,QAAQ,EAAE,aAAaJ,IAAI,CAACR,OAAO,EAAE;MACnCuB,EAAE,EAAE,QAAQ;MACZE,IAAI,EAAE,QAAQ;MACdT,QAAQ,EAAEA,QAAQ;MAClBG,QAAQ,EAAEA,QAAQ;MAClBG,EAAE,EAAEA,EAAE;MACNI,SAAS,EAAEN,YAAY;MACvB,GAAGC,QAAQ;MACXT,QAAQ,EAAEM;IACZ,CAAC;EACH,CAAC,CAAC;AACJ;AACA,MAAMS,IAAI,GAAGZ,KAAK,IAAI;EACpB,MAAM;IACJO,EAAE;IACFM,QAAQ;IACRC,UAAU;IACVC,YAAY,GAAG,KAAK;IACpBC,aAAa,GAAG,KAAK;IACrBC,OAAO,GAAG,MAAM;IAChBpB,QAAQ;IACRqB,SAAS,GAAGtB,mBAAmB,CAACC,QAAQ,CAAC;IACzC,GAAGsB;EACL,CAAC,GAAGrC,eAAe,CAACkB,KAAK,EAAE;IACzBkB,SAAS,EAAE;EACb,CAAC,CAAC;EACF,OAAO,aAAavB,KAAK,CAACZ,QAAQ,EAAE;IAClCwB,EAAE,EAAEA,EAAE;IACNW,SAAS,EAAEA,SAAS;IACpBL,QAAQ,EAAEA,QAAQ;IAClBC,UAAU,EAAEvB,yBAAyB,CAACuB,UAAU,CAAC;IACjDC,YAAY,EAAEA,YAAY;IAC1BC,aAAa,EAAEA,aAAa;IAC5BnB,QAAQ,EAAE,CAAC,aAAaJ,IAAI,CAACT,GAAG,EAAE;MAChCuB,EAAE,EAAEA,EAAE;MACN,GAAGY,eAAe;MAClBV,IAAI,EAAE,SAAS;MACfD,EAAE,EAAE,IAAI;MACRS,OAAO,EAAEA,OAAO;MAChBpB,QAAQ,EAAEP,GAAG,CAACO,QAAQ,EAAEK,SAAS;IACnC,CAAC,CAAC,EAAE,aAAaT,IAAI,CAACN,UAAU,EAAE;MAChCU,QAAQ,EAAEP,GAAG,CAACO,QAAQ,EAAEE,KAAK,IAAI;QAC/B,MAAMqB,UAAU,GAAG;UACjB,GAAGrB,KAAK,CAACC;QACX,CAAC;QACD,OAAOoB,UAAU,CAACjB,KAAK;QACvB,OAAOiB,UAAU,CAAChB,QAAQ;QAC1B,OAAOgB,UAAU,CAACf,YAAY;QAC9B,OAAOe,UAAU,CAACd,QAAQ;QAC1B,OAAO,aAAab,IAAI,CAACL,OAAO,EAAE;UAChC,GAAGgC;QACL,CAAC,CAAC;MACJ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACDR,IAAI,CAACS,WAAW,GAAG,MAAM;AACzB,eAAeT,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}