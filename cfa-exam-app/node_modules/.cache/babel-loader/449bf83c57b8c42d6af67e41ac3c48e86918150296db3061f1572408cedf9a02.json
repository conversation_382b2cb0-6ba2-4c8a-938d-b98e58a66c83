{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Ta\\u0300i lie\\u0323\\u0302u CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/QuestionDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Card, Button, Alert, Badge, Row, Col } from 'react-bootstrap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuestionDisplay = ({\n  question,\n  groupQuestions,\n  answers,\n  userAnswer,\n  showAnswer,\n  onSubmitAnswer\n}) => {\n  _s();\n  var _groupQuestions$, _groupQuestions;\n  const [selectedAnswer, setSelectedAnswer] = useState(userAnswer || '');\n  const handleAnswerSelect = choice => {\n    if (showAnswer) return; // Prevent changing answer after submission\n    setSelectedAnswer(choice);\n  };\n  const handleSubmit = () => {\n    if (!selectedAnswer) return;\n    onSubmitAnswer(question.questionNumber, selectedAnswer);\n  };\n  const getAnswerData = () => {\n    return answers[question.questionNumber] || {};\n  };\n  const isCorrect = () => {\n    const answerData = getAnswerData();\n    return userAnswer === answerData.correctAnswer;\n  };\n  const getChoiceVariant = choice => {\n    if (!showAnswer) {\n      return selectedAnswer === choice ? 'primary' : 'outline-secondary';\n    }\n    const answerData = getAnswerData();\n    if (choice === answerData.correctAnswer) {\n      return 'success';\n    } else if (choice === userAnswer && choice !== answerData.correctAnswer) {\n      return 'danger';\n    }\n    return 'outline-secondary';\n  };\n  const getChoiceIcon = choice => {\n    if (!showAnswer) {\n      return selectedAnswer === choice ? 'fas fa-check-circle' : 'far fa-circle';\n    }\n    const answerData = getAnswerData();\n    if (choice === answerData.correctAnswer) {\n      return 'fas fa-check-circle';\n    } else if (choice === userAnswer && choice !== answerData.correctAnswer) {\n      return 'fas fa-times-circle';\n    }\n    return 'far fa-circle';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"question-display\",\n    children: [question.isGroupQuestion && question.context && /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-4 border-info\",\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        className: \"bg-info text-white\",\n        children: /*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-info-circle me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this), \"Th\\xF4ng tin chung cho c\\xE2u h\\u1ECFi \", (_groupQuestions$ = groupQuestions[0]) === null || _groupQuestions$ === void 0 ? void 0 : _groupQuestions$.questionNumber, \" - \", (_groupQuestions = groupQuestions[groupQuestions.length - 1]) === null || _groupQuestions === void 0 ? void 0 : _groupQuestions.questionNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"context-content\",\n          style: {\n            whiteSpace: 'pre-line',\n            lineHeight: '1.6'\n          },\n          children: question.context\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-4 shadow\",\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        className: \"d-flex justify-content-between align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"mb-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-question-circle me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), \"C\\xE2u h\\u1ECFi \", question.questionNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Badge, {\n            bg: \"secondary\",\n            className: \"me-2\",\n            children: [\"ID: \", question.questionId]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), question.isGroupQuestion && /*#__PURE__*/_jsxDEV(Badge, {\n            bg: \"info\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-layer-group me-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 17\n            }, this), \"Nh\\xF3m c\\xE2u h\\u1ECFi\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"question-text mb-4\",\n          style: {\n            fontSize: '1.1rem',\n            lineHeight: '1.6'\n          },\n          children: question.questionText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"choices-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-3\",\n            children: \"Ch\\u1ECDn \\u0111\\xE1p \\xE1n:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: Object.entries(question.choices).map(([choice, text]) => /*#__PURE__*/_jsxDEV(Col, {\n              xs: 12,\n              className: \"mb-2\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: getChoiceVariant(choice),\n                className: \"w-100 text-start p-3\",\n                onClick: () => handleAnswerSelect(choice),\n                disabled: showAnswer,\n                style: {\n                  minHeight: '60px',\n                  border: selectedAnswer === choice && !showAnswer ? '2px solid #0d6efd' : undefined\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"me-3 mt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: getChoiceIcon(choice)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 124,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      className: \"me-2\",\n                      children: [choice, \")\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 127,\n                      columnNumber: 25\n                    }, this), text]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 126,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 19\n              }, this)\n            }, choice, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), !showAnswer && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"success\",\n            size: \"lg\",\n            onClick: handleSubmit,\n            disabled: !selectedAnswer,\n            className: \"px-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-check me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this), \"X\\xE1c nh\\u1EADn \\u0111\\xE1p \\xE1n\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: [\"Ho\\u1EB7c nh\\u1EA5n ph\\xEDm \", Object.keys(question.choices).map((choice, index) => `${index + 1} (${choice})`).join(', ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this), showAnswer && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            variant: isCorrect() ? 'success' : 'danger',\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas ${isCorrect() ? 'fa-check-circle' : 'fa-times-circle'} me-2`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: isCorrect() ? 'Chính xác!' : 'Không chính xác'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [\"\\u0110\\xE1p \\xE1n \\u0111\\xFAng: \", getAnswerData().correctAnswer]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this), userAnswer && userAnswer !== getAnswerData().correctAnswer && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-1\",\n                children: [\"B\\u1EA1n \\u0111\\xE3 ch\\u1ECDn: \", userAnswer]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this), getAnswerData().explanation && /*#__PURE__*/_jsxDEV(Card, {\n            className: \"mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n              className: \"bg-light\",\n              children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"mb-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-lightbulb me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 23\n                }, this), \"Gi\\u1EA3i th\\xEDch\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  whiteSpace: 'pre-line',\n                  lineHeight: '1.6'\n                },\n                children: getAnswerData().explanation\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n};\n_s(QuestionDisplay, \"bY9+dTFMyj3mt1tT/hv3/doJV24=\");\n_c = QuestionDisplay;\nexport default QuestionDisplay;\nvar _c;\n$RefreshReg$(_c, \"QuestionDisplay\");", "map": {"version": 3, "names": ["React", "useState", "Card", "<PERSON><PERSON>", "<PERSON><PERSON>", "Badge", "Row", "Col", "jsxDEV", "_jsxDEV", "QuestionDisplay", "question", "groupQuestions", "answers", "userAnswer", "showAnswer", "onSubmitAnswer", "_s", "_groupQuestions$", "_groupQuestions", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedAnswer", "handleAnswerSelect", "choice", "handleSubmit", "questionNumber", "getAnswerData", "isCorrect", "answerData", "<PERSON><PERSON><PERSON><PERSON>", "getChoiceVariant", "getChoiceIcon", "className", "children", "isGroupQuestion", "context", "Header", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "Body", "style", "whiteSpace", "lineHeight", "bg", "questionId", "fontSize", "questionText", "Object", "entries", "choices", "map", "text", "xs", "variant", "onClick", "disabled", "minHeight", "border", "undefined", "size", "keys", "index", "join", "explanation", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/QuestionDisplay.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { <PERSON>, <PERSON>ton, Alert, Badge, Row, Col } from 'react-bootstrap';\n\nconst QuestionDisplay = ({ \n  question, \n  groupQuestions, \n  answers, \n  userAnswer, \n  showAnswer, \n  onSubmitAnswer \n}) => {\n  const [selectedAnswer, setSelectedAnswer] = useState(userAnswer || '');\n\n  const handleAnswerSelect = (choice) => {\n    if (showAnswer) return; // Prevent changing answer after submission\n    setSelectedAnswer(choice);\n  };\n\n  const handleSubmit = () => {\n    if (!selectedAnswer) return;\n    onSubmitAnswer(question.questionNumber, selectedAnswer);\n  };\n\n  const getAnswerData = () => {\n    return answers[question.questionNumber] || {};\n  };\n\n  const isCorrect = () => {\n    const answerData = getAnswerData();\n    return userAnswer === answerData.correctAnswer;\n  };\n\n  const getChoiceVariant = (choice) => {\n    if (!showAnswer) {\n      return selectedAnswer === choice ? 'primary' : 'outline-secondary';\n    }\n    \n    const answerData = getAnswerData();\n    if (choice === answerData.correctAnswer) {\n      return 'success';\n    } else if (choice === userAnswer && choice !== answerData.correctAnswer) {\n      return 'danger';\n    }\n    return 'outline-secondary';\n  };\n\n  const getChoiceIcon = (choice) => {\n    if (!showAnswer) {\n      return selectedAnswer === choice ? 'fas fa-check-circle' : 'far fa-circle';\n    }\n    \n    const answerData = getAnswerData();\n    if (choice === answerData.correctAnswer) {\n      return 'fas fa-check-circle';\n    } else if (choice === userAnswer && choice !== answerData.correctAnswer) {\n      return 'fas fa-times-circle';\n    }\n    return 'far fa-circle';\n  };\n\n  return (\n    <div className=\"question-display\">\n      {/* Context Section (for group questions) */}\n      {question.isGroupQuestion && question.context && (\n        <Card className=\"mb-4 border-info\">\n          <Card.Header className=\"bg-info text-white\">\n            <h6 className=\"mb-0\">\n              <i className=\"fas fa-info-circle me-2\"></i>\n              Thông tin chung cho câu hỏi {groupQuestions[0]?.questionNumber} - {groupQuestions[groupQuestions.length - 1]?.questionNumber}\n            </h6>\n          </Card.Header>\n          <Card.Body>\n            <div className=\"context-content\" style={{ whiteSpace: 'pre-line', lineHeight: '1.6' }}>\n              {question.context}\n            </div>\n          </Card.Body>\n        </Card>\n      )}\n\n      {/* Question Section */}\n      <Card className=\"mb-4 shadow\">\n        <Card.Header className=\"d-flex justify-content-between align-items-center\">\n          <h5 className=\"mb-0\">\n            <i className=\"fas fa-question-circle me-2\"></i>\n            Câu hỏi {question.questionNumber}\n          </h5>\n          <div>\n            <Badge bg=\"secondary\" className=\"me-2\">\n              ID: {question.questionId}\n            </Badge>\n            {question.isGroupQuestion && (\n              <Badge bg=\"info\">\n                <i className=\"fas fa-layer-group me-1\"></i>\n                Nhóm câu hỏi\n              </Badge>\n            )}\n          </div>\n        </Card.Header>\n        \n        <Card.Body>\n          {/* Question Text */}\n          <div className=\"question-text mb-4\" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>\n            {question.questionText}\n          </div>\n\n          {/* Answer Choices */}\n          <div className=\"choices-section\">\n            <h6 className=\"mb-3\">Chọn đáp án:</h6>\n            <Row>\n              {Object.entries(question.choices).map(([choice, text]) => (\n                <Col key={choice} xs={12} className=\"mb-2\">\n                  <Button\n                    variant={getChoiceVariant(choice)}\n                    className=\"w-100 text-start p-3\"\n                    onClick={() => handleAnswerSelect(choice)}\n                    disabled={showAnswer}\n                    style={{ \n                      minHeight: '60px',\n                      border: selectedAnswer === choice && !showAnswer ? '2px solid #0d6efd' : undefined\n                    }}\n                  >\n                    <div className=\"d-flex align-items-start\">\n                      <div className=\"me-3 mt-1\">\n                        <i className={getChoiceIcon(choice)}></i>\n                      </div>\n                      <div>\n                        <strong className=\"me-2\">{choice})</strong>\n                        {text}\n                      </div>\n                    </div>\n                  </Button>\n                </Col>\n              ))}\n            </Row>\n          </div>\n\n          {/* Submit Button */}\n          {!showAnswer && (\n            <div className=\"text-center mt-4\">\n              <Button\n                variant=\"success\"\n                size=\"lg\"\n                onClick={handleSubmit}\n                disabled={!selectedAnswer}\n                className=\"px-5\"\n              >\n                <i className=\"fas fa-check me-2\"></i>\n                Xác nhận đáp án\n              </Button>\n              <div className=\"mt-2\">\n                <small className=\"text-muted\">\n                  Hoặc nhấn phím {Object.keys(question.choices).map((choice, index) => `${index + 1} (${choice})`).join(', ')}\n                </small>\n              </div>\n            </div>\n          )}\n\n          {/* Answer Explanation */}\n          {showAnswer && (\n            <div className=\"mt-4\">\n              <Alert variant={isCorrect() ? 'success' : 'danger'}>\n                <div className=\"d-flex align-items-center mb-2\">\n                  <i className={`fas ${isCorrect() ? 'fa-check-circle' : 'fa-times-circle'} me-2`}></i>\n                  <strong>\n                    {isCorrect() ? 'Chính xác!' : 'Không chính xác'}\n                  </strong>\n                </div>\n                <div>\n                  <strong>Đáp án đúng: {getAnswerData().correctAnswer}</strong>\n                  {userAnswer && userAnswer !== getAnswerData().correctAnswer && (\n                    <div className=\"mt-1\">\n                      Bạn đã chọn: {userAnswer}\n                    </div>\n                  )}\n                </div>\n              </Alert>\n\n              {getAnswerData().explanation && (\n                <Card className=\"mt-3\">\n                  <Card.Header className=\"bg-light\">\n                    <h6 className=\"mb-0\">\n                      <i className=\"fas fa-lightbulb me-2\"></i>\n                      Giải thích\n                    </h6>\n                  </Card.Header>\n                  <Card.Body>\n                    <div style={{ whiteSpace: 'pre-line', lineHeight: '1.6' }}>\n                      {getAnswerData().explanation}\n                    </div>\n                  </Card.Body>\n                </Card>\n              )}\n            </div>\n          )}\n        </Card.Body>\n      </Card>\n    </div>\n  );\n};\n\nexport default QuestionDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,eAAe,GAAGA,CAAC;EACvBC,QAAQ;EACRC,cAAc;EACdC,OAAO;EACPC,UAAU;EACVC,UAAU;EACVC;AACF,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,eAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAACa,UAAU,IAAI,EAAE,CAAC;EAEtE,MAAMQ,kBAAkB,GAAIC,MAAM,IAAK;IACrC,IAAIR,UAAU,EAAE,OAAO,CAAC;IACxBM,iBAAiB,CAACE,MAAM,CAAC;EAC3B,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACJ,cAAc,EAAE;IACrBJ,cAAc,CAACL,QAAQ,CAACc,cAAc,EAAEL,cAAc,CAAC;EACzD,CAAC;EAED,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAOb,OAAO,CAACF,QAAQ,CAACc,cAAc,CAAC,IAAI,CAAC,CAAC;EAC/C,CAAC;EAED,MAAME,SAAS,GAAGA,CAAA,KAAM;IACtB,MAAMC,UAAU,GAAGF,aAAa,CAAC,CAAC;IAClC,OAAOZ,UAAU,KAAKc,UAAU,CAACC,aAAa;EAChD,CAAC;EAED,MAAMC,gBAAgB,GAAIP,MAAM,IAAK;IACnC,IAAI,CAACR,UAAU,EAAE;MACf,OAAOK,cAAc,KAAKG,MAAM,GAAG,SAAS,GAAG,mBAAmB;IACpE;IAEA,MAAMK,UAAU,GAAGF,aAAa,CAAC,CAAC;IAClC,IAAIH,MAAM,KAAKK,UAAU,CAACC,aAAa,EAAE;MACvC,OAAO,SAAS;IAClB,CAAC,MAAM,IAAIN,MAAM,KAAKT,UAAU,IAAIS,MAAM,KAAKK,UAAU,CAACC,aAAa,EAAE;MACvE,OAAO,QAAQ;IACjB;IACA,OAAO,mBAAmB;EAC5B,CAAC;EAED,MAAME,aAAa,GAAIR,MAAM,IAAK;IAChC,IAAI,CAACR,UAAU,EAAE;MACf,OAAOK,cAAc,KAAKG,MAAM,GAAG,qBAAqB,GAAG,eAAe;IAC5E;IAEA,MAAMK,UAAU,GAAGF,aAAa,CAAC,CAAC;IAClC,IAAIH,MAAM,KAAKK,UAAU,CAACC,aAAa,EAAE;MACvC,OAAO,qBAAqB;IAC9B,CAAC,MAAM,IAAIN,MAAM,KAAKT,UAAU,IAAIS,MAAM,KAAKK,UAAU,CAACC,aAAa,EAAE;MACvE,OAAO,qBAAqB;IAC9B;IACA,OAAO,eAAe;EACxB,CAAC;EAED,oBACEpB,OAAA;IAAKuB,SAAS,EAAC,kBAAkB;IAAAC,QAAA,GAE9BtB,QAAQ,CAACuB,eAAe,IAAIvB,QAAQ,CAACwB,OAAO,iBAC3C1B,OAAA,CAACP,IAAI;MAAC8B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAChCxB,OAAA,CAACP,IAAI,CAACkC,MAAM;QAACJ,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACzCxB,OAAA;UAAIuB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAClBxB,OAAA;YAAGuB,SAAS,EAAC;UAAyB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,2CACf,GAAAtB,gBAAA,GAACN,cAAc,CAAC,CAAC,CAAC,cAAAM,gBAAA,uBAAjBA,gBAAA,CAAmBO,cAAc,EAAC,KAAG,GAAAN,eAAA,GAACP,cAAc,CAACA,cAAc,CAAC6B,MAAM,GAAG,CAAC,CAAC,cAAAtB,eAAA,uBAAzCA,eAAA,CAA2CM,cAAc;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1H;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eACd/B,OAAA,CAACP,IAAI,CAACwC,IAAI;QAAAT,QAAA,eACRxB,OAAA;UAAKuB,SAAS,EAAC,iBAAiB;UAACW,KAAK,EAAE;YAAEC,UAAU,EAAE,UAAU;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EACnFtB,QAAQ,CAACwB;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACP,eAGD/B,OAAA,CAACP,IAAI;MAAC8B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC3BxB,OAAA,CAACP,IAAI,CAACkC,MAAM;QAACJ,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBACxExB,OAAA;UAAIuB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAClBxB,OAAA;YAAGuB,SAAS,EAAC;UAA6B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,oBACvC,EAAC7B,QAAQ,CAACc,cAAc;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACL/B,OAAA;UAAAwB,QAAA,gBACExB,OAAA,CAACJ,KAAK;YAACyC,EAAE,EAAC,WAAW;YAACd,SAAS,EAAC,MAAM;YAAAC,QAAA,GAAC,MACjC,EAACtB,QAAQ,CAACoC,UAAU;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,EACP7B,QAAQ,CAACuB,eAAe,iBACvBzB,OAAA,CAACJ,KAAK;YAACyC,EAAE,EAAC,MAAM;YAAAb,QAAA,gBACdxB,OAAA;cAAGuB,SAAS,EAAC;YAAyB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,2BAE7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEd/B,OAAA,CAACP,IAAI,CAACwC,IAAI;QAAAT,QAAA,gBAERxB,OAAA;UAAKuB,SAAS,EAAC,oBAAoB;UAACW,KAAK,EAAE;YAAEK,QAAQ,EAAE,QAAQ;YAAEH,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAClFtB,QAAQ,CAACsC;QAAY;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAGN/B,OAAA;UAAKuB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BxB,OAAA;YAAIuB,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtC/B,OAAA,CAACH,GAAG;YAAA2B,QAAA,EACDiB,MAAM,CAACC,OAAO,CAACxC,QAAQ,CAACyC,OAAO,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC9B,MAAM,EAAE+B,IAAI,CAAC,kBACnD7C,OAAA,CAACF,GAAG;cAAcgD,EAAE,EAAE,EAAG;cAACvB,SAAS,EAAC,MAAM;cAAAC,QAAA,eACxCxB,OAAA,CAACN,MAAM;gBACLqD,OAAO,EAAE1B,gBAAgB,CAACP,MAAM,CAAE;gBAClCS,SAAS,EAAC,sBAAsB;gBAChCyB,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAACC,MAAM,CAAE;gBAC1CmC,QAAQ,EAAE3C,UAAW;gBACrB4B,KAAK,EAAE;kBACLgB,SAAS,EAAE,MAAM;kBACjBC,MAAM,EAAExC,cAAc,KAAKG,MAAM,IAAI,CAACR,UAAU,GAAG,mBAAmB,GAAG8C;gBAC3E,CAAE;gBAAA5B,QAAA,eAEFxB,OAAA;kBAAKuB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCxB,OAAA;oBAAKuB,SAAS,EAAC,WAAW;oBAAAC,QAAA,eACxBxB,OAAA;sBAAGuB,SAAS,EAAED,aAAa,CAACR,MAAM;oBAAE;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACN/B,OAAA;oBAAAwB,QAAA,gBACExB,OAAA;sBAAQuB,SAAS,EAAC,MAAM;sBAAAC,QAAA,GAAEV,MAAM,EAAC,GAAC;oBAAA;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC1Cc,IAAI;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC,GApBDjB,MAAM;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBX,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAACzB,UAAU,iBACVN,OAAA;UAAKuB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BxB,OAAA,CAACN,MAAM;YACLqD,OAAO,EAAC,SAAS;YACjBM,IAAI,EAAC,IAAI;YACTL,OAAO,EAAEjC,YAAa;YACtBkC,QAAQ,EAAE,CAACtC,cAAe;YAC1BY,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAEhBxB,OAAA;cAAGuB,SAAS,EAAC;YAAmB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,sCAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/B,OAAA;YAAKuB,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBxB,OAAA;cAAOuB,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,8BACb,EAACiB,MAAM,CAACa,IAAI,CAACpD,QAAQ,CAACyC,OAAO,CAAC,CAACC,GAAG,CAAC,CAAC9B,MAAM,EAAEyC,KAAK,KAAK,GAAGA,KAAK,GAAG,CAAC,KAAKzC,MAAM,GAAG,CAAC,CAAC0C,IAAI,CAAC,IAAI,CAAC;YAAA;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGAzB,UAAU,iBACTN,OAAA;UAAKuB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBxB,OAAA,CAACL,KAAK;YAACoD,OAAO,EAAE7B,SAAS,CAAC,CAAC,GAAG,SAAS,GAAG,QAAS;YAAAM,QAAA,gBACjDxB,OAAA;cAAKuB,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7CxB,OAAA;gBAAGuB,SAAS,EAAE,OAAOL,SAAS,CAAC,CAAC,GAAG,iBAAiB,GAAG,iBAAiB;cAAQ;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrF/B,OAAA;gBAAAwB,QAAA,EACGN,SAAS,CAAC,CAAC,GAAG,YAAY,GAAG;cAAiB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN/B,OAAA;cAAAwB,QAAA,gBACExB,OAAA;gBAAAwB,QAAA,GAAQ,kCAAa,EAACP,aAAa,CAAC,CAAC,CAACG,aAAa;cAAA;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,EAC5D1B,UAAU,IAAIA,UAAU,KAAKY,aAAa,CAAC,CAAC,CAACG,aAAa,iBACzDpB,OAAA;gBAAKuB,SAAS,EAAC,MAAM;gBAAAC,QAAA,GAAC,iCACP,EAACnB,UAAU;cAAA;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EAEPd,aAAa,CAAC,CAAC,CAACwC,WAAW,iBAC1BzD,OAAA,CAACP,IAAI;YAAC8B,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACpBxB,OAAA,CAACP,IAAI,CAACkC,MAAM;cAACJ,SAAS,EAAC,UAAU;cAAAC,QAAA,eAC/BxB,OAAA;gBAAIuB,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAClBxB,OAAA;kBAAGuB,SAAS,EAAC;gBAAuB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,sBAE3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACd/B,OAAA,CAACP,IAAI,CAACwC,IAAI;cAAAT,QAAA,eACRxB,OAAA;gBAAKkC,KAAK,EAAE;kBAAEC,UAAU,EAAE,UAAU;kBAAEC,UAAU,EAAE;gBAAM,CAAE;gBAAAZ,QAAA,EACvDP,aAAa,CAAC,CAAC,CAACwC;cAAW;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACvB,EAAA,CAnMIP,eAAe;AAAAyD,EAAA,GAAfzD,eAAe;AAqMrB,eAAeA,eAAe;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}