{"ast": null, "code": "// PDF Parser for CFA Questions and Answers\nimport * as pdfjsLib from 'pdfjs-dist';\n\n// Set worker path - use the worker from the same version\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;\nclass CFAQuestionParser {\n  constructor() {\n    this.questions = [];\n    this.answers = {};\n  }\n  async parseQuestionFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n        fullText += pageText + '\\n\\n';\n      }\n\n      // Debug: Log extracted text\n      console.log('Extracted text length:', fullText.length);\n      console.log('First 2000 characters:', fullText.substring(0, 2000));\n\n      // Parse questions from text\n      const questions = this.extractQuestions(fullText);\n      console.log('Extracted questions:', questions.length);\n      return questions;\n    } catch (error) {\n      console.error('Error parsing question file:', error);\n      throw error;\n    }\n  }\n  async parseAnswerFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n        fullText += pageText + '\\n\\n';\n      }\n      console.log('Answer file text length:', fullText.length);\n      console.log('Answer file preview:', fullText.substring(0, 1000));\n\n      // Parse answers from text\n      const answers = this.extractAnswers(fullText);\n      console.log('Extracted answers:', Object.keys(answers).length);\n      return answers;\n    } catch (error) {\n      console.error('Error parsing answer file:', error);\n      throw error;\n    }\n  }\n  extractQuestions(text) {\n    const questions = [];\n    console.log('=== STARTING QUESTION EXTRACTION ===');\n    console.log('Text length:', text.length);\n\n    // First, let's find all question markers and their positions\n    const questionMarkers = [];\n    const questionPattern = /Question #(\\d+)(?:\\s*-\\s*(\\d+))?\\s+of\\s+(\\d+)(?:\\s+Question\\s+ID:\\s*(\\d+))?/g;\n    let match;\n    while ((match = questionPattern.exec(text)) !== null) {\n      const startQ = parseInt(match[1]);\n      const endQ = match[2] ? parseInt(match[2]) : null;\n      const totalQ = parseInt(match[3]);\n      const questionId = match[4] || `q${startQ}`;\n      questionMarkers.push({\n        startQ,\n        endQ,\n        totalQ,\n        questionId,\n        isGroup: endQ !== null,\n        index: match.index,\n        fullMatch: match[0]\n      });\n    }\n    console.log(`Found ${questionMarkers.length} question markers:`, questionMarkers);\n\n    // Process each marker\n    for (let i = 0; i < questionMarkers.length; i++) {\n      const marker = questionMarkers[i];\n      const nextMarker = questionMarkers[i + 1];\n\n      // Extract content from this marker to the next (or end of text)\n      const startIndex = marker.index + marker.fullMatch.length;\n      const endIndex = nextMarker ? nextMarker.index : text.length;\n      const content = text.substring(startIndex, endIndex).trim();\n      console.log(`\\n--- Processing marker ${i + 1}: Q${marker.startQ}${marker.endQ ? `-${marker.endQ}` : ''} ---`);\n      console.log(`Content length: ${content.length}`);\n      console.log(`Content preview: ${content.substring(0, 200)}...`);\n      if (marker.isGroup) {\n        // This is a group of questions with shared context\n        console.log(`Processing question group ${marker.startQ}-${marker.endQ}`);\n\n        // Extract shared context and individual questions\n        const groupQuestions = this.extractGroupQuestions(content, marker.startQ, marker.endQ, marker.totalQ, marker.questionId);\n        questions.push(...groupQuestions);\n      } else {\n        // Single question\n        console.log(`Processing single question ${marker.startQ}`);\n        const questionData = this.parseSingleQuestion(content, marker.startQ, marker.totalQ, marker.questionId);\n        if (questionData) {\n          questions.push(questionData);\n        }\n      }\n    }\n\n    // Sort questions by number\n    questions.sort((a, b) => a.questionNumber - b.questionNumber);\n    console.log(`\\n=== EXTRACTION COMPLETE ===`);\n    console.log(`Total questions extracted: ${questions.length}`);\n    console.log('Question numbers:', questions.map(q => q.questionNumber));\n    return questions;\n  }\n  extractQuestionsAlternative(text) {\n    console.log('Using alternative extraction method...');\n    const questions = [];\n\n    // Look for any numbered patterns that might be questions\n    const patterns = [/(\\d+)\\.\\s+([^0-9]+?)(?=\\d+\\.|$)/gs,\n    // 1. Question text\n    /Question\\s+(\\d+)[:\\.]?\\s*([^Q]+?)(?=Question\\s+\\d+|$)/gis // Question 1: text\n    ];\n    for (const pattern of patterns) {\n      const matches = [...text.matchAll(pattern)];\n      console.log(`Alternative pattern found ${matches.length} matches`);\n      for (const match of matches) {\n        const questionNum = parseInt(match[1]);\n        const content = match[2].trim();\n        if (content.length > 20) {\n          // Filter out very short matches\n          const questionData = this.parseSingleQuestion(content, questionNum, 100,\n          // Default total\n          'alt-' + questionNum);\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n      if (questions.length > 0) break; // Use first successful pattern\n    }\n    return questions;\n  }\n  extractGroupQuestions(content, startQ, endQ, totalQ, questionId) {\n    const questions = [];\n    console.log(`Extracting group questions ${startQ}-${endQ || startQ}`);\n    console.log('Group content preview:', content.substring(0, 200));\n\n    // First, try to extract context (text before any question-specific content)\n    let context = '';\n    const contextMatch = content.match(/^(.*?)(?=[A-E]\\)|Question|$)/s);\n    if (contextMatch && contextMatch[1].trim().length > 50) {\n      context = contextMatch[1].trim();\n      console.log('Extracted context:', context.substring(0, 100));\n    }\n\n    // If this is a range (e.g., Question #6-11), try to find individual questions\n    if (endQ && endQ > startQ) {\n      console.log(`Looking for individual questions ${startQ} to ${endQ}`);\n\n      // Try to split content by question numbers\n      for (let qNum = startQ; qNum <= endQ; qNum++) {\n        // Look for patterns like \"6.\", \"Question 6\", etc.\n        const patterns = [new RegExp(`${qNum}\\\\.(.*?)(?=${qNum + 1}\\\\.|$)`, 's'), new RegExp(`Question\\\\s+${qNum}[:\\\\.]?(.*?)(?=Question\\\\s+${qNum + 1}|$)`, 'is')];\n        let questionContent = '';\n        for (const pattern of patterns) {\n          const match = content.match(pattern);\n          if (match) {\n            questionContent = match[1].trim();\n            break;\n          }\n        }\n        if (questionContent) {\n          console.log(`Found content for question ${qNum}`);\n          const questionData = this.parseSingleQuestion(questionContent, qNum, totalQ, `${questionId}-${qNum}`, context);\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n    }\n\n    // If no individual questions found, treat the whole content as one question\n    if (questions.length === 0) {\n      console.log('No individual questions found, treating as single question with context');\n      const questionData = this.parseSingleQuestion(content, startQ, totalQ, questionId, context);\n      if (questionData) {\n        questions.push(questionData);\n      }\n    }\n    console.log(`Extracted ${questions.length} questions from group`);\n    return questions;\n  }\n  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {\n    if (!content.trim()) return null;\n    console.log(`\\n=== Parsing Question ${questionNum} ===`);\n    console.log(`Content length: ${content.length}`);\n    console.log(`Content preview: ${content.substring(0, 300)}...`);\n    let questionText = '';\n    const choices = {};\n\n    // Method 1: Look for choice patterns A) B) C) D) E)\n    const choicePattern = /([A-E])\\)\\s*([^A-E]*?)(?=\\s*[A-E]\\)|$)/g;\n    const choiceMatches = [...content.matchAll(choicePattern)];\n    console.log(`Found ${choiceMatches.length} choice matches`);\n    if (choiceMatches.length >= 3) {\n      // Extract question text (everything before first choice)\n      const firstChoiceIndex = choiceMatches[0].index;\n      questionText = content.substring(0, firstChoiceIndex).trim();\n\n      // Extract choices\n      choiceMatches.forEach(match => {\n        const choice = match[1];\n        const text = match[2].trim();\n        if (text.length > 0) {\n          choices[choice] = text;\n        }\n      });\n      console.log(`Method 1 success: ${Object.keys(choices).length} choices extracted`);\n    } else {\n      // Method 2: Line-by-line parsing with better logic\n      console.log('Using Method 2: Line-by-line parsing');\n      const lines = content.split(/\\n/).map(line => line.trim()).filter(line => line.length > 0);\n      let currentChoice = null;\n      let choiceText = '';\n      let questionLines = [];\n      let foundFirstChoice = false;\n      for (const line of lines) {\n        // Check for choice pattern at start of line\n        const choiceMatch = line.match(/^([A-E])\\)\\s*(.*)/);\n        if (choiceMatch) {\n          // Save previous choice if exists\n          if (currentChoice && choiceText.trim()) {\n            choices[currentChoice] = choiceText.trim();\n          }\n          currentChoice = choiceMatch[1];\n          choiceText = choiceMatch[2];\n          foundFirstChoice = true;\n        } else if (currentChoice && foundFirstChoice) {\n          // Continue current choice text\n          choiceText += ' ' + line;\n        } else if (!foundFirstChoice) {\n          // Still in question text area\n          questionLines.push(line);\n        }\n      }\n\n      // Save last choice\n      if (currentChoice && choiceText.trim()) {\n        choices[currentChoice] = choiceText.trim();\n      }\n      questionText = questionLines.join(' ').trim();\n      console.log(`Method 2 result: ${Object.keys(choices).length} choices, question length: ${questionText.length}`);\n    }\n\n    // Method 3: If still no good results, try more aggressive parsing\n    if (Object.keys(choices).length < 3 && content.length > 100) {\n      console.log('Using Method 3: Aggressive parsing');\n\n      // Look for any A) B) C) patterns anywhere in text\n      const aggressivePattern = /([A-E])\\)\\s*([^A-E\\n]{10,200}?)(?=\\s*[A-E]\\)|$)/g;\n      const aggressiveMatches = [...content.matchAll(aggressivePattern)];\n      if (aggressiveMatches.length >= 3) {\n        // Clear previous results\n        Object.keys(choices).forEach(key => delete choices[key]);\n\n        // Extract question text (everything before first choice)\n        const firstChoiceIndex = aggressiveMatches[0].index;\n        questionText = content.substring(0, firstChoiceIndex).trim();\n\n        // Extract choices\n        aggressiveMatches.forEach(match => {\n          const choice = match[1];\n          const text = match[2].trim();\n          choices[choice] = text;\n        });\n        console.log(`Method 3 success: ${Object.keys(choices).length} choices extracted`);\n      }\n    }\n\n    // Clean up and validate\n    questionText = questionText.replace(/\\s+/g, ' ').trim();\n    Object.keys(choices).forEach(key => {\n      choices[key] = choices[key].replace(/\\s+/g, ' ').trim();\n      // Remove empty choices\n      if (!choices[key]) {\n        delete choices[key];\n      }\n    });\n    console.log(`Final result for Question ${questionNum}:`);\n    console.log(`- Question text: ${questionText.length} chars`);\n    console.log(`- Choices: ${Object.keys(choices).join(', ')}`);\n    console.log(`- Context: ${context ? 'Yes' : 'No'}`);\n\n    // Validation and fallbacks\n    if (!questionText && Object.keys(choices).length === 0) {\n      console.log(`Question ${questionNum}: Complete parsing failure`);\n      return null;\n    }\n\n    // Ensure minimum choices\n    const expectedChoices = ['A', 'B', 'C', 'D', 'E'];\n    const missingChoices = expectedChoices.filter(c => !choices[c]);\n    if (missingChoices.length > 2) {\n      console.log(`Question ${questionNum}: Missing too many choices, adding placeholders`);\n      missingChoices.forEach(choice => {\n        choices[choice] = `Choice ${choice} (not found in PDF)`;\n      });\n    }\n\n    // Ensure question text\n    if (!questionText.trim()) {\n      questionText = `Question ${questionNum} content (parsing incomplete - check PDF)`;\n    }\n    return {\n      questionNumber: questionNum,\n      totalQuestions: totalQ,\n      questionId: questionId,\n      context: context,\n      questionText: questionText,\n      choices: choices,\n      isGroupQuestion: Boolean(context)\n    };\n  }\n  extractAnswers(text) {\n    const answers = {};\n    console.log('Extracting answers from text...');\n\n    // Find all question markers in answer file\n    const questionMarkers = [];\n    const patterns = [/Question #(\\d+) of (\\d+) Question ID: (\\d+)/g, /Question #(\\d+)/g];\n    for (const pattern of patterns) {\n      let match;\n      while ((match = pattern.exec(text)) !== null) {\n        questionMarkers.push({\n          index: match.index,\n          fullMatch: match[0],\n          questionNum: parseInt(match[1])\n        });\n      }\n    }\n    console.log(`Found ${questionMarkers.length} question markers in answer file`);\n\n    // Sort by position\n    questionMarkers.sort((a, b) => a.index - b.index);\n\n    // Extract answer content for each question\n    for (let i = 0; i < questionMarkers.length; i++) {\n      const marker = questionMarkers[i];\n      const nextMarker = questionMarkers[i + 1];\n\n      // Get content from current marker to next marker (or end of text)\n      const startIndex = marker.index + marker.fullMatch.length;\n      const endIndex = nextMarker ? nextMarker.index : text.length;\n      const content = text.substring(startIndex, endIndex).trim();\n      console.log(`Processing answer for question ${marker.questionNum}, content length: ${content.length}`);\n\n      // Find correct answer\n      let correctAnswer = null;\n      const answerPatterns = [/The correct answer is ([A-E])\\./i, /The correct answer is ([A-E])\\s/i, /Correct answer:\\s*([A-E])/i, /Answer:\\s*([A-E])/i, /([A-E])\\s*is correct/i, /Choice\\s*([A-E])\\s*is correct/i];\n      for (const pattern of answerPatterns) {\n        const match = content.match(pattern);\n        if (match) {\n          correctAnswer = match[1].toUpperCase();\n          console.log(`Found correct answer ${correctAnswer} for question ${marker.questionNum}`);\n          break;\n        }\n      }\n\n      // Find explanation - be more flexible and comprehensive\n      let explanation = '';\n      console.log(`Looking for explanation in question ${marker.questionNum} content...`);\n      console.log(`Content preview: ${content.substring(0, 500)}...`);\n\n      // Multiple patterns to find explanation\n      const explanationPatterns = [/Explanation[:\\s]*\\n?(.*?)(?=Question #|$)/is, /Explanation[:\\s]+(.*?)(?=\\(Module|$)/is, /Explanation[:\\s]+(.*?)(?=\\n\\s*\\n|$)/is, /Explanation[:\\s]+(.*)/is];\n      for (let i = 0; i < explanationPatterns.length; i++) {\n        const pattern = explanationPatterns[i];\n        const match = content.match(pattern);\n        if (match && match[1].trim().length > 10) {\n          explanation = match[1].trim();\n          console.log(`Found explanation using pattern ${i + 1} for question ${marker.questionNum}`);\n          break;\n        }\n      }\n\n      // If no explanation found with patterns, try manual extraction\n      if (!explanation) {\n        const explKeywords = ['explanation', 'Explanation', 'EXPLANATION'];\n        for (const keyword of explKeywords) {\n          const explIndex = content.indexOf(keyword);\n          if (explIndex !== -1) {\n            // Extract everything after the keyword\n            let afterExpl = content.substring(explIndex + keyword.length).trim();\n\n            // Remove leading colons, spaces, newlines\n            afterExpl = afterExpl.replace(/^[:\\s\\n]+/, '');\n\n            // Take everything until next question or end\n            const nextQuestionIndex = afterExpl.search(/Question #\\d+/i);\n            if (nextQuestionIndex !== -1) {\n              afterExpl = afterExpl.substring(0, nextQuestionIndex);\n            }\n            if (afterExpl.length > 20) {\n              explanation = afterExpl.trim();\n              console.log(`Manual explanation extraction for question ${marker.questionNum}`);\n              break;\n            }\n          }\n        }\n      }\n\n      // Clean up explanation\n      if (explanation) {\n        // Normalize whitespace\n        explanation = explanation.replace(/\\s+/g, ' ');\n\n        // Remove common suffixes and prefixes\n        explanation = explanation.replace(/^\\s*[:\\-\\s]+/, ''); // Remove leading colons/dashes\n        explanation = explanation.replace(/\\(Module.*?\\)$/i, '');\n        explanation = explanation.replace(/\\(LOS.*?\\)$/i, '');\n        explanation = explanation.replace(/\\(Reading.*?\\)$/i, '');\n\n        // Remove trailing page numbers or references\n        explanation = explanation.replace(/\\s+\\d+\\s*$/, '');\n        explanation = explanation.trim();\n        console.log(`Cleaned explanation for question ${marker.questionNum}, final length: ${explanation.length}`);\n        console.log(`Explanation preview: ${explanation.substring(0, 100)}...`);\n      } else {\n        console.log(`No explanation found for question ${marker.questionNum}`);\n      }\n      if (correctAnswer) {\n        answers[marker.questionNum] = {\n          correctAnswer: correctAnswer,\n          explanation: explanation || 'No explanation available'\n        };\n      } else {\n        console.log(`No correct answer found for question ${marker.questionNum}`);\n      }\n    }\n    console.log(`Extracted answers for ${Object.keys(answers).length} questions`);\n    return answers;\n  }\n  async parseFiles(questionFile, answerFile) {\n    try {\n      const [questions, answers] = await Promise.all([this.parseQuestionFile(questionFile), this.parseAnswerFile(answerFile)]);\n      return {\n        questions,\n        answers\n      };\n    } catch (error) {\n      console.error('Error parsing files:', error);\n      throw error;\n    }\n  }\n}\nexport default CFAQuestionParser;", "map": {"version": 3, "names": ["pdfjsLib", "GlobalWorkerOptions", "workerSrc", "CFAQuestionParser", "constructor", "questions", "answers", "parseQuestionFile", "file", "arrayBuffer", "pdf", "getDocument", "promise", "fullText", "i", "numPages", "page", "getPage", "textContent", "getTextContent", "pageText", "lastY", "item", "items", "Math", "abs", "transform", "str", "console", "log", "length", "substring", "extractQuestions", "error", "parseAnswerFile", "extractAnswers", "Object", "keys", "text", "questionMarkers", "questionPattern", "match", "exec", "startQ", "parseInt", "endQ", "totalQ", "questionId", "push", "isGroup", "index", "fullMatch", "marker", "nextM<PERSON><PERSON>", "startIndex", "endIndex", "content", "trim", "groupQuestions", "extractGroupQuestions", "questionData", "parseSingleQuestion", "sort", "a", "b", "questionNumber", "map", "q", "extractQuestionsAlternative", "patterns", "pattern", "matches", "matchAll", "questionNum", "context", "contextMatch", "qNum", "RegExp", "questionContent", "questionText", "choices", "choicePattern", "choiceMatches", "firstChoiceIndex", "for<PERSON>ach", "choice", "lines", "split", "line", "filter", "currentChoice", "choiceText", "questionLines", "foundFirstChoice", "choiceMatch", "join", "aggressivePattern", "aggressiveMatches", "key", "replace", "expectedChoices", "missingChoices", "c", "totalQuestions", "isGroupQuestion", "Boolean", "<PERSON><PERSON><PERSON><PERSON>", "answerPatterns", "toUpperCase", "explanation", "explanationPatterns", "explKeywords", "keyword", "explIndex", "indexOf", "afterExpl", "nextQuestionIndex", "search", "parseFiles", "questionFile", "answerFile", "Promise", "all"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/utils/pdfParser.js"], "sourcesContent": ["// PDF Parser for CFA Questions and Answers\nimport * as pdfjsLib from 'pdfjs-dist';\n\n// Set worker path - use the worker from the same version\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;\n\nclass CFAQuestionParser {\n  constructor() {\n    this.questions = [];\n    this.answers = {};\n  }\n\n  async parseQuestionFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n\n        fullText += pageText + '\\n\\n';\n      }\n\n      // Debug: Log extracted text\n      console.log('Extracted text length:', fullText.length);\n      console.log('First 2000 characters:', fullText.substring(0, 2000));\n\n      // Parse questions from text\n      const questions = this.extractQuestions(fullText);\n      console.log('Extracted questions:', questions.length);\n\n      return questions;\n    } catch (error) {\n      console.error('Error parsing question file:', error);\n      throw error;\n    }\n  }\n\n  async parseAnswerFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n\n        fullText += pageText + '\\n\\n';\n      }\n\n      console.log('Answer file text length:', fullText.length);\n      console.log('Answer file preview:', fullText.substring(0, 1000));\n\n      // Parse answers from text\n      const answers = this.extractAnswers(fullText);\n      console.log('Extracted answers:', Object.keys(answers).length);\n\n      return answers;\n    } catch (error) {\n      console.error('Error parsing answer file:', error);\n      throw error;\n    }\n  }\n\n  extractQuestions(text) {\n    const questions = [];\n\n    console.log('=== STARTING QUESTION EXTRACTION ===');\n    console.log('Text length:', text.length);\n\n    // First, let's find all question markers and their positions\n    const questionMarkers = [];\n    const questionPattern = /Question #(\\d+)(?:\\s*-\\s*(\\d+))?\\s+of\\s+(\\d+)(?:\\s+Question\\s+ID:\\s*(\\d+))?/g;\n\n    let match;\n    while ((match = questionPattern.exec(text)) !== null) {\n      const startQ = parseInt(match[1]);\n      const endQ = match[2] ? parseInt(match[2]) : null;\n      const totalQ = parseInt(match[3]);\n      const questionId = match[4] || `q${startQ}`;\n\n      questionMarkers.push({\n        startQ,\n        endQ,\n        totalQ,\n        questionId,\n        isGroup: endQ !== null,\n        index: match.index,\n        fullMatch: match[0]\n      });\n    }\n\n    console.log(`Found ${questionMarkers.length} question markers:`, questionMarkers);\n\n    // Process each marker\n    for (let i = 0; i < questionMarkers.length; i++) {\n      const marker = questionMarkers[i];\n      const nextMarker = questionMarkers[i + 1];\n\n      // Extract content from this marker to the next (or end of text)\n      const startIndex = marker.index + marker.fullMatch.length;\n      const endIndex = nextMarker ? nextMarker.index : text.length;\n      const content = text.substring(startIndex, endIndex).trim();\n\n      console.log(`\\n--- Processing marker ${i + 1}: Q${marker.startQ}${marker.endQ ? `-${marker.endQ}` : ''} ---`);\n      console.log(`Content length: ${content.length}`);\n      console.log(`Content preview: ${content.substring(0, 200)}...`);\n\n      if (marker.isGroup) {\n        // This is a group of questions with shared context\n        console.log(`Processing question group ${marker.startQ}-${marker.endQ}`);\n\n        // Extract shared context and individual questions\n        const groupQuestions = this.extractGroupQuestions(\n          content,\n          marker.startQ,\n          marker.endQ,\n          marker.totalQ,\n          marker.questionId\n        );\n\n        questions.push(...groupQuestions);\n      } else {\n        // Single question\n        console.log(`Processing single question ${marker.startQ}`);\n\n        const questionData = this.parseSingleQuestion(\n          content,\n          marker.startQ,\n          marker.totalQ,\n          marker.questionId\n        );\n\n        if (questionData) {\n          questions.push(questionData);\n        }\n      }\n    }\n\n    // Sort questions by number\n    questions.sort((a, b) => a.questionNumber - b.questionNumber);\n\n    console.log(`\\n=== EXTRACTION COMPLETE ===`);\n    console.log(`Total questions extracted: ${questions.length}`);\n    console.log('Question numbers:', questions.map(q => q.questionNumber));\n\n    return questions;\n  }\n\n  extractQuestionsAlternative(text) {\n    console.log('Using alternative extraction method...');\n    const questions = [];\n\n    // Look for any numbered patterns that might be questions\n    const patterns = [\n      /(\\d+)\\.\\s+([^0-9]+?)(?=\\d+\\.|$)/gs,  // 1. Question text\n      /Question\\s+(\\d+)[:\\.]?\\s*([^Q]+?)(?=Question\\s+\\d+|$)/gis,  // Question 1: text\n    ];\n\n    for (const pattern of patterns) {\n      const matches = [...text.matchAll(pattern)];\n      console.log(`Alternative pattern found ${matches.length} matches`);\n\n      for (const match of matches) {\n        const questionNum = parseInt(match[1]);\n        const content = match[2].trim();\n\n        if (content.length > 20) { // Filter out very short matches\n          const questionData = this.parseSingleQuestion(\n            content,\n            questionNum,\n            100, // Default total\n            'alt-' + questionNum\n          );\n\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n\n      if (questions.length > 0) break; // Use first successful pattern\n    }\n\n    return questions;\n  }\n\n  extractGroupQuestions(content, startQ, endQ, totalQ, questionId) {\n    const questions = [];\n\n    console.log(`Extracting group questions ${startQ}-${endQ || startQ}`);\n    console.log('Group content preview:', content.substring(0, 200));\n\n    // First, try to extract context (text before any question-specific content)\n    let context = '';\n    const contextMatch = content.match(/^(.*?)(?=[A-E]\\)|Question|$)/s);\n    if (contextMatch && contextMatch[1].trim().length > 50) {\n      context = contextMatch[1].trim();\n      console.log('Extracted context:', context.substring(0, 100));\n    }\n\n    // If this is a range (e.g., Question #6-11), try to find individual questions\n    if (endQ && endQ > startQ) {\n      console.log(`Looking for individual questions ${startQ} to ${endQ}`);\n\n      // Try to split content by question numbers\n      for (let qNum = startQ; qNum <= endQ; qNum++) {\n        // Look for patterns like \"6.\", \"Question 6\", etc.\n        const patterns = [\n          new RegExp(`${qNum}\\\\.(.*?)(?=${qNum + 1}\\\\.|$)`, 's'),\n          new RegExp(`Question\\\\s+${qNum}[:\\\\.]?(.*?)(?=Question\\\\s+${qNum + 1}|$)`, 'is')\n        ];\n\n        let questionContent = '';\n        for (const pattern of patterns) {\n          const match = content.match(pattern);\n          if (match) {\n            questionContent = match[1].trim();\n            break;\n          }\n        }\n\n        if (questionContent) {\n          console.log(`Found content for question ${qNum}`);\n          const questionData = this.parseSingleQuestion(\n            questionContent,\n            qNum,\n            totalQ,\n            `${questionId}-${qNum}`,\n            context\n          );\n\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n    }\n\n    // If no individual questions found, treat the whole content as one question\n    if (questions.length === 0) {\n      console.log('No individual questions found, treating as single question with context');\n      const questionData = this.parseSingleQuestion(\n        content,\n        startQ,\n        totalQ,\n        questionId,\n        context\n      );\n\n      if (questionData) {\n        questions.push(questionData);\n      }\n    }\n\n    console.log(`Extracted ${questions.length} questions from group`);\n    return questions;\n  }\n\n  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {\n    if (!content.trim()) return null;\n\n    console.log(`\\n=== Parsing Question ${questionNum} ===`);\n    console.log(`Content length: ${content.length}`);\n    console.log(`Content preview: ${content.substring(0, 300)}...`);\n\n    let questionText = '';\n    const choices = {};\n\n    // Method 1: Look for choice patterns A) B) C) D) E)\n    const choicePattern = /([A-E])\\)\\s*([^A-E]*?)(?=\\s*[A-E]\\)|$)/g;\n    const choiceMatches = [...content.matchAll(choicePattern)];\n\n    console.log(`Found ${choiceMatches.length} choice matches`);\n\n    if (choiceMatches.length >= 3) {\n      // Extract question text (everything before first choice)\n      const firstChoiceIndex = choiceMatches[0].index;\n      questionText = content.substring(0, firstChoiceIndex).trim();\n\n      // Extract choices\n      choiceMatches.forEach(match => {\n        const choice = match[1];\n        const text = match[2].trim();\n        if (text.length > 0) {\n          choices[choice] = text;\n        }\n      });\n\n      console.log(`Method 1 success: ${Object.keys(choices).length} choices extracted`);\n    } else {\n      // Method 2: Line-by-line parsing with better logic\n      console.log('Using Method 2: Line-by-line parsing');\n\n      const lines = content.split(/\\n/).map(line => line.trim()).filter(line => line.length > 0);\n\n      let currentChoice = null;\n      let choiceText = '';\n      let questionLines = [];\n      let foundFirstChoice = false;\n\n      for (const line of lines) {\n        // Check for choice pattern at start of line\n        const choiceMatch = line.match(/^([A-E])\\)\\s*(.*)/);\n\n        if (choiceMatch) {\n          // Save previous choice if exists\n          if (currentChoice && choiceText.trim()) {\n            choices[currentChoice] = choiceText.trim();\n          }\n\n          currentChoice = choiceMatch[1];\n          choiceText = choiceMatch[2];\n          foundFirstChoice = true;\n\n        } else if (currentChoice && foundFirstChoice) {\n          // Continue current choice text\n          choiceText += ' ' + line;\n\n        } else if (!foundFirstChoice) {\n          // Still in question text area\n          questionLines.push(line);\n        }\n      }\n\n      // Save last choice\n      if (currentChoice && choiceText.trim()) {\n        choices[currentChoice] = choiceText.trim();\n      }\n\n      questionText = questionLines.join(' ').trim();\n      console.log(`Method 2 result: ${Object.keys(choices).length} choices, question length: ${questionText.length}`);\n    }\n\n    // Method 3: If still no good results, try more aggressive parsing\n    if (Object.keys(choices).length < 3 && content.length > 100) {\n      console.log('Using Method 3: Aggressive parsing');\n\n      // Look for any A) B) C) patterns anywhere in text\n      const aggressivePattern = /([A-E])\\)\\s*([^A-E\\n]{10,200}?)(?=\\s*[A-E]\\)|$)/g;\n      const aggressiveMatches = [...content.matchAll(aggressivePattern)];\n\n      if (aggressiveMatches.length >= 3) {\n        // Clear previous results\n        Object.keys(choices).forEach(key => delete choices[key]);\n\n        // Extract question text (everything before first choice)\n        const firstChoiceIndex = aggressiveMatches[0].index;\n        questionText = content.substring(0, firstChoiceIndex).trim();\n\n        // Extract choices\n        aggressiveMatches.forEach(match => {\n          const choice = match[1];\n          const text = match[2].trim();\n          choices[choice] = text;\n        });\n\n        console.log(`Method 3 success: ${Object.keys(choices).length} choices extracted`);\n      }\n    }\n\n    // Clean up and validate\n    questionText = questionText.replace(/\\s+/g, ' ').trim();\n    Object.keys(choices).forEach(key => {\n      choices[key] = choices[key].replace(/\\s+/g, ' ').trim();\n      // Remove empty choices\n      if (!choices[key]) {\n        delete choices[key];\n      }\n    });\n\n    console.log(`Final result for Question ${questionNum}:`);\n    console.log(`- Question text: ${questionText.length} chars`);\n    console.log(`- Choices: ${Object.keys(choices).join(', ')}`);\n    console.log(`- Context: ${context ? 'Yes' : 'No'}`);\n\n    // Validation and fallbacks\n    if (!questionText && Object.keys(choices).length === 0) {\n      console.log(`Question ${questionNum}: Complete parsing failure`);\n      return null;\n    }\n\n    // Ensure minimum choices\n    const expectedChoices = ['A', 'B', 'C', 'D', 'E'];\n    const missingChoices = expectedChoices.filter(c => !choices[c]);\n\n    if (missingChoices.length > 2) {\n      console.log(`Question ${questionNum}: Missing too many choices, adding placeholders`);\n      missingChoices.forEach(choice => {\n        choices[choice] = `Choice ${choice} (not found in PDF)`;\n      });\n    }\n\n    // Ensure question text\n    if (!questionText.trim()) {\n      questionText = `Question ${questionNum} content (parsing incomplete - check PDF)`;\n    }\n\n    return {\n      questionNumber: questionNum,\n      totalQuestions: totalQ,\n      questionId: questionId,\n      context: context,\n      questionText: questionText,\n      choices: choices,\n      isGroupQuestion: Boolean(context)\n    };\n  }\n\n  extractAnswers(text) {\n    const answers = {};\n\n    console.log('Extracting answers from text...');\n\n    // Find all question markers in answer file\n    const questionMarkers = [];\n    const patterns = [\n      /Question #(\\d+) of (\\d+) Question ID: (\\d+)/g,\n      /Question #(\\d+)/g\n    ];\n\n    for (const pattern of patterns) {\n      let match;\n      while ((match = pattern.exec(text)) !== null) {\n        questionMarkers.push({\n          index: match.index,\n          fullMatch: match[0],\n          questionNum: parseInt(match[1])\n        });\n      }\n    }\n\n    console.log(`Found ${questionMarkers.length} question markers in answer file`);\n\n    // Sort by position\n    questionMarkers.sort((a, b) => a.index - b.index);\n\n    // Extract answer content for each question\n    for (let i = 0; i < questionMarkers.length; i++) {\n      const marker = questionMarkers[i];\n      const nextMarker = questionMarkers[i + 1];\n\n      // Get content from current marker to next marker (or end of text)\n      const startIndex = marker.index + marker.fullMatch.length;\n      const endIndex = nextMarker ? nextMarker.index : text.length;\n      const content = text.substring(startIndex, endIndex).trim();\n\n      console.log(`Processing answer for question ${marker.questionNum}, content length: ${content.length}`);\n\n      // Find correct answer\n      let correctAnswer = null;\n      const answerPatterns = [\n        /The correct answer is ([A-E])\\./i,\n        /The correct answer is ([A-E])\\s/i,\n        /Correct answer:\\s*([A-E])/i,\n        /Answer:\\s*([A-E])/i,\n        /([A-E])\\s*is correct/i,\n        /Choice\\s*([A-E])\\s*is correct/i\n      ];\n\n      for (const pattern of answerPatterns) {\n        const match = content.match(pattern);\n        if (match) {\n          correctAnswer = match[1].toUpperCase();\n          console.log(`Found correct answer ${correctAnswer} for question ${marker.questionNum}`);\n          break;\n        }\n      }\n\n      // Find explanation - be more flexible and comprehensive\n      let explanation = '';\n\n      console.log(`Looking for explanation in question ${marker.questionNum} content...`);\n      console.log(`Content preview: ${content.substring(0, 500)}...`);\n\n      // Multiple patterns to find explanation\n      const explanationPatterns = [\n        /Explanation[:\\s]*\\n?(.*?)(?=Question #|$)/is,\n        /Explanation[:\\s]+(.*?)(?=\\(Module|$)/is,\n        /Explanation[:\\s]+(.*?)(?=\\n\\s*\\n|$)/is,\n        /Explanation[:\\s]+(.*)/is\n      ];\n\n      for (let i = 0; i < explanationPatterns.length; i++) {\n        const pattern = explanationPatterns[i];\n        const match = content.match(pattern);\n        if (match && match[1].trim().length > 10) {\n          explanation = match[1].trim();\n          console.log(`Found explanation using pattern ${i + 1} for question ${marker.questionNum}`);\n          break;\n        }\n      }\n\n      // If no explanation found with patterns, try manual extraction\n      if (!explanation) {\n        const explKeywords = ['explanation', 'Explanation', 'EXPLANATION'];\n        for (const keyword of explKeywords) {\n          const explIndex = content.indexOf(keyword);\n          if (explIndex !== -1) {\n            // Extract everything after the keyword\n            let afterExpl = content.substring(explIndex + keyword.length).trim();\n\n            // Remove leading colons, spaces, newlines\n            afterExpl = afterExpl.replace(/^[:\\s\\n]+/, '');\n\n            // Take everything until next question or end\n            const nextQuestionIndex = afterExpl.search(/Question #\\d+/i);\n            if (nextQuestionIndex !== -1) {\n              afterExpl = afterExpl.substring(0, nextQuestionIndex);\n            }\n\n            if (afterExpl.length > 20) {\n              explanation = afterExpl.trim();\n              console.log(`Manual explanation extraction for question ${marker.questionNum}`);\n              break;\n            }\n          }\n        }\n      }\n\n      // Clean up explanation\n      if (explanation) {\n        // Normalize whitespace\n        explanation = explanation.replace(/\\s+/g, ' ');\n\n        // Remove common suffixes and prefixes\n        explanation = explanation.replace(/^\\s*[:\\-\\s]+/, ''); // Remove leading colons/dashes\n        explanation = explanation.replace(/\\(Module.*?\\)$/i, '');\n        explanation = explanation.replace(/\\(LOS.*?\\)$/i, '');\n        explanation = explanation.replace(/\\(Reading.*?\\)$/i, '');\n\n        // Remove trailing page numbers or references\n        explanation = explanation.replace(/\\s+\\d+\\s*$/, '');\n\n        explanation = explanation.trim();\n\n        console.log(`Cleaned explanation for question ${marker.questionNum}, final length: ${explanation.length}`);\n        console.log(`Explanation preview: ${explanation.substring(0, 100)}...`);\n      } else {\n        console.log(`No explanation found for question ${marker.questionNum}`);\n      }\n\n      if (correctAnswer) {\n        answers[marker.questionNum] = {\n          correctAnswer: correctAnswer,\n          explanation: explanation || 'No explanation available'\n        };\n      } else {\n        console.log(`No correct answer found for question ${marker.questionNum}`);\n      }\n    }\n\n    console.log(`Extracted answers for ${Object.keys(answers).length} questions`);\n    return answers;\n  }\n\n  async parseFiles(questionFile, answerFile) {\n    try {\n      const [questions, answers] = await Promise.all([\n        this.parseQuestionFile(questionFile),\n        this.parseAnswerFile(answerFile)\n      ]);\n      \n      return { questions, answers };\n    } catch (error) {\n      console.error('Error parsing files:', error);\n      throw error;\n    }\n  }\n}\n\nexport default CFAQuestionParser;\n"], "mappings": "AAAA;AACA,OAAO,KAAKA,QAAQ,MAAM,YAAY;;AAEtC;AACAA,QAAQ,CAACC,mBAAmB,CAACC,SAAS,GAAG,+DAA+D;AAExG,MAAMC,iBAAiB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;EACnB;EAEA,MAAMC,iBAAiBA,CAACC,IAAI,EAAE;IAC5B,IAAI;MACF,MAAMC,WAAW,GAAG,MAAMD,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5C,MAAMC,GAAG,GAAG,MAAMV,QAAQ,CAACW,WAAW,CAACF,WAAW,CAAC,CAACG,OAAO;MAE3D,IAAIC,QAAQ,GAAG,EAAE;;MAEjB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,GAAG,CAACK,QAAQ,EAAED,CAAC,EAAE,EAAE;QACtC,MAAME,IAAI,GAAG,MAAMN,GAAG,CAACO,OAAO,CAACH,CAAC,CAAC;QACjC,MAAMI,WAAW,GAAG,MAAMF,IAAI,CAACG,cAAc,CAAC,CAAC;;QAE/C;QACA,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAIC,KAAK,GAAG,IAAI;QAEhB,KAAK,MAAMC,IAAI,IAAIJ,WAAW,CAACK,KAAK,EAAE;UACpC;UACA,IAAIF,KAAK,KAAK,IAAI,IAAIG,IAAI,CAACC,GAAG,CAACH,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,GAAGL,KAAK,CAAC,GAAG,CAAC,EAAE;YAC7DD,QAAQ,IAAI,IAAI;UAClB;UAEAA,QAAQ,IAAIE,IAAI,CAACK,GAAG,GAAG,GAAG;UAC1BN,KAAK,GAAGC,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC;QAC3B;QAEAb,QAAQ,IAAIO,QAAQ,GAAG,MAAM;MAC/B;;MAEA;MACAQ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEhB,QAAQ,CAACiB,MAAM,CAAC;MACtDF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEhB,QAAQ,CAACkB,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;;MAElE;MACA,MAAM1B,SAAS,GAAG,IAAI,CAAC2B,gBAAgB,CAACnB,QAAQ,CAAC;MACjDe,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAExB,SAAS,CAACyB,MAAM,CAAC;MAErD,OAAOzB,SAAS;IAClB,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;EAEA,MAAMC,eAAeA,CAAC1B,IAAI,EAAE;IAC1B,IAAI;MACF,MAAMC,WAAW,GAAG,MAAMD,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5C,MAAMC,GAAG,GAAG,MAAMV,QAAQ,CAACW,WAAW,CAACF,WAAW,CAAC,CAACG,OAAO;MAE3D,IAAIC,QAAQ,GAAG,EAAE;;MAEjB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,GAAG,CAACK,QAAQ,EAAED,CAAC,EAAE,EAAE;QACtC,MAAME,IAAI,GAAG,MAAMN,GAAG,CAACO,OAAO,CAACH,CAAC,CAAC;QACjC,MAAMI,WAAW,GAAG,MAAMF,IAAI,CAACG,cAAc,CAAC,CAAC;;QAE/C;QACA,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAIC,KAAK,GAAG,IAAI;QAEhB,KAAK,MAAMC,IAAI,IAAIJ,WAAW,CAACK,KAAK,EAAE;UACpC;UACA,IAAIF,KAAK,KAAK,IAAI,IAAIG,IAAI,CAACC,GAAG,CAACH,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,GAAGL,KAAK,CAAC,GAAG,CAAC,EAAE;YAC7DD,QAAQ,IAAI,IAAI;UAClB;UAEAA,QAAQ,IAAIE,IAAI,CAACK,GAAG,GAAG,GAAG;UAC1BN,KAAK,GAAGC,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC;QAC3B;QAEAb,QAAQ,IAAIO,QAAQ,GAAG,MAAM;MAC/B;MAEAQ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEhB,QAAQ,CAACiB,MAAM,CAAC;MACxDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEhB,QAAQ,CAACkB,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;;MAEhE;MACA,MAAMzB,OAAO,GAAG,IAAI,CAAC6B,cAAc,CAACtB,QAAQ,CAAC;MAC7Ce,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,MAAM,CAACC,IAAI,CAAC/B,OAAO,CAAC,CAACwB,MAAM,CAAC;MAE9D,OAAOxB,OAAO;IAChB,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF;EAEAD,gBAAgBA,CAACM,IAAI,EAAE;IACrB,MAAMjC,SAAS,GAAG,EAAE;IAEpBuB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnDD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAES,IAAI,CAACR,MAAM,CAAC;;IAExC;IACA,MAAMS,eAAe,GAAG,EAAE;IAC1B,MAAMC,eAAe,GAAG,8EAA8E;IAEtG,IAAIC,KAAK;IACT,OAAO,CAACA,KAAK,GAAGD,eAAe,CAACE,IAAI,CAACJ,IAAI,CAAC,MAAM,IAAI,EAAE;MACpD,MAAMK,MAAM,GAAGC,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;MACjC,MAAMI,IAAI,GAAGJ,KAAK,CAAC,CAAC,CAAC,GAAGG,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;MACjD,MAAMK,MAAM,GAAGF,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;MACjC,MAAMM,UAAU,GAAGN,KAAK,CAAC,CAAC,CAAC,IAAI,IAAIE,MAAM,EAAE;MAE3CJ,eAAe,CAACS,IAAI,CAAC;QACnBL,MAAM;QACNE,IAAI;QACJC,MAAM;QACNC,UAAU;QACVE,OAAO,EAAEJ,IAAI,KAAK,IAAI;QACtBK,KAAK,EAAET,KAAK,CAACS,KAAK;QAClBC,SAAS,EAAEV,KAAK,CAAC,CAAC;MACpB,CAAC,CAAC;IACJ;IAEAb,OAAO,CAACC,GAAG,CAAC,SAASU,eAAe,CAACT,MAAM,oBAAoB,EAAES,eAAe,CAAC;;IAEjF;IACA,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,eAAe,CAACT,MAAM,EAAEhB,CAAC,EAAE,EAAE;MAC/C,MAAMsC,MAAM,GAAGb,eAAe,CAACzB,CAAC,CAAC;MACjC,MAAMuC,UAAU,GAAGd,eAAe,CAACzB,CAAC,GAAG,CAAC,CAAC;;MAEzC;MACA,MAAMwC,UAAU,GAAGF,MAAM,CAACF,KAAK,GAAGE,MAAM,CAACD,SAAS,CAACrB,MAAM;MACzD,MAAMyB,QAAQ,GAAGF,UAAU,GAAGA,UAAU,CAACH,KAAK,GAAGZ,IAAI,CAACR,MAAM;MAC5D,MAAM0B,OAAO,GAAGlB,IAAI,CAACP,SAAS,CAACuB,UAAU,EAAEC,QAAQ,CAAC,CAACE,IAAI,CAAC,CAAC;MAE3D7B,OAAO,CAACC,GAAG,CAAC,2BAA2Bf,CAAC,GAAG,CAAC,MAAMsC,MAAM,CAACT,MAAM,GAAGS,MAAM,CAACP,IAAI,GAAG,IAAIO,MAAM,CAACP,IAAI,EAAE,GAAG,EAAE,MAAM,CAAC;MAC7GjB,OAAO,CAACC,GAAG,CAAC,mBAAmB2B,OAAO,CAAC1B,MAAM,EAAE,CAAC;MAChDF,OAAO,CAACC,GAAG,CAAC,oBAAoB2B,OAAO,CAACzB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;MAE/D,IAAIqB,MAAM,CAACH,OAAO,EAAE;QAClB;QACArB,OAAO,CAACC,GAAG,CAAC,6BAA6BuB,MAAM,CAACT,MAAM,IAAIS,MAAM,CAACP,IAAI,EAAE,CAAC;;QAExE;QACA,MAAMa,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAC/CH,OAAO,EACPJ,MAAM,CAACT,MAAM,EACbS,MAAM,CAACP,IAAI,EACXO,MAAM,CAACN,MAAM,EACbM,MAAM,CAACL,UACT,CAAC;QAED1C,SAAS,CAAC2C,IAAI,CAAC,GAAGU,cAAc,CAAC;MACnC,CAAC,MAAM;QACL;QACA9B,OAAO,CAACC,GAAG,CAAC,8BAA8BuB,MAAM,CAACT,MAAM,EAAE,CAAC;QAE1D,MAAMiB,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CL,OAAO,EACPJ,MAAM,CAACT,MAAM,EACbS,MAAM,CAACN,MAAM,EACbM,MAAM,CAACL,UACT,CAAC;QAED,IAAIa,YAAY,EAAE;UAChBvD,SAAS,CAAC2C,IAAI,CAACY,YAAY,CAAC;QAC9B;MACF;IACF;;IAEA;IACAvD,SAAS,CAACyD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,cAAc,GAAGD,CAAC,CAACC,cAAc,CAAC;IAE7DrC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5CD,OAAO,CAACC,GAAG,CAAC,8BAA8BxB,SAAS,CAACyB,MAAM,EAAE,CAAC;IAC7DF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAExB,SAAS,CAAC6D,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACF,cAAc,CAAC,CAAC;IAEtE,OAAO5D,SAAS;EAClB;EAEA+D,2BAA2BA,CAAC9B,IAAI,EAAE;IAChCV,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACrD,MAAMxB,SAAS,GAAG,EAAE;;IAEpB;IACA,MAAMgE,QAAQ,GAAG,CACf,mCAAmC;IAAG;IACtC,0DAA0D,CAAG;IAAA,CAC9D;IAED,KAAK,MAAMC,OAAO,IAAID,QAAQ,EAAE;MAC9B,MAAME,OAAO,GAAG,CAAC,GAAGjC,IAAI,CAACkC,QAAQ,CAACF,OAAO,CAAC,CAAC;MAC3C1C,OAAO,CAACC,GAAG,CAAC,6BAA6B0C,OAAO,CAACzC,MAAM,UAAU,CAAC;MAElE,KAAK,MAAMW,KAAK,IAAI8B,OAAO,EAAE;QAC3B,MAAME,WAAW,GAAG7B,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;QACtC,MAAMe,OAAO,GAAGf,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC;QAE/B,IAAID,OAAO,CAAC1B,MAAM,GAAG,EAAE,EAAE;UAAE;UACzB,MAAM8B,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CL,OAAO,EACPiB,WAAW,EACX,GAAG;UAAE;UACL,MAAM,GAAGA,WACX,CAAC;UAED,IAAIb,YAAY,EAAE;YAChBvD,SAAS,CAAC2C,IAAI,CAACY,YAAY,CAAC;UAC9B;QACF;MACF;MAEA,IAAIvD,SAAS,CAACyB,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC;IACnC;IAEA,OAAOzB,SAAS;EAClB;EAEAsD,qBAAqBA,CAACH,OAAO,EAAEb,MAAM,EAAEE,IAAI,EAAEC,MAAM,EAAEC,UAAU,EAAE;IAC/D,MAAM1C,SAAS,GAAG,EAAE;IAEpBuB,OAAO,CAACC,GAAG,CAAC,8BAA8Bc,MAAM,IAAIE,IAAI,IAAIF,MAAM,EAAE,CAAC;IACrEf,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE2B,OAAO,CAACzB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;;IAEhE;IACA,IAAI2C,OAAO,GAAG,EAAE;IAChB,MAAMC,YAAY,GAAGnB,OAAO,CAACf,KAAK,CAAC,+BAA+B,CAAC;IACnE,IAAIkC,YAAY,IAAIA,YAAY,CAAC,CAAC,CAAC,CAAClB,IAAI,CAAC,CAAC,CAAC3B,MAAM,GAAG,EAAE,EAAE;MACtD4C,OAAO,GAAGC,YAAY,CAAC,CAAC,CAAC,CAAClB,IAAI,CAAC,CAAC;MAChC7B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE6C,OAAO,CAAC3C,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC9D;;IAEA;IACA,IAAIc,IAAI,IAAIA,IAAI,GAAGF,MAAM,EAAE;MACzBf,OAAO,CAACC,GAAG,CAAC,oCAAoCc,MAAM,OAAOE,IAAI,EAAE,CAAC;;MAEpE;MACA,KAAK,IAAI+B,IAAI,GAAGjC,MAAM,EAAEiC,IAAI,IAAI/B,IAAI,EAAE+B,IAAI,EAAE,EAAE;QAC5C;QACA,MAAMP,QAAQ,GAAG,CACf,IAAIQ,MAAM,CAAC,GAAGD,IAAI,cAAcA,IAAI,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EACtD,IAAIC,MAAM,CAAC,eAAeD,IAAI,8BAA8BA,IAAI,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CACjF;QAED,IAAIE,eAAe,GAAG,EAAE;QACxB,KAAK,MAAMR,OAAO,IAAID,QAAQ,EAAE;UAC9B,MAAM5B,KAAK,GAAGe,OAAO,CAACf,KAAK,CAAC6B,OAAO,CAAC;UACpC,IAAI7B,KAAK,EAAE;YACTqC,eAAe,GAAGrC,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC;YACjC;UACF;QACF;QAEA,IAAIqB,eAAe,EAAE;UACnBlD,OAAO,CAACC,GAAG,CAAC,8BAA8B+C,IAAI,EAAE,CAAC;UACjD,MAAMhB,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CiB,eAAe,EACfF,IAAI,EACJ9B,MAAM,EACN,GAAGC,UAAU,IAAI6B,IAAI,EAAE,EACvBF,OACF,CAAC;UAED,IAAId,YAAY,EAAE;YAChBvD,SAAS,CAAC2C,IAAI,CAACY,YAAY,CAAC;UAC9B;QACF;MACF;IACF;;IAEA;IACA,IAAIvD,SAAS,CAACyB,MAAM,KAAK,CAAC,EAAE;MAC1BF,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;MACtF,MAAM+B,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CL,OAAO,EACPb,MAAM,EACNG,MAAM,EACNC,UAAU,EACV2B,OACF,CAAC;MAED,IAAId,YAAY,EAAE;QAChBvD,SAAS,CAAC2C,IAAI,CAACY,YAAY,CAAC;MAC9B;IACF;IAEAhC,OAAO,CAACC,GAAG,CAAC,aAAaxB,SAAS,CAACyB,MAAM,uBAAuB,CAAC;IACjE,OAAOzB,SAAS;EAClB;EAEAwD,mBAAmBA,CAACL,OAAO,EAAEiB,WAAW,EAAE3B,MAAM,EAAEC,UAAU,EAAE2B,OAAO,GAAG,EAAE,EAAE;IAC1E,IAAI,CAAClB,OAAO,CAACC,IAAI,CAAC,CAAC,EAAE,OAAO,IAAI;IAEhC7B,OAAO,CAACC,GAAG,CAAC,0BAA0B4C,WAAW,MAAM,CAAC;IACxD7C,OAAO,CAACC,GAAG,CAAC,mBAAmB2B,OAAO,CAAC1B,MAAM,EAAE,CAAC;IAChDF,OAAO,CAACC,GAAG,CAAC,oBAAoB2B,OAAO,CAACzB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;IAE/D,IAAIgD,YAAY,GAAG,EAAE;IACrB,MAAMC,OAAO,GAAG,CAAC,CAAC;;IAElB;IACA,MAAMC,aAAa,GAAG,yCAAyC;IAC/D,MAAMC,aAAa,GAAG,CAAC,GAAG1B,OAAO,CAACgB,QAAQ,CAACS,aAAa,CAAC,CAAC;IAE1DrD,OAAO,CAACC,GAAG,CAAC,SAASqD,aAAa,CAACpD,MAAM,iBAAiB,CAAC;IAE3D,IAAIoD,aAAa,CAACpD,MAAM,IAAI,CAAC,EAAE;MAC7B;MACA,MAAMqD,gBAAgB,GAAGD,aAAa,CAAC,CAAC,CAAC,CAAChC,KAAK;MAC/C6B,YAAY,GAAGvB,OAAO,CAACzB,SAAS,CAAC,CAAC,EAAEoD,gBAAgB,CAAC,CAAC1B,IAAI,CAAC,CAAC;;MAE5D;MACAyB,aAAa,CAACE,OAAO,CAAC3C,KAAK,IAAI;QAC7B,MAAM4C,MAAM,GAAG5C,KAAK,CAAC,CAAC,CAAC;QACvB,MAAMH,IAAI,GAAGG,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC;QAC5B,IAAInB,IAAI,CAACR,MAAM,GAAG,CAAC,EAAE;UACnBkD,OAAO,CAACK,MAAM,CAAC,GAAG/C,IAAI;QACxB;MACF,CAAC,CAAC;MAEFV,OAAO,CAACC,GAAG,CAAC,qBAAqBO,MAAM,CAACC,IAAI,CAAC2C,OAAO,CAAC,CAAClD,MAAM,oBAAoB,CAAC;IACnF,CAAC,MAAM;MACL;MACAF,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MAEnD,MAAMyD,KAAK,GAAG9B,OAAO,CAAC+B,KAAK,CAAC,IAAI,CAAC,CAACrB,GAAG,CAACsB,IAAI,IAAIA,IAAI,CAAC/B,IAAI,CAAC,CAAC,CAAC,CAACgC,MAAM,CAACD,IAAI,IAAIA,IAAI,CAAC1D,MAAM,GAAG,CAAC,CAAC;MAE1F,IAAI4D,aAAa,GAAG,IAAI;MACxB,IAAIC,UAAU,GAAG,EAAE;MACnB,IAAIC,aAAa,GAAG,EAAE;MACtB,IAAIC,gBAAgB,GAAG,KAAK;MAE5B,KAAK,MAAML,IAAI,IAAIF,KAAK,EAAE;QACxB;QACA,MAAMQ,WAAW,GAAGN,IAAI,CAAC/C,KAAK,CAAC,mBAAmB,CAAC;QAEnD,IAAIqD,WAAW,EAAE;UACf;UACA,IAAIJ,aAAa,IAAIC,UAAU,CAAClC,IAAI,CAAC,CAAC,EAAE;YACtCuB,OAAO,CAACU,aAAa,CAAC,GAAGC,UAAU,CAAClC,IAAI,CAAC,CAAC;UAC5C;UAEAiC,aAAa,GAAGI,WAAW,CAAC,CAAC,CAAC;UAC9BH,UAAU,GAAGG,WAAW,CAAC,CAAC,CAAC;UAC3BD,gBAAgB,GAAG,IAAI;QAEzB,CAAC,MAAM,IAAIH,aAAa,IAAIG,gBAAgB,EAAE;UAC5C;UACAF,UAAU,IAAI,GAAG,GAAGH,IAAI;QAE1B,CAAC,MAAM,IAAI,CAACK,gBAAgB,EAAE;UAC5B;UACAD,aAAa,CAAC5C,IAAI,CAACwC,IAAI,CAAC;QAC1B;MACF;;MAEA;MACA,IAAIE,aAAa,IAAIC,UAAU,CAAClC,IAAI,CAAC,CAAC,EAAE;QACtCuB,OAAO,CAACU,aAAa,CAAC,GAAGC,UAAU,CAAClC,IAAI,CAAC,CAAC;MAC5C;MAEAsB,YAAY,GAAGa,aAAa,CAACG,IAAI,CAAC,GAAG,CAAC,CAACtC,IAAI,CAAC,CAAC;MAC7C7B,OAAO,CAACC,GAAG,CAAC,oBAAoBO,MAAM,CAACC,IAAI,CAAC2C,OAAO,CAAC,CAAClD,MAAM,8BAA8BiD,YAAY,CAACjD,MAAM,EAAE,CAAC;IACjH;;IAEA;IACA,IAAIM,MAAM,CAACC,IAAI,CAAC2C,OAAO,CAAC,CAAClD,MAAM,GAAG,CAAC,IAAI0B,OAAO,CAAC1B,MAAM,GAAG,GAAG,EAAE;MAC3DF,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;;MAEjD;MACA,MAAMmE,iBAAiB,GAAG,kDAAkD;MAC5E,MAAMC,iBAAiB,GAAG,CAAC,GAAGzC,OAAO,CAACgB,QAAQ,CAACwB,iBAAiB,CAAC,CAAC;MAElE,IAAIC,iBAAiB,CAACnE,MAAM,IAAI,CAAC,EAAE;QACjC;QACAM,MAAM,CAACC,IAAI,CAAC2C,OAAO,CAAC,CAACI,OAAO,CAACc,GAAG,IAAI,OAAOlB,OAAO,CAACkB,GAAG,CAAC,CAAC;;QAExD;QACA,MAAMf,gBAAgB,GAAGc,iBAAiB,CAAC,CAAC,CAAC,CAAC/C,KAAK;QACnD6B,YAAY,GAAGvB,OAAO,CAACzB,SAAS,CAAC,CAAC,EAAEoD,gBAAgB,CAAC,CAAC1B,IAAI,CAAC,CAAC;;QAE5D;QACAwC,iBAAiB,CAACb,OAAO,CAAC3C,KAAK,IAAI;UACjC,MAAM4C,MAAM,GAAG5C,KAAK,CAAC,CAAC,CAAC;UACvB,MAAMH,IAAI,GAAGG,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC;UAC5BuB,OAAO,CAACK,MAAM,CAAC,GAAG/C,IAAI;QACxB,CAAC,CAAC;QAEFV,OAAO,CAACC,GAAG,CAAC,qBAAqBO,MAAM,CAACC,IAAI,CAAC2C,OAAO,CAAC,CAAClD,MAAM,oBAAoB,CAAC;MACnF;IACF;;IAEA;IACAiD,YAAY,GAAGA,YAAY,CAACoB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC1C,IAAI,CAAC,CAAC;IACvDrB,MAAM,CAACC,IAAI,CAAC2C,OAAO,CAAC,CAACI,OAAO,CAACc,GAAG,IAAI;MAClClB,OAAO,CAACkB,GAAG,CAAC,GAAGlB,OAAO,CAACkB,GAAG,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC1C,IAAI,CAAC,CAAC;MACvD;MACA,IAAI,CAACuB,OAAO,CAACkB,GAAG,CAAC,EAAE;QACjB,OAAOlB,OAAO,CAACkB,GAAG,CAAC;MACrB;IACF,CAAC,CAAC;IAEFtE,OAAO,CAACC,GAAG,CAAC,6BAA6B4C,WAAW,GAAG,CAAC;IACxD7C,OAAO,CAACC,GAAG,CAAC,oBAAoBkD,YAAY,CAACjD,MAAM,QAAQ,CAAC;IAC5DF,OAAO,CAACC,GAAG,CAAC,cAAcO,MAAM,CAACC,IAAI,CAAC2C,OAAO,CAAC,CAACe,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC5DnE,OAAO,CAACC,GAAG,CAAC,cAAc6C,OAAO,GAAG,KAAK,GAAG,IAAI,EAAE,CAAC;;IAEnD;IACA,IAAI,CAACK,YAAY,IAAI3C,MAAM,CAACC,IAAI,CAAC2C,OAAO,CAAC,CAAClD,MAAM,KAAK,CAAC,EAAE;MACtDF,OAAO,CAACC,GAAG,CAAC,YAAY4C,WAAW,4BAA4B,CAAC;MAChE,OAAO,IAAI;IACb;;IAEA;IACA,MAAM2B,eAAe,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACjD,MAAMC,cAAc,GAAGD,eAAe,CAACX,MAAM,CAACa,CAAC,IAAI,CAACtB,OAAO,CAACsB,CAAC,CAAC,CAAC;IAE/D,IAAID,cAAc,CAACvE,MAAM,GAAG,CAAC,EAAE;MAC7BF,OAAO,CAACC,GAAG,CAAC,YAAY4C,WAAW,iDAAiD,CAAC;MACrF4B,cAAc,CAACjB,OAAO,CAACC,MAAM,IAAI;QAC/BL,OAAO,CAACK,MAAM,CAAC,GAAG,UAAUA,MAAM,qBAAqB;MACzD,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAACN,YAAY,CAACtB,IAAI,CAAC,CAAC,EAAE;MACxBsB,YAAY,GAAG,YAAYN,WAAW,2CAA2C;IACnF;IAEA,OAAO;MACLR,cAAc,EAAEQ,WAAW;MAC3B8B,cAAc,EAAEzD,MAAM;MACtBC,UAAU,EAAEA,UAAU;MACtB2B,OAAO,EAAEA,OAAO;MAChBK,YAAY,EAAEA,YAAY;MAC1BC,OAAO,EAAEA,OAAO;MAChBwB,eAAe,EAAEC,OAAO,CAAC/B,OAAO;IAClC,CAAC;EACH;EAEAvC,cAAcA,CAACG,IAAI,EAAE;IACnB,MAAMhC,OAAO,GAAG,CAAC,CAAC;IAElBsB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;;IAE9C;IACA,MAAMU,eAAe,GAAG,EAAE;IAC1B,MAAM8B,QAAQ,GAAG,CACf,8CAA8C,EAC9C,kBAAkB,CACnB;IAED,KAAK,MAAMC,OAAO,IAAID,QAAQ,EAAE;MAC9B,IAAI5B,KAAK;MACT,OAAO,CAACA,KAAK,GAAG6B,OAAO,CAAC5B,IAAI,CAACJ,IAAI,CAAC,MAAM,IAAI,EAAE;QAC5CC,eAAe,CAACS,IAAI,CAAC;UACnBE,KAAK,EAAET,KAAK,CAACS,KAAK;UAClBC,SAAS,EAAEV,KAAK,CAAC,CAAC,CAAC;UACnBgC,WAAW,EAAE7B,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC;MACJ;IACF;IAEAb,OAAO,CAACC,GAAG,CAAC,SAASU,eAAe,CAACT,MAAM,kCAAkC,CAAC;;IAE9E;IACAS,eAAe,CAACuB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACb,KAAK,GAAGc,CAAC,CAACd,KAAK,CAAC;;IAEjD;IACA,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,eAAe,CAACT,MAAM,EAAEhB,CAAC,EAAE,EAAE;MAC/C,MAAMsC,MAAM,GAAGb,eAAe,CAACzB,CAAC,CAAC;MACjC,MAAMuC,UAAU,GAAGd,eAAe,CAACzB,CAAC,GAAG,CAAC,CAAC;;MAEzC;MACA,MAAMwC,UAAU,GAAGF,MAAM,CAACF,KAAK,GAAGE,MAAM,CAACD,SAAS,CAACrB,MAAM;MACzD,MAAMyB,QAAQ,GAAGF,UAAU,GAAGA,UAAU,CAACH,KAAK,GAAGZ,IAAI,CAACR,MAAM;MAC5D,MAAM0B,OAAO,GAAGlB,IAAI,CAACP,SAAS,CAACuB,UAAU,EAAEC,QAAQ,CAAC,CAACE,IAAI,CAAC,CAAC;MAE3D7B,OAAO,CAACC,GAAG,CAAC,kCAAkCuB,MAAM,CAACqB,WAAW,qBAAqBjB,OAAO,CAAC1B,MAAM,EAAE,CAAC;;MAEtG;MACA,IAAI4E,aAAa,GAAG,IAAI;MACxB,MAAMC,cAAc,GAAG,CACrB,kCAAkC,EAClC,kCAAkC,EAClC,4BAA4B,EAC5B,oBAAoB,EACpB,uBAAuB,EACvB,gCAAgC,CACjC;MAED,KAAK,MAAMrC,OAAO,IAAIqC,cAAc,EAAE;QACpC,MAAMlE,KAAK,GAAGe,OAAO,CAACf,KAAK,CAAC6B,OAAO,CAAC;QACpC,IAAI7B,KAAK,EAAE;UACTiE,aAAa,GAAGjE,KAAK,CAAC,CAAC,CAAC,CAACmE,WAAW,CAAC,CAAC;UACtChF,OAAO,CAACC,GAAG,CAAC,wBAAwB6E,aAAa,iBAAiBtD,MAAM,CAACqB,WAAW,EAAE,CAAC;UACvF;QACF;MACF;;MAEA;MACA,IAAIoC,WAAW,GAAG,EAAE;MAEpBjF,OAAO,CAACC,GAAG,CAAC,uCAAuCuB,MAAM,CAACqB,WAAW,aAAa,CAAC;MACnF7C,OAAO,CAACC,GAAG,CAAC,oBAAoB2B,OAAO,CAACzB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;;MAE/D;MACA,MAAM+E,mBAAmB,GAAG,CAC1B,6CAA6C,EAC7C,wCAAwC,EACxC,uCAAuC,EACvC,yBAAyB,CAC1B;MAED,KAAK,IAAIhG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgG,mBAAmB,CAAChF,MAAM,EAAEhB,CAAC,EAAE,EAAE;QACnD,MAAMwD,OAAO,GAAGwC,mBAAmB,CAAChG,CAAC,CAAC;QACtC,MAAM2B,KAAK,GAAGe,OAAO,CAACf,KAAK,CAAC6B,OAAO,CAAC;QACpC,IAAI7B,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC,CAAC3B,MAAM,GAAG,EAAE,EAAE;UACxC+E,WAAW,GAAGpE,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC;UAC7B7B,OAAO,CAACC,GAAG,CAAC,mCAAmCf,CAAC,GAAG,CAAC,iBAAiBsC,MAAM,CAACqB,WAAW,EAAE,CAAC;UAC1F;QACF;MACF;;MAEA;MACA,IAAI,CAACoC,WAAW,EAAE;QAChB,MAAME,YAAY,GAAG,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;QAClE,KAAK,MAAMC,OAAO,IAAID,YAAY,EAAE;UAClC,MAAME,SAAS,GAAGzD,OAAO,CAAC0D,OAAO,CAACF,OAAO,CAAC;UAC1C,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;YACpB;YACA,IAAIE,SAAS,GAAG3D,OAAO,CAACzB,SAAS,CAACkF,SAAS,GAAGD,OAAO,CAAClF,MAAM,CAAC,CAAC2B,IAAI,CAAC,CAAC;;YAEpE;YACA0D,SAAS,GAAGA,SAAS,CAAChB,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;;YAE9C;YACA,MAAMiB,iBAAiB,GAAGD,SAAS,CAACE,MAAM,CAAC,gBAAgB,CAAC;YAC5D,IAAID,iBAAiB,KAAK,CAAC,CAAC,EAAE;cAC5BD,SAAS,GAAGA,SAAS,CAACpF,SAAS,CAAC,CAAC,EAAEqF,iBAAiB,CAAC;YACvD;YAEA,IAAID,SAAS,CAACrF,MAAM,GAAG,EAAE,EAAE;cACzB+E,WAAW,GAAGM,SAAS,CAAC1D,IAAI,CAAC,CAAC;cAC9B7B,OAAO,CAACC,GAAG,CAAC,8CAA8CuB,MAAM,CAACqB,WAAW,EAAE,CAAC;cAC/E;YACF;UACF;QACF;MACF;;MAEA;MACA,IAAIoC,WAAW,EAAE;QACf;QACAA,WAAW,GAAGA,WAAW,CAACV,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;;QAE9C;QACAU,WAAW,GAAGA,WAAW,CAACV,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC;QACvDU,WAAW,GAAGA,WAAW,CAACV,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;QACxDU,WAAW,GAAGA,WAAW,CAACV,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;QACrDU,WAAW,GAAGA,WAAW,CAACV,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;;QAEzD;QACAU,WAAW,GAAGA,WAAW,CAACV,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;QAEnDU,WAAW,GAAGA,WAAW,CAACpD,IAAI,CAAC,CAAC;QAEhC7B,OAAO,CAACC,GAAG,CAAC,oCAAoCuB,MAAM,CAACqB,WAAW,mBAAmBoC,WAAW,CAAC/E,MAAM,EAAE,CAAC;QAC1GF,OAAO,CAACC,GAAG,CAAC,wBAAwBgF,WAAW,CAAC9E,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;MACzE,CAAC,MAAM;QACLH,OAAO,CAACC,GAAG,CAAC,qCAAqCuB,MAAM,CAACqB,WAAW,EAAE,CAAC;MACxE;MAEA,IAAIiC,aAAa,EAAE;QACjBpG,OAAO,CAAC8C,MAAM,CAACqB,WAAW,CAAC,GAAG;UAC5BiC,aAAa,EAAEA,aAAa;UAC5BG,WAAW,EAAEA,WAAW,IAAI;QAC9B,CAAC;MACH,CAAC,MAAM;QACLjF,OAAO,CAACC,GAAG,CAAC,wCAAwCuB,MAAM,CAACqB,WAAW,EAAE,CAAC;MAC3E;IACF;IAEA7C,OAAO,CAACC,GAAG,CAAC,yBAAyBO,MAAM,CAACC,IAAI,CAAC/B,OAAO,CAAC,CAACwB,MAAM,YAAY,CAAC;IAC7E,OAAOxB,OAAO;EAChB;EAEA,MAAMgH,UAAUA,CAACC,YAAY,EAAEC,UAAU,EAAE;IACzC,IAAI;MACF,MAAM,CAACnH,SAAS,EAAEC,OAAO,CAAC,GAAG,MAAMmH,OAAO,CAACC,GAAG,CAAC,CAC7C,IAAI,CAACnH,iBAAiB,CAACgH,YAAY,CAAC,EACpC,IAAI,CAACrF,eAAe,CAACsF,UAAU,CAAC,CACjC,CAAC;MAEF,OAAO;QAAEnH,SAAS;QAAEC;MAAQ,CAAC;IAC/B,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF;AACF;AAEA,eAAe9B,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}