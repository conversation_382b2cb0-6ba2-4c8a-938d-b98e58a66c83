{"ast": null, "code": "// PDF Parser for CFA Questions and Answers\nimport * as pdfjsLib from 'pdfjs-dist';\n\n// Set worker path - use the worker from the same version\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;\nclass CFAQuestionParser {\n  constructor() {\n    this.questions = [];\n    this.answers = {};\n  }\n  async parseQuestionFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n        fullText += pageText + '\\n\\n';\n      }\n\n      // Debug: Log extracted text\n      console.log('Extracted text length:', fullText.length);\n      console.log('First 2000 characters:', fullText.substring(0, 2000));\n\n      // Parse questions from text\n      const questions = this.extractQuestions(fullText);\n      console.log('Extracted questions:', questions.length);\n      return questions;\n    } catch (error) {\n      console.error('Error parsing question file:', error);\n      throw error;\n    }\n  }\n  async parseAnswerFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n        fullText += pageText + '\\n\\n';\n      }\n      console.log('Answer file text length:', fullText.length);\n      console.log('Answer file preview:', fullText.substring(0, 1000));\n\n      // Parse answers from text\n      const answers = this.extractAnswers(fullText);\n      console.log('Extracted answers:', Object.keys(answers).length);\n      return answers;\n    } catch (error) {\n      console.error('Error parsing answer file:', error);\n      throw error;\n    }\n  }\n  extractQuestions(text) {\n    const questions = [];\n    console.log('=== STARTING QUESTION EXTRACTION ===');\n    console.log('Text length:', text.length);\n\n    // First, let's find all question markers and their positions\n    const questionMarkers = [];\n    const questionPattern = /Question #(\\d+)(?:\\s*-\\s*(\\d+))?\\s+of\\s+(\\d+)(?:\\s+Question\\s+ID:\\s*(\\d+))?/g;\n    let match;\n    while ((match = questionPattern.exec(text)) !== null) {\n      const startQ = parseInt(match[1]);\n      const endQ = match[2] ? parseInt(match[2]) : null;\n      const totalQ = parseInt(match[3]);\n      const questionId = match[4] || `q${startQ}`;\n      questionMarkers.push({\n        startQ,\n        endQ,\n        totalQ,\n        questionId,\n        isGroup: endQ !== null,\n        index: match.index,\n        fullMatch: match[0]\n      });\n    }\n    console.log(`Found ${questionMarkers.length} question markers:`, questionMarkers);\n\n    // Process each marker\n    for (let i = 0; i < questionMarkers.length; i++) {\n      const marker = questionMarkers[i];\n      const nextMarker = questionMarkers[i + 1];\n\n      // Extract content from this marker to the next (or end of text)\n      const startIndex = marker.index + marker.fullMatch.length;\n      const endIndex = nextMarker ? nextMarker.index : text.length;\n      const content = text.substring(startIndex, endIndex).trim();\n      console.log(`\\n--- Processing marker ${i + 1}: Q${marker.startQ}${marker.endQ ? `-${marker.endQ}` : ''} ---`);\n      console.log(`Content length: ${content.length}`);\n      console.log(`Content preview: ${content.substring(0, 200)}...`);\n      if (marker.isGroup) {\n        // This is a group of questions with shared context\n        console.log(`Processing question group ${marker.startQ}-${marker.endQ}`);\n\n        // Extract shared context and individual questions\n        const groupQuestions = this.extractGroupQuestions(content, marker.startQ, marker.endQ, marker.totalQ, marker.questionId);\n        questions.push(...groupQuestions);\n      } else {\n        // Single question\n        console.log(`Processing single question ${marker.startQ}`);\n        const questionData = this.parseSingleQuestion(content, marker.startQ, marker.totalQ, marker.questionId);\n        if (questionData) {\n          questions.push(questionData);\n        }\n      }\n    }\n\n    // Sort questions by number\n    questions.sort((a, b) => a.questionNumber - b.questionNumber);\n    console.log(`\\n=== EXTRACTION COMPLETE ===`);\n    console.log(`Total questions extracted: ${questions.length}`);\n    console.log('Question numbers:', questions.map(q => q.questionNumber));\n    return questions;\n  }\n  extractQuestionsAlternative(text) {\n    console.log('Using alternative extraction method...');\n    const questions = [];\n\n    // Look for any numbered patterns that might be questions\n    const patterns = [/(\\d+)\\.\\s+([^0-9]+?)(?=\\d+\\.|$)/gs,\n    // 1. Question text\n    /Question\\s+(\\d+)[:\\.]?\\s*([^Q]+?)(?=Question\\s+\\d+|$)/gis // Question 1: text\n    ];\n    for (const pattern of patterns) {\n      const matches = [...text.matchAll(pattern)];\n      console.log(`Alternative pattern found ${matches.length} matches`);\n      for (const match of matches) {\n        const questionNum = parseInt(match[1]);\n        const content = match[2].trim();\n        if (content.length > 20) {\n          // Filter out very short matches\n          const questionData = this.parseSingleQuestion(content, questionNum, 100,\n          // Default total\n          'alt-' + questionNum);\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n      if (questions.length > 0) break; // Use first successful pattern\n    }\n    return questions;\n  }\n  extractGroupQuestions(content, startQ, endQ, totalQ, questionId) {\n    const questions = [];\n    console.log(`\\n=== EXTRACTING GROUP QUESTIONS ${startQ}-${endQ} ===`);\n    console.log('Group content length:', content.length);\n    console.log('Group content preview:', content.substring(0, 300));\n\n    // Step 1: Extract shared reading context\n    // The context is everything before the first individual question\n    let sharedContext = '';\n    let remainingContent = content;\n\n    // Look for the first individual question marker (like \"6.\" or \"Question 6\")\n    const firstQuestionPatterns = [new RegExp(`^(.*?)(?=\\\\b${startQ}\\\\.)`, 's'),\n    // Everything before \"6.\"\n    new RegExp(`^(.*?)(?=Question\\\\s+${startQ}\\\\b)`, 'is'),\n    // Everything before \"Question 6\"\n    new RegExp(`^(.*?)(?=\\\\b${startQ}\\\\s*\\\\))`, 's') // Everything before \"6)\"\n    ];\n    for (const pattern of firstQuestionPatterns) {\n      const match = content.match(pattern);\n      if (match && match[1].trim().length > 100) {\n        // Ensure substantial context\n        sharedContext = match[1].trim();\n        remainingContent = content.substring(match[1].length);\n        console.log(`Found shared context (${sharedContext.length} chars):`, sharedContext.substring(0, 150) + '...');\n        break;\n      }\n    }\n\n    // If no clear context separation, try to identify context by looking for reading material\n    if (!sharedContext && content.length > 500) {\n      // Look for typical reading indicators\n      const contextIndicators = [/^(.*?(?:company|firm|portfolio|investment|analysis|data|information|table|exhibit).*?)(?=\\d+\\.)/is, /^(.*?(?:following|given|based on|consider|assume).*?)(?=\\d+\\.)/is];\n      for (const pattern of contextIndicators) {\n        const match = content.match(pattern);\n        if (match && match[1].trim().length > 100) {\n          sharedContext = match[1].trim();\n          remainingContent = content.substring(match[1].length);\n          console.log(`Found context via indicators (${sharedContext.length} chars):`, sharedContext.substring(0, 150) + '...');\n          break;\n        }\n      }\n    }\n\n    // Step 2: Extract individual questions\n    console.log(`Looking for individual questions ${startQ} to ${endQ}`);\n    for (let qNum = startQ; qNum <= endQ; qNum++) {\n      console.log(`\\n--- Extracting Question ${qNum} ---`);\n\n      // Multiple patterns to find individual questions\n      const questionPatterns = [new RegExp(`\\\\b${qNum}\\\\.\\\\s*(.*?)(?=\\\\b${qNum + 1}\\\\.|$)`, 's'),\n      // \"6. content\"\n      new RegExp(`Question\\\\s+${qNum}[:\\\\.]?\\\\s*(.*?)(?=Question\\\\s+${qNum + 1}|$)`, 'is'),\n      // \"Question 6: content\"\n      new RegExp(`${qNum}\\\\)\\\\s*(.*?)(?=${qNum + 1}\\\\)|$)`, 's'),\n      // \"6) content\"\n      new RegExp(`^.*?${qNum}[^\\\\d]*?(.*?)(?=${qNum + 1}[^\\\\d]|$)`, 's') // Flexible pattern\n      ];\n      let questionContent = '';\n      for (let i = 0; i < questionPatterns.length; i++) {\n        const pattern = questionPatterns[i];\n        const match = remainingContent.match(pattern);\n        if (match && match[1].trim().length > 20) {\n          questionContent = match[1].trim();\n          console.log(`Found Q${qNum} using pattern ${i}: ${questionContent.substring(0, 100)}...`);\n          break;\n        }\n      }\n\n      // If still no content, try searching in the full content\n      if (!questionContent) {\n        console.log(`Trying full content search for Q${qNum}`);\n        for (let i = 0; i < questionPatterns.length; i++) {\n          const pattern = questionPatterns[i];\n          const match = content.match(pattern);\n          if (match && match[1].trim().length > 20) {\n            questionContent = match[1].trim();\n            console.log(`Found Q${qNum} in full content using pattern ${i}`);\n            break;\n          }\n        }\n      }\n      if (questionContent) {\n        const questionData = this.parseSingleQuestion(questionContent, qNum, totalQ, `${questionId}-${qNum}`, sharedContext);\n        if (questionData) {\n          questions.push(questionData);\n          console.log(`Successfully extracted Q${qNum}`);\n        } else {\n          console.log(`Failed to parse Q${qNum} content`);\n        }\n      } else {\n        console.log(`No content found for Q${qNum}`);\n      }\n    }\n    console.log(`\\n=== GROUP EXTRACTION COMPLETE ===`);\n    console.log(`Extracted ${questions.length} questions from group ${startQ}-${endQ}`);\n    console.log(`Shared context length: ${sharedContext.length}`);\n    return questions;\n  }\n  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {\n    if (!content.trim()) return null;\n    console.log(`\\n=== Parsing Question ${questionNum} ===`);\n    console.log(`Content length: ${content.length}`);\n    console.log(`Content preview: ${content.substring(0, 300)}...`);\n    let questionText = '';\n    const choices = {};\n\n    // Method 1: Look for choice patterns A) B) C) D) E)\n    const choicePattern = /([A-E])\\)\\s*([^A-E]*?)(?=\\s*[A-E]\\)|$)/g;\n    const choiceMatches = [...content.matchAll(choicePattern)];\n    console.log(`Found ${choiceMatches.length} choice matches`);\n    if (choiceMatches.length >= 3) {\n      // Extract question text (everything before first choice)\n      const firstChoiceIndex = choiceMatches[0].index;\n      questionText = content.substring(0, firstChoiceIndex).trim();\n\n      // Extract choices\n      choiceMatches.forEach(match => {\n        const choice = match[1];\n        const text = match[2].trim();\n        if (text.length > 0) {\n          choices[choice] = text;\n        }\n      });\n      console.log(`Method 1 success: ${Object.keys(choices).length} choices extracted`);\n    } else {\n      // Method 2: Line-by-line parsing with better logic\n      console.log('Using Method 2: Line-by-line parsing');\n      const lines = content.split(/\\n/).map(line => line.trim()).filter(line => line.length > 0);\n      let currentChoice = null;\n      let choiceText = '';\n      let questionLines = [];\n      let foundFirstChoice = false;\n      for (const line of lines) {\n        // Check for choice pattern at start of line\n        const choiceMatch = line.match(/^([A-E])\\)\\s*(.*)/);\n        if (choiceMatch) {\n          // Save previous choice if exists\n          if (currentChoice && choiceText.trim()) {\n            choices[currentChoice] = choiceText.trim();\n          }\n          currentChoice = choiceMatch[1];\n          choiceText = choiceMatch[2];\n          foundFirstChoice = true;\n        } else if (currentChoice && foundFirstChoice) {\n          // Continue current choice text\n          choiceText += ' ' + line;\n        } else if (!foundFirstChoice) {\n          // Still in question text area\n          questionLines.push(line);\n        }\n      }\n\n      // Save last choice\n      if (currentChoice && choiceText.trim()) {\n        choices[currentChoice] = choiceText.trim();\n      }\n      questionText = questionLines.join(' ').trim();\n      console.log(`Method 2 result: ${Object.keys(choices).length} choices, question length: ${questionText.length}`);\n    }\n\n    // Method 3: If still no good results, try more aggressive parsing\n    if (Object.keys(choices).length < 3 && content.length > 100) {\n      console.log('Using Method 3: Aggressive parsing');\n\n      // Look for any A) B) C) patterns anywhere in text\n      const aggressivePattern = /([A-E])\\)\\s*([^A-E\\n]{10,200}?)(?=\\s*[A-E]\\)|$)/g;\n      const aggressiveMatches = [...content.matchAll(aggressivePattern)];\n      if (aggressiveMatches.length >= 3) {\n        // Clear previous results\n        Object.keys(choices).forEach(key => delete choices[key]);\n\n        // Extract question text (everything before first choice)\n        const firstChoiceIndex = aggressiveMatches[0].index;\n        questionText = content.substring(0, firstChoiceIndex).trim();\n\n        // Extract choices\n        aggressiveMatches.forEach(match => {\n          const choice = match[1];\n          const text = match[2].trim();\n          choices[choice] = text;\n        });\n        console.log(`Method 3 success: ${Object.keys(choices).length} choices extracted`);\n      }\n    }\n\n    // Clean up and validate\n    questionText = questionText.replace(/\\s+/g, ' ').trim();\n    Object.keys(choices).forEach(key => {\n      choices[key] = choices[key].replace(/\\s+/g, ' ').trim();\n      // Remove empty choices\n      if (!choices[key]) {\n        delete choices[key];\n      }\n    });\n    console.log(`Final result for Question ${questionNum}:`);\n    console.log(`- Question text: ${questionText.length} chars`);\n    console.log(`- Choices: ${Object.keys(choices).join(', ')}`);\n    console.log(`- Context: ${context ? 'Yes' : 'No'}`);\n\n    // Validation and fallbacks\n    if (!questionText && Object.keys(choices).length === 0) {\n      console.log(`Question ${questionNum}: Complete parsing failure`);\n      return null;\n    }\n\n    // Ensure minimum choices\n    const expectedChoices = ['A', 'B', 'C', 'D', 'E'];\n    const missingChoices = expectedChoices.filter(c => !choices[c]);\n    if (missingChoices.length > 2) {\n      console.log(`Question ${questionNum}: Missing too many choices, adding placeholders`);\n      missingChoices.forEach(choice => {\n        choices[choice] = `Choice ${choice} (not found in PDF)`;\n      });\n    }\n\n    // Ensure question text\n    if (!questionText.trim()) {\n      questionText = `Question ${questionNum} content (parsing incomplete - check PDF)`;\n    }\n    return {\n      questionNumber: questionNum,\n      totalQuestions: totalQ,\n      questionId: questionId,\n      context: context,\n      questionText: questionText,\n      choices: choices,\n      isGroupQuestion: Boolean(context)\n    };\n  }\n  extractAnswers(text) {\n    const answers = {};\n    console.log('\\n=== STARTING ANSWER EXTRACTION ===');\n    console.log('Answer file text length:', text.length);\n    console.log('Answer file preview:', text.substring(0, 500));\n\n    // Step 1: Find all question markers in answer file\n    const questionMarkers = [];\n\n    // More comprehensive patterns for answer file\n    const markerPatterns = [/Question #(\\d+) of (\\d+) Question ID: (\\d+)/g, /Question #(\\d+) of (\\d+)/g, /Question #(\\d+)/g, /Question\\s+(\\d+)/g];\n    for (const pattern of markerPatterns) {\n      let match;\n      while ((match = pattern.exec(text)) !== null) {\n        const questionNum = parseInt(match[1]);\n        if (!questionMarkers.find(m => m.questionNum === questionNum)) {\n          questionMarkers.push({\n            index: match.index,\n            fullMatch: match[0],\n            questionNum: questionNum\n          });\n        }\n      }\n    }\n    console.log(`Found ${questionMarkers.length} question markers in answer file:`, questionMarkers.map(m => m.questionNum).sort((a, b) => a - b));\n\n    // Sort by position in text\n    questionMarkers.sort((a, b) => a.index - b.index);\n\n    // Step 2: Extract answer content for each question\n    for (let i = 0; i < questionMarkers.length; i++) {\n      const marker = questionMarkers[i];\n      const nextMarker = questionMarkers[i + 1];\n\n      // Get content from current marker to next marker (or end of text)\n      const startIndex = marker.index + marker.fullMatch.length;\n      const endIndex = nextMarker ? nextMarker.index : text.length;\n      const content = text.substring(startIndex, endIndex).trim();\n      console.log(`\\n--- Processing Answer for Question ${marker.questionNum} ---`);\n      console.log(`Content length: ${content.length}`);\n      console.log(`Content preview: ${content.substring(0, 300)}...`);\n\n      // Step 3: Find correct answer with multiple patterns\n      let correctAnswer = null;\n      const answerPatterns = [/The correct answer is\\s*([A-E])\\.?/i, /The correct answer is\\s*([A-E])\\s/i, /Correct answer:\\s*([A-E])\\.?/i, /Correct answer\\s*is\\s*([A-E])\\.?/i, /Answer:\\s*([A-E])\\.?/i, /Answer\\s*is\\s*([A-E])\\.?/i, /([A-E])\\s*is\\s*correct/i, /([A-E])\\s*is\\s*the\\s*correct/i, /Choice\\s*([A-E])\\s*is\\s*correct/i, /Option\\s*([A-E])\\s*is\\s*correct/i, /\\b([A-E])\\s*\\.\\s*$/m,\n      // Single letter at end of line\n      /^([A-E])$/m // Single letter on its own line\n      ];\n      for (let j = 0; j < answerPatterns.length; j++) {\n        const pattern = answerPatterns[j];\n        const match = content.match(pattern);\n        if (match) {\n          correctAnswer = match[1].toUpperCase();\n          console.log(`Found correct answer \"${correctAnswer}\" for Q${marker.questionNum} using pattern ${j}: ${pattern.source}`);\n          break;\n        }\n      }\n\n      // If still no answer, try more aggressive search\n      if (!correctAnswer) {\n        console.log(`No answer found with standard patterns, trying aggressive search for Q${marker.questionNum}`);\n\n        // Look for any single letter A-E that appears prominently\n        const aggressivePatterns = [/\\b([A-E])\\b/g // Any single letter A-E\n        ];\n        for (const pattern of aggressivePatterns) {\n          const matches = [...content.matchAll(pattern)];\n          if (matches.length > 0) {\n            // Take the first occurrence\n            correctAnswer = matches[0][1].toUpperCase();\n            console.log(`Found answer \"${correctAnswer}\" for Q${marker.questionNum} via aggressive search`);\n            break;\n          }\n        }\n      }\n\n      // Step 4: Extract explanation\n      let explanation = '';\n\n      // Multiple patterns to find explanation\n      const explanationPatterns = [/Explanation[:\\s]*\\n?(.*?)(?=Question #|$)/is, /Explanation[:\\s]+(.*?)(?=\\(Module|$)/is, /Explanation[:\\s]+(.*?)(?=\\n\\s*\\n|$)/is, /Explanation[:\\s]+(.*)/is];\n      for (let j = 0; j < explanationPatterns.length; j++) {\n        const pattern = explanationPatterns[j];\n        const match = content.match(pattern);\n        if (match && match[1].trim().length > 10) {\n          explanation = match[1].trim();\n          console.log(`Found explanation for Q${marker.questionNum} using pattern ${j}`);\n          break;\n        }\n      }\n\n      // Manual explanation extraction if patterns fail\n      if (!explanation) {\n        const explKeywords = ['explanation', 'Explanation', 'EXPLANATION'];\n        for (const keyword of explKeywords) {\n          const explIndex = content.toLowerCase().indexOf(keyword.toLowerCase());\n          if (explIndex !== -1) {\n            let afterExpl = content.substring(explIndex + keyword.length).trim();\n            afterExpl = afterExpl.replace(/^[:\\s\\n]+/, '');\n            const nextQuestionIndex = afterExpl.search(/Question #\\d+/i);\n            if (nextQuestionIndex !== -1) {\n              afterExpl = afterExpl.substring(0, nextQuestionIndex);\n            }\n            if (afterExpl.length > 20) {\n              explanation = afterExpl.trim();\n              console.log(`Manual explanation extraction for Q${marker.questionNum}`);\n              break;\n            }\n          }\n        }\n      }\n\n      // Clean up explanation\n      if (explanation) {\n        explanation = explanation.replace(/\\s+/g, ' ');\n        explanation = explanation.replace(/^\\s*[:\\-\\s]+/, '');\n        explanation = explanation.replace(/\\(Module.*?\\)$/i, '');\n        explanation = explanation.replace(/\\(LOS.*?\\)$/i, '');\n        explanation = explanation.replace(/\\(Reading.*?\\)$/i, '');\n        explanation = explanation.replace(/\\s+\\d+\\s*$/, '');\n        explanation = explanation.trim();\n      }\n\n      // Step 5: Store the answer data\n      if (correctAnswer) {\n        answers[marker.questionNum] = {\n          correctAnswer: correctAnswer,\n          explanation: explanation || 'No explanation available'\n        };\n        console.log(`✓ Successfully extracted answer for Q${marker.questionNum}: ${correctAnswer}`);\n      } else {\n        console.log(`✗ Failed to find correct answer for Q${marker.questionNum}`);\n        console.log(`Raw content for debugging: ${content.substring(0, 200)}...`);\n      }\n    }\n    console.log(`\\n=== ANSWER EXTRACTION COMPLETE ===`);\n    console.log(`Successfully extracted answers for ${Object.keys(answers).length} questions`);\n    console.log(`Answer summary:`, Object.keys(answers).sort((a, b) => a - b).map(q => `Q${q}:${answers[q].correctAnswer}`));\n    return answers;\n  }\n  async parseFiles(questionFile, answerFile) {\n    try {\n      const [questions, answers] = await Promise.all([this.parseQuestionFile(questionFile), this.parseAnswerFile(answerFile)]);\n      return {\n        questions,\n        answers\n      };\n    } catch (error) {\n      console.error('Error parsing files:', error);\n      throw error;\n    }\n  }\n}\nexport default CFAQuestionParser;", "map": {"version": 3, "names": ["pdfjsLib", "GlobalWorkerOptions", "workerSrc", "CFAQuestionParser", "constructor", "questions", "answers", "parseQuestionFile", "file", "arrayBuffer", "pdf", "getDocument", "promise", "fullText", "i", "numPages", "page", "getPage", "textContent", "getTextContent", "pageText", "lastY", "item", "items", "Math", "abs", "transform", "str", "console", "log", "length", "substring", "extractQuestions", "error", "parseAnswerFile", "extractAnswers", "Object", "keys", "text", "questionMarkers", "questionPattern", "match", "exec", "startQ", "parseInt", "endQ", "totalQ", "questionId", "push", "isGroup", "index", "fullMatch", "marker", "nextM<PERSON><PERSON>", "startIndex", "endIndex", "content", "trim", "groupQuestions", "extractGroupQuestions", "questionData", "parseSingleQuestion", "sort", "a", "b", "questionNumber", "map", "q", "extractQuestionsAlternative", "patterns", "pattern", "matches", "matchAll", "questionNum", "sharedContext", "remainingContent", "firstQuestionPatterns", "RegExp", "contextIndicators", "qNum", "questionPatterns", "questionContent", "context", "questionText", "choices", "choicePattern", "choiceMatches", "firstChoiceIndex", "for<PERSON>ach", "choice", "lines", "split", "line", "filter", "currentChoice", "choiceText", "questionLines", "foundFirstChoice", "choiceMatch", "join", "aggressivePattern", "aggressiveMatches", "key", "replace", "expectedChoices", "missingChoices", "c", "totalQuestions", "isGroupQuestion", "Boolean", "markerPatterns", "find", "m", "<PERSON><PERSON><PERSON><PERSON>", "answerPatterns", "j", "toUpperCase", "source", "aggressivePatterns", "explanation", "explanationPatterns", "explKeywords", "keyword", "explIndex", "toLowerCase", "indexOf", "afterExpl", "nextQuestionIndex", "search", "parseFiles", "questionFile", "answerFile", "Promise", "all"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/utils/pdfParser.js"], "sourcesContent": ["// PDF Parser for CFA Questions and Answers\nimport * as pdfjsLib from 'pdfjs-dist';\n\n// Set worker path - use the worker from the same version\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;\n\nclass CFAQuestionParser {\n  constructor() {\n    this.questions = [];\n    this.answers = {};\n  }\n\n  async parseQuestionFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n\n        fullText += pageText + '\\n\\n';\n      }\n\n      // Debug: Log extracted text\n      console.log('Extracted text length:', fullText.length);\n      console.log('First 2000 characters:', fullText.substring(0, 2000));\n\n      // Parse questions from text\n      const questions = this.extractQuestions(fullText);\n      console.log('Extracted questions:', questions.length);\n\n      return questions;\n    } catch (error) {\n      console.error('Error parsing question file:', error);\n      throw error;\n    }\n  }\n\n  async parseAnswerFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n\n        fullText += pageText + '\\n\\n';\n      }\n\n      console.log('Answer file text length:', fullText.length);\n      console.log('Answer file preview:', fullText.substring(0, 1000));\n\n      // Parse answers from text\n      const answers = this.extractAnswers(fullText);\n      console.log('Extracted answers:', Object.keys(answers).length);\n\n      return answers;\n    } catch (error) {\n      console.error('Error parsing answer file:', error);\n      throw error;\n    }\n  }\n\n  extractQuestions(text) {\n    const questions = [];\n\n    console.log('=== STARTING QUESTION EXTRACTION ===');\n    console.log('Text length:', text.length);\n\n    // First, let's find all question markers and their positions\n    const questionMarkers = [];\n    const questionPattern = /Question #(\\d+)(?:\\s*-\\s*(\\d+))?\\s+of\\s+(\\d+)(?:\\s+Question\\s+ID:\\s*(\\d+))?/g;\n\n    let match;\n    while ((match = questionPattern.exec(text)) !== null) {\n      const startQ = parseInt(match[1]);\n      const endQ = match[2] ? parseInt(match[2]) : null;\n      const totalQ = parseInt(match[3]);\n      const questionId = match[4] || `q${startQ}`;\n\n      questionMarkers.push({\n        startQ,\n        endQ,\n        totalQ,\n        questionId,\n        isGroup: endQ !== null,\n        index: match.index,\n        fullMatch: match[0]\n      });\n    }\n\n    console.log(`Found ${questionMarkers.length} question markers:`, questionMarkers);\n\n    // Process each marker\n    for (let i = 0; i < questionMarkers.length; i++) {\n      const marker = questionMarkers[i];\n      const nextMarker = questionMarkers[i + 1];\n\n      // Extract content from this marker to the next (or end of text)\n      const startIndex = marker.index + marker.fullMatch.length;\n      const endIndex = nextMarker ? nextMarker.index : text.length;\n      const content = text.substring(startIndex, endIndex).trim();\n\n      console.log(`\\n--- Processing marker ${i + 1}: Q${marker.startQ}${marker.endQ ? `-${marker.endQ}` : ''} ---`);\n      console.log(`Content length: ${content.length}`);\n      console.log(`Content preview: ${content.substring(0, 200)}...`);\n\n      if (marker.isGroup) {\n        // This is a group of questions with shared context\n        console.log(`Processing question group ${marker.startQ}-${marker.endQ}`);\n\n        // Extract shared context and individual questions\n        const groupQuestions = this.extractGroupQuestions(\n          content,\n          marker.startQ,\n          marker.endQ,\n          marker.totalQ,\n          marker.questionId\n        );\n\n        questions.push(...groupQuestions);\n      } else {\n        // Single question\n        console.log(`Processing single question ${marker.startQ}`);\n\n        const questionData = this.parseSingleQuestion(\n          content,\n          marker.startQ,\n          marker.totalQ,\n          marker.questionId\n        );\n\n        if (questionData) {\n          questions.push(questionData);\n        }\n      }\n    }\n\n    // Sort questions by number\n    questions.sort((a, b) => a.questionNumber - b.questionNumber);\n\n    console.log(`\\n=== EXTRACTION COMPLETE ===`);\n    console.log(`Total questions extracted: ${questions.length}`);\n    console.log('Question numbers:', questions.map(q => q.questionNumber));\n\n    return questions;\n  }\n\n  extractQuestionsAlternative(text) {\n    console.log('Using alternative extraction method...');\n    const questions = [];\n\n    // Look for any numbered patterns that might be questions\n    const patterns = [\n      /(\\d+)\\.\\s+([^0-9]+?)(?=\\d+\\.|$)/gs,  // 1. Question text\n      /Question\\s+(\\d+)[:\\.]?\\s*([^Q]+?)(?=Question\\s+\\d+|$)/gis,  // Question 1: text\n    ];\n\n    for (const pattern of patterns) {\n      const matches = [...text.matchAll(pattern)];\n      console.log(`Alternative pattern found ${matches.length} matches`);\n\n      for (const match of matches) {\n        const questionNum = parseInt(match[1]);\n        const content = match[2].trim();\n\n        if (content.length > 20) { // Filter out very short matches\n          const questionData = this.parseSingleQuestion(\n            content,\n            questionNum,\n            100, // Default total\n            'alt-' + questionNum\n          );\n\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n\n      if (questions.length > 0) break; // Use first successful pattern\n    }\n\n    return questions;\n  }\n\n  extractGroupQuestions(content, startQ, endQ, totalQ, questionId) {\n    const questions = [];\n\n    console.log(`\\n=== EXTRACTING GROUP QUESTIONS ${startQ}-${endQ} ===`);\n    console.log('Group content length:', content.length);\n    console.log('Group content preview:', content.substring(0, 300));\n\n    // Step 1: Extract shared reading context\n    // The context is everything before the first individual question\n    let sharedContext = '';\n    let remainingContent = content;\n\n    // Look for the first individual question marker (like \"6.\" or \"Question 6\")\n    const firstQuestionPatterns = [\n      new RegExp(`^(.*?)(?=\\\\b${startQ}\\\\.)`, 's'),  // Everything before \"6.\"\n      new RegExp(`^(.*?)(?=Question\\\\s+${startQ}\\\\b)`, 'is'),  // Everything before \"Question 6\"\n      new RegExp(`^(.*?)(?=\\\\b${startQ}\\\\s*\\\\))`, 's'),  // Everything before \"6)\"\n    ];\n\n    for (const pattern of firstQuestionPatterns) {\n      const match = content.match(pattern);\n      if (match && match[1].trim().length > 100) {  // Ensure substantial context\n        sharedContext = match[1].trim();\n        remainingContent = content.substring(match[1].length);\n        console.log(`Found shared context (${sharedContext.length} chars):`, sharedContext.substring(0, 150) + '...');\n        break;\n      }\n    }\n\n    // If no clear context separation, try to identify context by looking for reading material\n    if (!sharedContext && content.length > 500) {\n      // Look for typical reading indicators\n      const contextIndicators = [\n        /^(.*?(?:company|firm|portfolio|investment|analysis|data|information|table|exhibit).*?)(?=\\d+\\.)/is,\n        /^(.*?(?:following|given|based on|consider|assume).*?)(?=\\d+\\.)/is\n      ];\n\n      for (const pattern of contextIndicators) {\n        const match = content.match(pattern);\n        if (match && match[1].trim().length > 100) {\n          sharedContext = match[1].trim();\n          remainingContent = content.substring(match[1].length);\n          console.log(`Found context via indicators (${sharedContext.length} chars):`, sharedContext.substring(0, 150) + '...');\n          break;\n        }\n      }\n    }\n\n    // Step 2: Extract individual questions\n    console.log(`Looking for individual questions ${startQ} to ${endQ}`);\n\n    for (let qNum = startQ; qNum <= endQ; qNum++) {\n      console.log(`\\n--- Extracting Question ${qNum} ---`);\n\n      // Multiple patterns to find individual questions\n      const questionPatterns = [\n        new RegExp(`\\\\b${qNum}\\\\.\\\\s*(.*?)(?=\\\\b${qNum + 1}\\\\.|$)`, 's'),  // \"6. content\"\n        new RegExp(`Question\\\\s+${qNum}[:\\\\.]?\\\\s*(.*?)(?=Question\\\\s+${qNum + 1}|$)`, 'is'),  // \"Question 6: content\"\n        new RegExp(`${qNum}\\\\)\\\\s*(.*?)(?=${qNum + 1}\\\\)|$)`, 's'),  // \"6) content\"\n        new RegExp(`^.*?${qNum}[^\\\\d]*?(.*?)(?=${qNum + 1}[^\\\\d]|$)`, 's')  // Flexible pattern\n      ];\n\n      let questionContent = '';\n\n      for (let i = 0; i < questionPatterns.length; i++) {\n        const pattern = questionPatterns[i];\n        const match = remainingContent.match(pattern);\n        if (match && match[1].trim().length > 20) {\n          questionContent = match[1].trim();\n          console.log(`Found Q${qNum} using pattern ${i}: ${questionContent.substring(0, 100)}...`);\n          break;\n        }\n      }\n\n      // If still no content, try searching in the full content\n      if (!questionContent) {\n        console.log(`Trying full content search for Q${qNum}`);\n        for (let i = 0; i < questionPatterns.length; i++) {\n          const pattern = questionPatterns[i];\n          const match = content.match(pattern);\n          if (match && match[1].trim().length > 20) {\n            questionContent = match[1].trim();\n            console.log(`Found Q${qNum} in full content using pattern ${i}`);\n            break;\n          }\n        }\n      }\n\n      if (questionContent) {\n        const questionData = this.parseSingleQuestion(\n          questionContent,\n          qNum,\n          totalQ,\n          `${questionId}-${qNum}`,\n          sharedContext\n        );\n\n        if (questionData) {\n          questions.push(questionData);\n          console.log(`Successfully extracted Q${qNum}`);\n        } else {\n          console.log(`Failed to parse Q${qNum} content`);\n        }\n      } else {\n        console.log(`No content found for Q${qNum}`);\n      }\n    }\n\n    console.log(`\\n=== GROUP EXTRACTION COMPLETE ===`);\n    console.log(`Extracted ${questions.length} questions from group ${startQ}-${endQ}`);\n    console.log(`Shared context length: ${sharedContext.length}`);\n\n    return questions;\n  }\n\n  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {\n    if (!content.trim()) return null;\n\n    console.log(`\\n=== Parsing Question ${questionNum} ===`);\n    console.log(`Content length: ${content.length}`);\n    console.log(`Content preview: ${content.substring(0, 300)}...`);\n\n    let questionText = '';\n    const choices = {};\n\n    // Method 1: Look for choice patterns A) B) C) D) E)\n    const choicePattern = /([A-E])\\)\\s*([^A-E]*?)(?=\\s*[A-E]\\)|$)/g;\n    const choiceMatches = [...content.matchAll(choicePattern)];\n\n    console.log(`Found ${choiceMatches.length} choice matches`);\n\n    if (choiceMatches.length >= 3) {\n      // Extract question text (everything before first choice)\n      const firstChoiceIndex = choiceMatches[0].index;\n      questionText = content.substring(0, firstChoiceIndex).trim();\n\n      // Extract choices\n      choiceMatches.forEach(match => {\n        const choice = match[1];\n        const text = match[2].trim();\n        if (text.length > 0) {\n          choices[choice] = text;\n        }\n      });\n\n      console.log(`Method 1 success: ${Object.keys(choices).length} choices extracted`);\n    } else {\n      // Method 2: Line-by-line parsing with better logic\n      console.log('Using Method 2: Line-by-line parsing');\n\n      const lines = content.split(/\\n/).map(line => line.trim()).filter(line => line.length > 0);\n\n      let currentChoice = null;\n      let choiceText = '';\n      let questionLines = [];\n      let foundFirstChoice = false;\n\n      for (const line of lines) {\n        // Check for choice pattern at start of line\n        const choiceMatch = line.match(/^([A-E])\\)\\s*(.*)/);\n\n        if (choiceMatch) {\n          // Save previous choice if exists\n          if (currentChoice && choiceText.trim()) {\n            choices[currentChoice] = choiceText.trim();\n          }\n\n          currentChoice = choiceMatch[1];\n          choiceText = choiceMatch[2];\n          foundFirstChoice = true;\n\n        } else if (currentChoice && foundFirstChoice) {\n          // Continue current choice text\n          choiceText += ' ' + line;\n\n        } else if (!foundFirstChoice) {\n          // Still in question text area\n          questionLines.push(line);\n        }\n      }\n\n      // Save last choice\n      if (currentChoice && choiceText.trim()) {\n        choices[currentChoice] = choiceText.trim();\n      }\n\n      questionText = questionLines.join(' ').trim();\n      console.log(`Method 2 result: ${Object.keys(choices).length} choices, question length: ${questionText.length}`);\n    }\n\n    // Method 3: If still no good results, try more aggressive parsing\n    if (Object.keys(choices).length < 3 && content.length > 100) {\n      console.log('Using Method 3: Aggressive parsing');\n\n      // Look for any A) B) C) patterns anywhere in text\n      const aggressivePattern = /([A-E])\\)\\s*([^A-E\\n]{10,200}?)(?=\\s*[A-E]\\)|$)/g;\n      const aggressiveMatches = [...content.matchAll(aggressivePattern)];\n\n      if (aggressiveMatches.length >= 3) {\n        // Clear previous results\n        Object.keys(choices).forEach(key => delete choices[key]);\n\n        // Extract question text (everything before first choice)\n        const firstChoiceIndex = aggressiveMatches[0].index;\n        questionText = content.substring(0, firstChoiceIndex).trim();\n\n        // Extract choices\n        aggressiveMatches.forEach(match => {\n          const choice = match[1];\n          const text = match[2].trim();\n          choices[choice] = text;\n        });\n\n        console.log(`Method 3 success: ${Object.keys(choices).length} choices extracted`);\n      }\n    }\n\n    // Clean up and validate\n    questionText = questionText.replace(/\\s+/g, ' ').trim();\n    Object.keys(choices).forEach(key => {\n      choices[key] = choices[key].replace(/\\s+/g, ' ').trim();\n      // Remove empty choices\n      if (!choices[key]) {\n        delete choices[key];\n      }\n    });\n\n    console.log(`Final result for Question ${questionNum}:`);\n    console.log(`- Question text: ${questionText.length} chars`);\n    console.log(`- Choices: ${Object.keys(choices).join(', ')}`);\n    console.log(`- Context: ${context ? 'Yes' : 'No'}`);\n\n    // Validation and fallbacks\n    if (!questionText && Object.keys(choices).length === 0) {\n      console.log(`Question ${questionNum}: Complete parsing failure`);\n      return null;\n    }\n\n    // Ensure minimum choices\n    const expectedChoices = ['A', 'B', 'C', 'D', 'E'];\n    const missingChoices = expectedChoices.filter(c => !choices[c]);\n\n    if (missingChoices.length > 2) {\n      console.log(`Question ${questionNum}: Missing too many choices, adding placeholders`);\n      missingChoices.forEach(choice => {\n        choices[choice] = `Choice ${choice} (not found in PDF)`;\n      });\n    }\n\n    // Ensure question text\n    if (!questionText.trim()) {\n      questionText = `Question ${questionNum} content (parsing incomplete - check PDF)`;\n    }\n\n    return {\n      questionNumber: questionNum,\n      totalQuestions: totalQ,\n      questionId: questionId,\n      context: context,\n      questionText: questionText,\n      choices: choices,\n      isGroupQuestion: Boolean(context)\n    };\n  }\n\n  extractAnswers(text) {\n    const answers = {};\n\n    console.log('\\n=== STARTING ANSWER EXTRACTION ===');\n    console.log('Answer file text length:', text.length);\n    console.log('Answer file preview:', text.substring(0, 500));\n\n    // Step 1: Find all question markers in answer file\n    const questionMarkers = [];\n\n    // More comprehensive patterns for answer file\n    const markerPatterns = [\n      /Question #(\\d+) of (\\d+) Question ID: (\\d+)/g,\n      /Question #(\\d+) of (\\d+)/g,\n      /Question #(\\d+)/g,\n      /Question\\s+(\\d+)/g\n    ];\n\n    for (const pattern of markerPatterns) {\n      let match;\n      while ((match = pattern.exec(text)) !== null) {\n        const questionNum = parseInt(match[1]);\n        if (!questionMarkers.find(m => m.questionNum === questionNum)) {\n          questionMarkers.push({\n            index: match.index,\n            fullMatch: match[0],\n            questionNum: questionNum\n          });\n        }\n      }\n    }\n\n    console.log(`Found ${questionMarkers.length} question markers in answer file:`,\n                questionMarkers.map(m => m.questionNum).sort((a, b) => a - b));\n\n    // Sort by position in text\n    questionMarkers.sort((a, b) => a.index - b.index);\n\n    // Step 2: Extract answer content for each question\n    for (let i = 0; i < questionMarkers.length; i++) {\n      const marker = questionMarkers[i];\n      const nextMarker = questionMarkers[i + 1];\n\n      // Get content from current marker to next marker (or end of text)\n      const startIndex = marker.index + marker.fullMatch.length;\n      const endIndex = nextMarker ? nextMarker.index : text.length;\n      const content = text.substring(startIndex, endIndex).trim();\n\n      console.log(`\\n--- Processing Answer for Question ${marker.questionNum} ---`);\n      console.log(`Content length: ${content.length}`);\n      console.log(`Content preview: ${content.substring(0, 300)}...`);\n\n      // Step 3: Find correct answer with multiple patterns\n      let correctAnswer = null;\n      const answerPatterns = [\n        /The correct answer is\\s*([A-E])\\.?/i,\n        /The correct answer is\\s*([A-E])\\s/i,\n        /Correct answer:\\s*([A-E])\\.?/i,\n        /Correct answer\\s*is\\s*([A-E])\\.?/i,\n        /Answer:\\s*([A-E])\\.?/i,\n        /Answer\\s*is\\s*([A-E])\\.?/i,\n        /([A-E])\\s*is\\s*correct/i,\n        /([A-E])\\s*is\\s*the\\s*correct/i,\n        /Choice\\s*([A-E])\\s*is\\s*correct/i,\n        /Option\\s*([A-E])\\s*is\\s*correct/i,\n        /\\b([A-E])\\s*\\.\\s*$/m,  // Single letter at end of line\n        /^([A-E])$/m  // Single letter on its own line\n      ];\n\n      for (let j = 0; j < answerPatterns.length; j++) {\n        const pattern = answerPatterns[j];\n        const match = content.match(pattern);\n        if (match) {\n          correctAnswer = match[1].toUpperCase();\n          console.log(`Found correct answer \"${correctAnswer}\" for Q${marker.questionNum} using pattern ${j}: ${pattern.source}`);\n          break;\n        }\n      }\n\n      // If still no answer, try more aggressive search\n      if (!correctAnswer) {\n        console.log(`No answer found with standard patterns, trying aggressive search for Q${marker.questionNum}`);\n\n        // Look for any single letter A-E that appears prominently\n        const aggressivePatterns = [\n          /\\b([A-E])\\b/g  // Any single letter A-E\n        ];\n\n        for (const pattern of aggressivePatterns) {\n          const matches = [...content.matchAll(pattern)];\n          if (matches.length > 0) {\n            // Take the first occurrence\n            correctAnswer = matches[0][1].toUpperCase();\n            console.log(`Found answer \"${correctAnswer}\" for Q${marker.questionNum} via aggressive search`);\n            break;\n          }\n        }\n      }\n\n      // Step 4: Extract explanation\n      let explanation = '';\n\n      // Multiple patterns to find explanation\n      const explanationPatterns = [\n        /Explanation[:\\s]*\\n?(.*?)(?=Question #|$)/is,\n        /Explanation[:\\s]+(.*?)(?=\\(Module|$)/is,\n        /Explanation[:\\s]+(.*?)(?=\\n\\s*\\n|$)/is,\n        /Explanation[:\\s]+(.*)/is\n      ];\n\n      for (let j = 0; j < explanationPatterns.length; j++) {\n        const pattern = explanationPatterns[j];\n        const match = content.match(pattern);\n        if (match && match[1].trim().length > 10) {\n          explanation = match[1].trim();\n          console.log(`Found explanation for Q${marker.questionNum} using pattern ${j}`);\n          break;\n        }\n      }\n\n      // Manual explanation extraction if patterns fail\n      if (!explanation) {\n        const explKeywords = ['explanation', 'Explanation', 'EXPLANATION'];\n        for (const keyword of explKeywords) {\n          const explIndex = content.toLowerCase().indexOf(keyword.toLowerCase());\n          if (explIndex !== -1) {\n            let afterExpl = content.substring(explIndex + keyword.length).trim();\n            afterExpl = afterExpl.replace(/^[:\\s\\n]+/, '');\n\n            const nextQuestionIndex = afterExpl.search(/Question #\\d+/i);\n            if (nextQuestionIndex !== -1) {\n              afterExpl = afterExpl.substring(0, nextQuestionIndex);\n            }\n\n            if (afterExpl.length > 20) {\n              explanation = afterExpl.trim();\n              console.log(`Manual explanation extraction for Q${marker.questionNum}`);\n              break;\n            }\n          }\n        }\n      }\n\n      // Clean up explanation\n      if (explanation) {\n        explanation = explanation.replace(/\\s+/g, ' ');\n        explanation = explanation.replace(/^\\s*[:\\-\\s]+/, '');\n        explanation = explanation.replace(/\\(Module.*?\\)$/i, '');\n        explanation = explanation.replace(/\\(LOS.*?\\)$/i, '');\n        explanation = explanation.replace(/\\(Reading.*?\\)$/i, '');\n        explanation = explanation.replace(/\\s+\\d+\\s*$/, '');\n        explanation = explanation.trim();\n      }\n\n      // Step 5: Store the answer data\n      if (correctAnswer) {\n        answers[marker.questionNum] = {\n          correctAnswer: correctAnswer,\n          explanation: explanation || 'No explanation available'\n        };\n        console.log(`✓ Successfully extracted answer for Q${marker.questionNum}: ${correctAnswer}`);\n      } else {\n        console.log(`✗ Failed to find correct answer for Q${marker.questionNum}`);\n        console.log(`Raw content for debugging: ${content.substring(0, 200)}...`);\n      }\n    }\n\n    console.log(`\\n=== ANSWER EXTRACTION COMPLETE ===`);\n    console.log(`Successfully extracted answers for ${Object.keys(answers).length} questions`);\n    console.log(`Answer summary:`, Object.keys(answers).sort((a, b) => a - b).map(q => `Q${q}:${answers[q].correctAnswer}`));\n\n    return answers;\n  }\n\n  async parseFiles(questionFile, answerFile) {\n    try {\n      const [questions, answers] = await Promise.all([\n        this.parseQuestionFile(questionFile),\n        this.parseAnswerFile(answerFile)\n      ]);\n      \n      return { questions, answers };\n    } catch (error) {\n      console.error('Error parsing files:', error);\n      throw error;\n    }\n  }\n}\n\nexport default CFAQuestionParser;\n"], "mappings": "AAAA;AACA,OAAO,KAAKA,QAAQ,MAAM,YAAY;;AAEtC;AACAA,QAAQ,CAACC,mBAAmB,CAACC,SAAS,GAAG,+DAA+D;AAExG,MAAMC,iBAAiB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;EACnB;EAEA,MAAMC,iBAAiBA,CAACC,IAAI,EAAE;IAC5B,IAAI;MACF,MAAMC,WAAW,GAAG,MAAMD,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5C,MAAMC,GAAG,GAAG,MAAMV,QAAQ,CAACW,WAAW,CAACF,WAAW,CAAC,CAACG,OAAO;MAE3D,IAAIC,QAAQ,GAAG,EAAE;;MAEjB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,GAAG,CAACK,QAAQ,EAAED,CAAC,EAAE,EAAE;QACtC,MAAME,IAAI,GAAG,MAAMN,GAAG,CAACO,OAAO,CAACH,CAAC,CAAC;QACjC,MAAMI,WAAW,GAAG,MAAMF,IAAI,CAACG,cAAc,CAAC,CAAC;;QAE/C;QACA,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAIC,KAAK,GAAG,IAAI;QAEhB,KAAK,MAAMC,IAAI,IAAIJ,WAAW,CAACK,KAAK,EAAE;UACpC;UACA,IAAIF,KAAK,KAAK,IAAI,IAAIG,IAAI,CAACC,GAAG,CAACH,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,GAAGL,KAAK,CAAC,GAAG,CAAC,EAAE;YAC7DD,QAAQ,IAAI,IAAI;UAClB;UAEAA,QAAQ,IAAIE,IAAI,CAACK,GAAG,GAAG,GAAG;UAC1BN,KAAK,GAAGC,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC;QAC3B;QAEAb,QAAQ,IAAIO,QAAQ,GAAG,MAAM;MAC/B;;MAEA;MACAQ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEhB,QAAQ,CAACiB,MAAM,CAAC;MACtDF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEhB,QAAQ,CAACkB,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;;MAElE;MACA,MAAM1B,SAAS,GAAG,IAAI,CAAC2B,gBAAgB,CAACnB,QAAQ,CAAC;MACjDe,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAExB,SAAS,CAACyB,MAAM,CAAC;MAErD,OAAOzB,SAAS;IAClB,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;EAEA,MAAMC,eAAeA,CAAC1B,IAAI,EAAE;IAC1B,IAAI;MACF,MAAMC,WAAW,GAAG,MAAMD,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5C,MAAMC,GAAG,GAAG,MAAMV,QAAQ,CAACW,WAAW,CAACF,WAAW,CAAC,CAACG,OAAO;MAE3D,IAAIC,QAAQ,GAAG,EAAE;;MAEjB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,GAAG,CAACK,QAAQ,EAAED,CAAC,EAAE,EAAE;QACtC,MAAME,IAAI,GAAG,MAAMN,GAAG,CAACO,OAAO,CAACH,CAAC,CAAC;QACjC,MAAMI,WAAW,GAAG,MAAMF,IAAI,CAACG,cAAc,CAAC,CAAC;;QAE/C;QACA,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAIC,KAAK,GAAG,IAAI;QAEhB,KAAK,MAAMC,IAAI,IAAIJ,WAAW,CAACK,KAAK,EAAE;UACpC;UACA,IAAIF,KAAK,KAAK,IAAI,IAAIG,IAAI,CAACC,GAAG,CAACH,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,GAAGL,KAAK,CAAC,GAAG,CAAC,EAAE;YAC7DD,QAAQ,IAAI,IAAI;UAClB;UAEAA,QAAQ,IAAIE,IAAI,CAACK,GAAG,GAAG,GAAG;UAC1BN,KAAK,GAAGC,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC;QAC3B;QAEAb,QAAQ,IAAIO,QAAQ,GAAG,MAAM;MAC/B;MAEAQ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEhB,QAAQ,CAACiB,MAAM,CAAC;MACxDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEhB,QAAQ,CAACkB,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;;MAEhE;MACA,MAAMzB,OAAO,GAAG,IAAI,CAAC6B,cAAc,CAACtB,QAAQ,CAAC;MAC7Ce,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,MAAM,CAACC,IAAI,CAAC/B,OAAO,CAAC,CAACwB,MAAM,CAAC;MAE9D,OAAOxB,OAAO;IAChB,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF;EAEAD,gBAAgBA,CAACM,IAAI,EAAE;IACrB,MAAMjC,SAAS,GAAG,EAAE;IAEpBuB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnDD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAES,IAAI,CAACR,MAAM,CAAC;;IAExC;IACA,MAAMS,eAAe,GAAG,EAAE;IAC1B,MAAMC,eAAe,GAAG,8EAA8E;IAEtG,IAAIC,KAAK;IACT,OAAO,CAACA,KAAK,GAAGD,eAAe,CAACE,IAAI,CAACJ,IAAI,CAAC,MAAM,IAAI,EAAE;MACpD,MAAMK,MAAM,GAAGC,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;MACjC,MAAMI,IAAI,GAAGJ,KAAK,CAAC,CAAC,CAAC,GAAGG,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;MACjD,MAAMK,MAAM,GAAGF,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;MACjC,MAAMM,UAAU,GAAGN,KAAK,CAAC,CAAC,CAAC,IAAI,IAAIE,MAAM,EAAE;MAE3CJ,eAAe,CAACS,IAAI,CAAC;QACnBL,MAAM;QACNE,IAAI;QACJC,MAAM;QACNC,UAAU;QACVE,OAAO,EAAEJ,IAAI,KAAK,IAAI;QACtBK,KAAK,EAAET,KAAK,CAACS,KAAK;QAClBC,SAAS,EAAEV,KAAK,CAAC,CAAC;MACpB,CAAC,CAAC;IACJ;IAEAb,OAAO,CAACC,GAAG,CAAC,SAASU,eAAe,CAACT,MAAM,oBAAoB,EAAES,eAAe,CAAC;;IAEjF;IACA,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,eAAe,CAACT,MAAM,EAAEhB,CAAC,EAAE,EAAE;MAC/C,MAAMsC,MAAM,GAAGb,eAAe,CAACzB,CAAC,CAAC;MACjC,MAAMuC,UAAU,GAAGd,eAAe,CAACzB,CAAC,GAAG,CAAC,CAAC;;MAEzC;MACA,MAAMwC,UAAU,GAAGF,MAAM,CAACF,KAAK,GAAGE,MAAM,CAACD,SAAS,CAACrB,MAAM;MACzD,MAAMyB,QAAQ,GAAGF,UAAU,GAAGA,UAAU,CAACH,KAAK,GAAGZ,IAAI,CAACR,MAAM;MAC5D,MAAM0B,OAAO,GAAGlB,IAAI,CAACP,SAAS,CAACuB,UAAU,EAAEC,QAAQ,CAAC,CAACE,IAAI,CAAC,CAAC;MAE3D7B,OAAO,CAACC,GAAG,CAAC,2BAA2Bf,CAAC,GAAG,CAAC,MAAMsC,MAAM,CAACT,MAAM,GAAGS,MAAM,CAACP,IAAI,GAAG,IAAIO,MAAM,CAACP,IAAI,EAAE,GAAG,EAAE,MAAM,CAAC;MAC7GjB,OAAO,CAACC,GAAG,CAAC,mBAAmB2B,OAAO,CAAC1B,MAAM,EAAE,CAAC;MAChDF,OAAO,CAACC,GAAG,CAAC,oBAAoB2B,OAAO,CAACzB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;MAE/D,IAAIqB,MAAM,CAACH,OAAO,EAAE;QAClB;QACArB,OAAO,CAACC,GAAG,CAAC,6BAA6BuB,MAAM,CAACT,MAAM,IAAIS,MAAM,CAACP,IAAI,EAAE,CAAC;;QAExE;QACA,MAAMa,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAC/CH,OAAO,EACPJ,MAAM,CAACT,MAAM,EACbS,MAAM,CAACP,IAAI,EACXO,MAAM,CAACN,MAAM,EACbM,MAAM,CAACL,UACT,CAAC;QAED1C,SAAS,CAAC2C,IAAI,CAAC,GAAGU,cAAc,CAAC;MACnC,CAAC,MAAM;QACL;QACA9B,OAAO,CAACC,GAAG,CAAC,8BAA8BuB,MAAM,CAACT,MAAM,EAAE,CAAC;QAE1D,MAAMiB,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CL,OAAO,EACPJ,MAAM,CAACT,MAAM,EACbS,MAAM,CAACN,MAAM,EACbM,MAAM,CAACL,UACT,CAAC;QAED,IAAIa,YAAY,EAAE;UAChBvD,SAAS,CAAC2C,IAAI,CAACY,YAAY,CAAC;QAC9B;MACF;IACF;;IAEA;IACAvD,SAAS,CAACyD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,cAAc,GAAGD,CAAC,CAACC,cAAc,CAAC;IAE7DrC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5CD,OAAO,CAACC,GAAG,CAAC,8BAA8BxB,SAAS,CAACyB,MAAM,EAAE,CAAC;IAC7DF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAExB,SAAS,CAAC6D,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACF,cAAc,CAAC,CAAC;IAEtE,OAAO5D,SAAS;EAClB;EAEA+D,2BAA2BA,CAAC9B,IAAI,EAAE;IAChCV,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACrD,MAAMxB,SAAS,GAAG,EAAE;;IAEpB;IACA,MAAMgE,QAAQ,GAAG,CACf,mCAAmC;IAAG;IACtC,0DAA0D,CAAG;IAAA,CAC9D;IAED,KAAK,MAAMC,OAAO,IAAID,QAAQ,EAAE;MAC9B,MAAME,OAAO,GAAG,CAAC,GAAGjC,IAAI,CAACkC,QAAQ,CAACF,OAAO,CAAC,CAAC;MAC3C1C,OAAO,CAACC,GAAG,CAAC,6BAA6B0C,OAAO,CAACzC,MAAM,UAAU,CAAC;MAElE,KAAK,MAAMW,KAAK,IAAI8B,OAAO,EAAE;QAC3B,MAAME,WAAW,GAAG7B,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;QACtC,MAAMe,OAAO,GAAGf,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC;QAE/B,IAAID,OAAO,CAAC1B,MAAM,GAAG,EAAE,EAAE;UAAE;UACzB,MAAM8B,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CL,OAAO,EACPiB,WAAW,EACX,GAAG;UAAE;UACL,MAAM,GAAGA,WACX,CAAC;UAED,IAAIb,YAAY,EAAE;YAChBvD,SAAS,CAAC2C,IAAI,CAACY,YAAY,CAAC;UAC9B;QACF;MACF;MAEA,IAAIvD,SAAS,CAACyB,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC;IACnC;IAEA,OAAOzB,SAAS;EAClB;EAEAsD,qBAAqBA,CAACH,OAAO,EAAEb,MAAM,EAAEE,IAAI,EAAEC,MAAM,EAAEC,UAAU,EAAE;IAC/D,MAAM1C,SAAS,GAAG,EAAE;IAEpBuB,OAAO,CAACC,GAAG,CAAC,oCAAoCc,MAAM,IAAIE,IAAI,MAAM,CAAC;IACrEjB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE2B,OAAO,CAAC1B,MAAM,CAAC;IACpDF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE2B,OAAO,CAACzB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;;IAEhE;IACA;IACA,IAAI2C,aAAa,GAAG,EAAE;IACtB,IAAIC,gBAAgB,GAAGnB,OAAO;;IAE9B;IACA,MAAMoB,qBAAqB,GAAG,CAC5B,IAAIC,MAAM,CAAC,eAAelC,MAAM,MAAM,EAAE,GAAG,CAAC;IAAG;IAC/C,IAAIkC,MAAM,CAAC,wBAAwBlC,MAAM,MAAM,EAAE,IAAI,CAAC;IAAG;IACzD,IAAIkC,MAAM,CAAC,eAAelC,MAAM,UAAU,EAAE,GAAG,CAAC,CAAG;IAAA,CACpD;IAED,KAAK,MAAM2B,OAAO,IAAIM,qBAAqB,EAAE;MAC3C,MAAMnC,KAAK,GAAGe,OAAO,CAACf,KAAK,CAAC6B,OAAO,CAAC;MACpC,IAAI7B,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC,CAAC3B,MAAM,GAAG,GAAG,EAAE;QAAG;QAC5C4C,aAAa,GAAGjC,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC;QAC/BkB,gBAAgB,GAAGnB,OAAO,CAACzB,SAAS,CAACU,KAAK,CAAC,CAAC,CAAC,CAACX,MAAM,CAAC;QACrDF,OAAO,CAACC,GAAG,CAAC,yBAAyB6C,aAAa,CAAC5C,MAAM,UAAU,EAAE4C,aAAa,CAAC3C,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAC7G;MACF;IACF;;IAEA;IACA,IAAI,CAAC2C,aAAa,IAAIlB,OAAO,CAAC1B,MAAM,GAAG,GAAG,EAAE;MAC1C;MACA,MAAMgD,iBAAiB,GAAG,CACxB,mGAAmG,EACnG,kEAAkE,CACnE;MAED,KAAK,MAAMR,OAAO,IAAIQ,iBAAiB,EAAE;QACvC,MAAMrC,KAAK,GAAGe,OAAO,CAACf,KAAK,CAAC6B,OAAO,CAAC;QACpC,IAAI7B,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC,CAAC3B,MAAM,GAAG,GAAG,EAAE;UACzC4C,aAAa,GAAGjC,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC;UAC/BkB,gBAAgB,GAAGnB,OAAO,CAACzB,SAAS,CAACU,KAAK,CAAC,CAAC,CAAC,CAACX,MAAM,CAAC;UACrDF,OAAO,CAACC,GAAG,CAAC,iCAAiC6C,aAAa,CAAC5C,MAAM,UAAU,EAAE4C,aAAa,CAAC3C,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;UACrH;QACF;MACF;IACF;;IAEA;IACAH,OAAO,CAACC,GAAG,CAAC,oCAAoCc,MAAM,OAAOE,IAAI,EAAE,CAAC;IAEpE,KAAK,IAAIkC,IAAI,GAAGpC,MAAM,EAAEoC,IAAI,IAAIlC,IAAI,EAAEkC,IAAI,EAAE,EAAE;MAC5CnD,OAAO,CAACC,GAAG,CAAC,6BAA6BkD,IAAI,MAAM,CAAC;;MAEpD;MACA,MAAMC,gBAAgB,GAAG,CACvB,IAAIH,MAAM,CAAC,MAAME,IAAI,qBAAqBA,IAAI,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;MAAG;MACnE,IAAIF,MAAM,CAAC,eAAeE,IAAI,kCAAkCA,IAAI,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC;MAAG;MACvF,IAAIF,MAAM,CAAC,GAAGE,IAAI,kBAAkBA,IAAI,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;MAAG;MAC7D,IAAIF,MAAM,CAAC,OAAOE,IAAI,mBAAmBA,IAAI,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,CAAE;MAAA,CACrE;MAED,IAAIE,eAAe,GAAG,EAAE;MAExB,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkE,gBAAgB,CAAClD,MAAM,EAAEhB,CAAC,EAAE,EAAE;QAChD,MAAMwD,OAAO,GAAGU,gBAAgB,CAAClE,CAAC,CAAC;QACnC,MAAM2B,KAAK,GAAGkC,gBAAgB,CAAClC,KAAK,CAAC6B,OAAO,CAAC;QAC7C,IAAI7B,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC,CAAC3B,MAAM,GAAG,EAAE,EAAE;UACxCmD,eAAe,GAAGxC,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC;UACjC7B,OAAO,CAACC,GAAG,CAAC,UAAUkD,IAAI,kBAAkBjE,CAAC,KAAKmE,eAAe,CAAClD,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;UACzF;QACF;MACF;;MAEA;MACA,IAAI,CAACkD,eAAe,EAAE;QACpBrD,OAAO,CAACC,GAAG,CAAC,mCAAmCkD,IAAI,EAAE,CAAC;QACtD,KAAK,IAAIjE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkE,gBAAgB,CAAClD,MAAM,EAAEhB,CAAC,EAAE,EAAE;UAChD,MAAMwD,OAAO,GAAGU,gBAAgB,CAAClE,CAAC,CAAC;UACnC,MAAM2B,KAAK,GAAGe,OAAO,CAACf,KAAK,CAAC6B,OAAO,CAAC;UACpC,IAAI7B,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC,CAAC3B,MAAM,GAAG,EAAE,EAAE;YACxCmD,eAAe,GAAGxC,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC;YACjC7B,OAAO,CAACC,GAAG,CAAC,UAAUkD,IAAI,kCAAkCjE,CAAC,EAAE,CAAC;YAChE;UACF;QACF;MACF;MAEA,IAAImE,eAAe,EAAE;QACnB,MAAMrB,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CoB,eAAe,EACfF,IAAI,EACJjC,MAAM,EACN,GAAGC,UAAU,IAAIgC,IAAI,EAAE,EACvBL,aACF,CAAC;QAED,IAAId,YAAY,EAAE;UAChBvD,SAAS,CAAC2C,IAAI,CAACY,YAAY,CAAC;UAC5BhC,OAAO,CAACC,GAAG,CAAC,2BAA2BkD,IAAI,EAAE,CAAC;QAChD,CAAC,MAAM;UACLnD,OAAO,CAACC,GAAG,CAAC,oBAAoBkD,IAAI,UAAU,CAAC;QACjD;MACF,CAAC,MAAM;QACLnD,OAAO,CAACC,GAAG,CAAC,yBAAyBkD,IAAI,EAAE,CAAC;MAC9C;IACF;IAEAnD,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClDD,OAAO,CAACC,GAAG,CAAC,aAAaxB,SAAS,CAACyB,MAAM,yBAAyBa,MAAM,IAAIE,IAAI,EAAE,CAAC;IACnFjB,OAAO,CAACC,GAAG,CAAC,0BAA0B6C,aAAa,CAAC5C,MAAM,EAAE,CAAC;IAE7D,OAAOzB,SAAS;EAClB;EAEAwD,mBAAmBA,CAACL,OAAO,EAAEiB,WAAW,EAAE3B,MAAM,EAAEC,UAAU,EAAEmC,OAAO,GAAG,EAAE,EAAE;IAC1E,IAAI,CAAC1B,OAAO,CAACC,IAAI,CAAC,CAAC,EAAE,OAAO,IAAI;IAEhC7B,OAAO,CAACC,GAAG,CAAC,0BAA0B4C,WAAW,MAAM,CAAC;IACxD7C,OAAO,CAACC,GAAG,CAAC,mBAAmB2B,OAAO,CAAC1B,MAAM,EAAE,CAAC;IAChDF,OAAO,CAACC,GAAG,CAAC,oBAAoB2B,OAAO,CAACzB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;IAE/D,IAAIoD,YAAY,GAAG,EAAE;IACrB,MAAMC,OAAO,GAAG,CAAC,CAAC;;IAElB;IACA,MAAMC,aAAa,GAAG,yCAAyC;IAC/D,MAAMC,aAAa,GAAG,CAAC,GAAG9B,OAAO,CAACgB,QAAQ,CAACa,aAAa,CAAC,CAAC;IAE1DzD,OAAO,CAACC,GAAG,CAAC,SAASyD,aAAa,CAACxD,MAAM,iBAAiB,CAAC;IAE3D,IAAIwD,aAAa,CAACxD,MAAM,IAAI,CAAC,EAAE;MAC7B;MACA,MAAMyD,gBAAgB,GAAGD,aAAa,CAAC,CAAC,CAAC,CAACpC,KAAK;MAC/CiC,YAAY,GAAG3B,OAAO,CAACzB,SAAS,CAAC,CAAC,EAAEwD,gBAAgB,CAAC,CAAC9B,IAAI,CAAC,CAAC;;MAE5D;MACA6B,aAAa,CAACE,OAAO,CAAC/C,KAAK,IAAI;QAC7B,MAAMgD,MAAM,GAAGhD,KAAK,CAAC,CAAC,CAAC;QACvB,MAAMH,IAAI,GAAGG,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC;QAC5B,IAAInB,IAAI,CAACR,MAAM,GAAG,CAAC,EAAE;UACnBsD,OAAO,CAACK,MAAM,CAAC,GAAGnD,IAAI;QACxB;MACF,CAAC,CAAC;MAEFV,OAAO,CAACC,GAAG,CAAC,qBAAqBO,MAAM,CAACC,IAAI,CAAC+C,OAAO,CAAC,CAACtD,MAAM,oBAAoB,CAAC;IACnF,CAAC,MAAM;MACL;MACAF,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MAEnD,MAAM6D,KAAK,GAAGlC,OAAO,CAACmC,KAAK,CAAC,IAAI,CAAC,CAACzB,GAAG,CAAC0B,IAAI,IAAIA,IAAI,CAACnC,IAAI,CAAC,CAAC,CAAC,CAACoC,MAAM,CAACD,IAAI,IAAIA,IAAI,CAAC9D,MAAM,GAAG,CAAC,CAAC;MAE1F,IAAIgE,aAAa,GAAG,IAAI;MACxB,IAAIC,UAAU,GAAG,EAAE;MACnB,IAAIC,aAAa,GAAG,EAAE;MACtB,IAAIC,gBAAgB,GAAG,KAAK;MAE5B,KAAK,MAAML,IAAI,IAAIF,KAAK,EAAE;QACxB;QACA,MAAMQ,WAAW,GAAGN,IAAI,CAACnD,KAAK,CAAC,mBAAmB,CAAC;QAEnD,IAAIyD,WAAW,EAAE;UACf;UACA,IAAIJ,aAAa,IAAIC,UAAU,CAACtC,IAAI,CAAC,CAAC,EAAE;YACtC2B,OAAO,CAACU,aAAa,CAAC,GAAGC,UAAU,CAACtC,IAAI,CAAC,CAAC;UAC5C;UAEAqC,aAAa,GAAGI,WAAW,CAAC,CAAC,CAAC;UAC9BH,UAAU,GAAGG,WAAW,CAAC,CAAC,CAAC;UAC3BD,gBAAgB,GAAG,IAAI;QAEzB,CAAC,MAAM,IAAIH,aAAa,IAAIG,gBAAgB,EAAE;UAC5C;UACAF,UAAU,IAAI,GAAG,GAAGH,IAAI;QAE1B,CAAC,MAAM,IAAI,CAACK,gBAAgB,EAAE;UAC5B;UACAD,aAAa,CAAChD,IAAI,CAAC4C,IAAI,CAAC;QAC1B;MACF;;MAEA;MACA,IAAIE,aAAa,IAAIC,UAAU,CAACtC,IAAI,CAAC,CAAC,EAAE;QACtC2B,OAAO,CAACU,aAAa,CAAC,GAAGC,UAAU,CAACtC,IAAI,CAAC,CAAC;MAC5C;MAEA0B,YAAY,GAAGa,aAAa,CAACG,IAAI,CAAC,GAAG,CAAC,CAAC1C,IAAI,CAAC,CAAC;MAC7C7B,OAAO,CAACC,GAAG,CAAC,oBAAoBO,MAAM,CAACC,IAAI,CAAC+C,OAAO,CAAC,CAACtD,MAAM,8BAA8BqD,YAAY,CAACrD,MAAM,EAAE,CAAC;IACjH;;IAEA;IACA,IAAIM,MAAM,CAACC,IAAI,CAAC+C,OAAO,CAAC,CAACtD,MAAM,GAAG,CAAC,IAAI0B,OAAO,CAAC1B,MAAM,GAAG,GAAG,EAAE;MAC3DF,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;;MAEjD;MACA,MAAMuE,iBAAiB,GAAG,kDAAkD;MAC5E,MAAMC,iBAAiB,GAAG,CAAC,GAAG7C,OAAO,CAACgB,QAAQ,CAAC4B,iBAAiB,CAAC,CAAC;MAElE,IAAIC,iBAAiB,CAACvE,MAAM,IAAI,CAAC,EAAE;QACjC;QACAM,MAAM,CAACC,IAAI,CAAC+C,OAAO,CAAC,CAACI,OAAO,CAACc,GAAG,IAAI,OAAOlB,OAAO,CAACkB,GAAG,CAAC,CAAC;;QAExD;QACA,MAAMf,gBAAgB,GAAGc,iBAAiB,CAAC,CAAC,CAAC,CAACnD,KAAK;QACnDiC,YAAY,GAAG3B,OAAO,CAACzB,SAAS,CAAC,CAAC,EAAEwD,gBAAgB,CAAC,CAAC9B,IAAI,CAAC,CAAC;;QAE5D;QACA4C,iBAAiB,CAACb,OAAO,CAAC/C,KAAK,IAAI;UACjC,MAAMgD,MAAM,GAAGhD,KAAK,CAAC,CAAC,CAAC;UACvB,MAAMH,IAAI,GAAGG,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC;UAC5B2B,OAAO,CAACK,MAAM,CAAC,GAAGnD,IAAI;QACxB,CAAC,CAAC;QAEFV,OAAO,CAACC,GAAG,CAAC,qBAAqBO,MAAM,CAACC,IAAI,CAAC+C,OAAO,CAAC,CAACtD,MAAM,oBAAoB,CAAC;MACnF;IACF;;IAEA;IACAqD,YAAY,GAAGA,YAAY,CAACoB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC9C,IAAI,CAAC,CAAC;IACvDrB,MAAM,CAACC,IAAI,CAAC+C,OAAO,CAAC,CAACI,OAAO,CAACc,GAAG,IAAI;MAClClB,OAAO,CAACkB,GAAG,CAAC,GAAGlB,OAAO,CAACkB,GAAG,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC9C,IAAI,CAAC,CAAC;MACvD;MACA,IAAI,CAAC2B,OAAO,CAACkB,GAAG,CAAC,EAAE;QACjB,OAAOlB,OAAO,CAACkB,GAAG,CAAC;MACrB;IACF,CAAC,CAAC;IAEF1E,OAAO,CAACC,GAAG,CAAC,6BAA6B4C,WAAW,GAAG,CAAC;IACxD7C,OAAO,CAACC,GAAG,CAAC,oBAAoBsD,YAAY,CAACrD,MAAM,QAAQ,CAAC;IAC5DF,OAAO,CAACC,GAAG,CAAC,cAAcO,MAAM,CAACC,IAAI,CAAC+C,OAAO,CAAC,CAACe,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC5DvE,OAAO,CAACC,GAAG,CAAC,cAAcqD,OAAO,GAAG,KAAK,GAAG,IAAI,EAAE,CAAC;;IAEnD;IACA,IAAI,CAACC,YAAY,IAAI/C,MAAM,CAACC,IAAI,CAAC+C,OAAO,CAAC,CAACtD,MAAM,KAAK,CAAC,EAAE;MACtDF,OAAO,CAACC,GAAG,CAAC,YAAY4C,WAAW,4BAA4B,CAAC;MAChE,OAAO,IAAI;IACb;;IAEA;IACA,MAAM+B,eAAe,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACjD,MAAMC,cAAc,GAAGD,eAAe,CAACX,MAAM,CAACa,CAAC,IAAI,CAACtB,OAAO,CAACsB,CAAC,CAAC,CAAC;IAE/D,IAAID,cAAc,CAAC3E,MAAM,GAAG,CAAC,EAAE;MAC7BF,OAAO,CAACC,GAAG,CAAC,YAAY4C,WAAW,iDAAiD,CAAC;MACrFgC,cAAc,CAACjB,OAAO,CAACC,MAAM,IAAI;QAC/BL,OAAO,CAACK,MAAM,CAAC,GAAG,UAAUA,MAAM,qBAAqB;MACzD,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAACN,YAAY,CAAC1B,IAAI,CAAC,CAAC,EAAE;MACxB0B,YAAY,GAAG,YAAYV,WAAW,2CAA2C;IACnF;IAEA,OAAO;MACLR,cAAc,EAAEQ,WAAW;MAC3BkC,cAAc,EAAE7D,MAAM;MACtBC,UAAU,EAAEA,UAAU;MACtBmC,OAAO,EAAEA,OAAO;MAChBC,YAAY,EAAEA,YAAY;MAC1BC,OAAO,EAAEA,OAAO;MAChBwB,eAAe,EAAEC,OAAO,CAAC3B,OAAO;IAClC,CAAC;EACH;EAEA/C,cAAcA,CAACG,IAAI,EAAE;IACnB,MAAMhC,OAAO,GAAG,CAAC,CAAC;IAElBsB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnDD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAES,IAAI,CAACR,MAAM,CAAC;IACpDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAES,IAAI,CAACP,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;;IAE3D;IACA,MAAMQ,eAAe,GAAG,EAAE;;IAE1B;IACA,MAAMuE,cAAc,GAAG,CACrB,8CAA8C,EAC9C,2BAA2B,EAC3B,kBAAkB,EAClB,mBAAmB,CACpB;IAED,KAAK,MAAMxC,OAAO,IAAIwC,cAAc,EAAE;MACpC,IAAIrE,KAAK;MACT,OAAO,CAACA,KAAK,GAAG6B,OAAO,CAAC5B,IAAI,CAACJ,IAAI,CAAC,MAAM,IAAI,EAAE;QAC5C,MAAMmC,WAAW,GAAG7B,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;QACtC,IAAI,CAACF,eAAe,CAACwE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvC,WAAW,KAAKA,WAAW,CAAC,EAAE;UAC7DlC,eAAe,CAACS,IAAI,CAAC;YACnBE,KAAK,EAAET,KAAK,CAACS,KAAK;YAClBC,SAAS,EAAEV,KAAK,CAAC,CAAC,CAAC;YACnBgC,WAAW,EAAEA;UACf,CAAC,CAAC;QACJ;MACF;IACF;IAEA7C,OAAO,CAACC,GAAG,CAAC,SAASU,eAAe,CAACT,MAAM,mCAAmC,EAClES,eAAe,CAAC2B,GAAG,CAAC8C,CAAC,IAAIA,CAAC,CAACvC,WAAW,CAAC,CAACX,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CAAC;;IAE1E;IACAzB,eAAe,CAACuB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACb,KAAK,GAAGc,CAAC,CAACd,KAAK,CAAC;;IAEjD;IACA,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,eAAe,CAACT,MAAM,EAAEhB,CAAC,EAAE,EAAE;MAC/C,MAAMsC,MAAM,GAAGb,eAAe,CAACzB,CAAC,CAAC;MACjC,MAAMuC,UAAU,GAAGd,eAAe,CAACzB,CAAC,GAAG,CAAC,CAAC;;MAEzC;MACA,MAAMwC,UAAU,GAAGF,MAAM,CAACF,KAAK,GAAGE,MAAM,CAACD,SAAS,CAACrB,MAAM;MACzD,MAAMyB,QAAQ,GAAGF,UAAU,GAAGA,UAAU,CAACH,KAAK,GAAGZ,IAAI,CAACR,MAAM;MAC5D,MAAM0B,OAAO,GAAGlB,IAAI,CAACP,SAAS,CAACuB,UAAU,EAAEC,QAAQ,CAAC,CAACE,IAAI,CAAC,CAAC;MAE3D7B,OAAO,CAACC,GAAG,CAAC,wCAAwCuB,MAAM,CAACqB,WAAW,MAAM,CAAC;MAC7E7C,OAAO,CAACC,GAAG,CAAC,mBAAmB2B,OAAO,CAAC1B,MAAM,EAAE,CAAC;MAChDF,OAAO,CAACC,GAAG,CAAC,oBAAoB2B,OAAO,CAACzB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;;MAE/D;MACA,IAAIkF,aAAa,GAAG,IAAI;MACxB,MAAMC,cAAc,GAAG,CACrB,qCAAqC,EACrC,oCAAoC,EACpC,+BAA+B,EAC/B,mCAAmC,EACnC,uBAAuB,EACvB,2BAA2B,EAC3B,yBAAyB,EACzB,+BAA+B,EAC/B,kCAAkC,EAClC,kCAAkC,EAClC,qBAAqB;MAAG;MACxB,YAAY,CAAE;MAAA,CACf;MAED,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,cAAc,CAACpF,MAAM,EAAEqF,CAAC,EAAE,EAAE;QAC9C,MAAM7C,OAAO,GAAG4C,cAAc,CAACC,CAAC,CAAC;QACjC,MAAM1E,KAAK,GAAGe,OAAO,CAACf,KAAK,CAAC6B,OAAO,CAAC;QACpC,IAAI7B,KAAK,EAAE;UACTwE,aAAa,GAAGxE,KAAK,CAAC,CAAC,CAAC,CAAC2E,WAAW,CAAC,CAAC;UACtCxF,OAAO,CAACC,GAAG,CAAC,yBAAyBoF,aAAa,UAAU7D,MAAM,CAACqB,WAAW,kBAAkB0C,CAAC,KAAK7C,OAAO,CAAC+C,MAAM,EAAE,CAAC;UACvH;QACF;MACF;;MAEA;MACA,IAAI,CAACJ,aAAa,EAAE;QAClBrF,OAAO,CAACC,GAAG,CAAC,yEAAyEuB,MAAM,CAACqB,WAAW,EAAE,CAAC;;QAE1G;QACA,MAAM6C,kBAAkB,GAAG,CACzB,cAAc,CAAE;QAAA,CACjB;QAED,KAAK,MAAMhD,OAAO,IAAIgD,kBAAkB,EAAE;UACxC,MAAM/C,OAAO,GAAG,CAAC,GAAGf,OAAO,CAACgB,QAAQ,CAACF,OAAO,CAAC,CAAC;UAC9C,IAAIC,OAAO,CAACzC,MAAM,GAAG,CAAC,EAAE;YACtB;YACAmF,aAAa,GAAG1C,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC6C,WAAW,CAAC,CAAC;YAC3CxF,OAAO,CAACC,GAAG,CAAC,iBAAiBoF,aAAa,UAAU7D,MAAM,CAACqB,WAAW,wBAAwB,CAAC;YAC/F;UACF;QACF;MACF;;MAEA;MACA,IAAI8C,WAAW,GAAG,EAAE;;MAEpB;MACA,MAAMC,mBAAmB,GAAG,CAC1B,6CAA6C,EAC7C,wCAAwC,EACxC,uCAAuC,EACvC,yBAAyB,CAC1B;MAED,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,mBAAmB,CAAC1F,MAAM,EAAEqF,CAAC,EAAE,EAAE;QACnD,MAAM7C,OAAO,GAAGkD,mBAAmB,CAACL,CAAC,CAAC;QACtC,MAAM1E,KAAK,GAAGe,OAAO,CAACf,KAAK,CAAC6B,OAAO,CAAC;QACpC,IAAI7B,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC,CAAC3B,MAAM,GAAG,EAAE,EAAE;UACxCyF,WAAW,GAAG9E,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC;UAC7B7B,OAAO,CAACC,GAAG,CAAC,0BAA0BuB,MAAM,CAACqB,WAAW,kBAAkB0C,CAAC,EAAE,CAAC;UAC9E;QACF;MACF;;MAEA;MACA,IAAI,CAACI,WAAW,EAAE;QAChB,MAAME,YAAY,GAAG,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;QAClE,KAAK,MAAMC,OAAO,IAAID,YAAY,EAAE;UAClC,MAAME,SAAS,GAAGnE,OAAO,CAACoE,WAAW,CAAC,CAAC,CAACC,OAAO,CAACH,OAAO,CAACE,WAAW,CAAC,CAAC,CAAC;UACtE,IAAID,SAAS,KAAK,CAAC,CAAC,EAAE;YACpB,IAAIG,SAAS,GAAGtE,OAAO,CAACzB,SAAS,CAAC4F,SAAS,GAAGD,OAAO,CAAC5F,MAAM,CAAC,CAAC2B,IAAI,CAAC,CAAC;YACpEqE,SAAS,GAAGA,SAAS,CAACvB,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;YAE9C,MAAMwB,iBAAiB,GAAGD,SAAS,CAACE,MAAM,CAAC,gBAAgB,CAAC;YAC5D,IAAID,iBAAiB,KAAK,CAAC,CAAC,EAAE;cAC5BD,SAAS,GAAGA,SAAS,CAAC/F,SAAS,CAAC,CAAC,EAAEgG,iBAAiB,CAAC;YACvD;YAEA,IAAID,SAAS,CAAChG,MAAM,GAAG,EAAE,EAAE;cACzByF,WAAW,GAAGO,SAAS,CAACrE,IAAI,CAAC,CAAC;cAC9B7B,OAAO,CAACC,GAAG,CAAC,sCAAsCuB,MAAM,CAACqB,WAAW,EAAE,CAAC;cACvE;YACF;UACF;QACF;MACF;;MAEA;MACA,IAAI8C,WAAW,EAAE;QACfA,WAAW,GAAGA,WAAW,CAAChB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;QAC9CgB,WAAW,GAAGA,WAAW,CAAChB,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;QACrDgB,WAAW,GAAGA,WAAW,CAAChB,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;QACxDgB,WAAW,GAAGA,WAAW,CAAChB,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;QACrDgB,WAAW,GAAGA,WAAW,CAAChB,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QACzDgB,WAAW,GAAGA,WAAW,CAAChB,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;QACnDgB,WAAW,GAAGA,WAAW,CAAC9D,IAAI,CAAC,CAAC;MAClC;;MAEA;MACA,IAAIwD,aAAa,EAAE;QACjB3G,OAAO,CAAC8C,MAAM,CAACqB,WAAW,CAAC,GAAG;UAC5BwC,aAAa,EAAEA,aAAa;UAC5BM,WAAW,EAAEA,WAAW,IAAI;QAC9B,CAAC;QACD3F,OAAO,CAACC,GAAG,CAAC,wCAAwCuB,MAAM,CAACqB,WAAW,KAAKwC,aAAa,EAAE,CAAC;MAC7F,CAAC,MAAM;QACLrF,OAAO,CAACC,GAAG,CAAC,wCAAwCuB,MAAM,CAACqB,WAAW,EAAE,CAAC;QACzE7C,OAAO,CAACC,GAAG,CAAC,8BAA8B2B,OAAO,CAACzB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;MAC3E;IACF;IAEAH,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnDD,OAAO,CAACC,GAAG,CAAC,sCAAsCO,MAAM,CAACC,IAAI,CAAC/B,OAAO,CAAC,CAACwB,MAAM,YAAY,CAAC;IAC1FF,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEO,MAAM,CAACC,IAAI,CAAC/B,OAAO,CAAC,CAACwD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CAACE,GAAG,CAACC,CAAC,IAAI,IAAIA,CAAC,IAAI7D,OAAO,CAAC6D,CAAC,CAAC,CAAC8C,aAAa,EAAE,CAAC,CAAC;IAExH,OAAO3G,OAAO;EAChB;EAEA,MAAM2H,UAAUA,CAACC,YAAY,EAAEC,UAAU,EAAE;IACzC,IAAI;MACF,MAAM,CAAC9H,SAAS,EAAEC,OAAO,CAAC,GAAG,MAAM8H,OAAO,CAACC,GAAG,CAAC,CAC7C,IAAI,CAAC9H,iBAAiB,CAAC2H,YAAY,CAAC,EACpC,IAAI,CAAChG,eAAe,CAACiG,UAAU,CAAC,CACjC,CAAC;MAEF,OAAO;QAAE9H,SAAS;QAAEC;MAAQ,CAAC;IAC/B,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF;AACF;AAEA,eAAe9B,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}