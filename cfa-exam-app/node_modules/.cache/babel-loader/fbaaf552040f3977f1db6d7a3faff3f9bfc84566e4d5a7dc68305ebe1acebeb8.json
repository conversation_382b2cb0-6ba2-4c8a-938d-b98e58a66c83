{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Button from './Button';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst noop = () => undefined;\nconst ToggleButton = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  name,\n  className,\n  checked,\n  type,\n  onChange,\n  value,\n  disabled,\n  id,\n  inputRef,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'btn-check');\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [/*#__PURE__*/_jsx(\"input\", {\n      className: bsPrefix,\n      name: name,\n      type: type,\n      value: value,\n      ref: inputRef,\n      autoComplete: \"off\",\n      checked: !!checked,\n      disabled: !!disabled,\n      onChange: onChange || noop,\n      id: id\n    }), /*#__PURE__*/_jsx(Button, {\n      ...props,\n      ref: ref,\n      className: classNames(className, disabled && 'disabled'),\n      type: undefined,\n      role: undefined,\n      as: \"label\",\n      htmlFor: id\n    })]\n  });\n});\nToggleButton.displayName = 'ToggleButton';\nexport default ToggleButton;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "<PERSON><PERSON>", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "noop", "undefined", "ToggleButton", "forwardRef", "bsPrefix", "name", "className", "checked", "type", "onChange", "value", "disabled", "id", "inputRef", "props", "ref", "children", "autoComplete", "role", "as", "htmlFor", "displayName"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/react-bootstrap/esm/ToggleButton.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Button from './Button';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst noop = () => undefined;\nconst ToggleButton = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  name,\n  className,\n  checked,\n  type,\n  onChange,\n  value,\n  disabled,\n  id,\n  inputRef,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'btn-check');\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [/*#__PURE__*/_jsx(\"input\", {\n      className: bsPrefix,\n      name: name,\n      type: type,\n      value: value,\n      ref: inputRef,\n      autoComplete: \"off\",\n      checked: !!checked,\n      disabled: !!disabled,\n      onChange: onChange || noop,\n      id: id\n    }), /*#__PURE__*/_jsx(Button, {\n      ...props,\n      ref: ref,\n      className: classNames(className, disabled && 'disabled'),\n      type: undefined,\n      role: undefined,\n      as: \"label\",\n      htmlFor: id\n    })]\n  });\n});\nToggleButton.displayName = 'ToggleButton';\nexport default ToggleButton;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,QAAQ,IAAIC,SAAS,QAAQ,mBAAmB;AACzD,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,IAAI,GAAGA,CAAA,KAAMC,SAAS;AAC5B,MAAMC,YAAY,GAAG,aAAaX,KAAK,CAACY,UAAU,CAAC,CAAC;EAClDC,QAAQ;EACRC,IAAI;EACJC,SAAS;EACTC,OAAO;EACPC,IAAI;EACJC,QAAQ;EACRC,KAAK;EACLC,QAAQ;EACRC,EAAE;EACFC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTX,QAAQ,GAAGZ,kBAAkB,CAACY,QAAQ,EAAE,WAAW,CAAC;EACpD,OAAO,aAAaL,KAAK,CAACF,SAAS,EAAE;IACnCmB,QAAQ,EAAE,CAAC,aAAarB,IAAI,CAAC,OAAO,EAAE;MACpCW,SAAS,EAAEF,QAAQ;MACnBC,IAAI,EAAEA,IAAI;MACVG,IAAI,EAAEA,IAAI;MACVE,KAAK,EAAEA,KAAK;MACZK,GAAG,EAAEF,QAAQ;MACbI,YAAY,EAAE,KAAK;MACnBV,OAAO,EAAE,CAAC,CAACA,OAAO;MAClBI,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBF,QAAQ,EAAEA,QAAQ,IAAIT,IAAI;MAC1BY,EAAE,EAAEA;IACN,CAAC,CAAC,EAAE,aAAajB,IAAI,CAACF,MAAM,EAAE;MAC5B,GAAGqB,KAAK;MACRC,GAAG,EAAEA,GAAG;MACRT,SAAS,EAAEhB,UAAU,CAACgB,SAAS,EAAEK,QAAQ,IAAI,UAAU,CAAC;MACxDH,IAAI,EAAEP,SAAS;MACfiB,IAAI,EAAEjB,SAAS;MACfkB,EAAE,EAAE,OAAO;MACXC,OAAO,EAAER;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFV,YAAY,CAACmB,WAAW,GAAG,cAAc;AACzC,eAAenB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}