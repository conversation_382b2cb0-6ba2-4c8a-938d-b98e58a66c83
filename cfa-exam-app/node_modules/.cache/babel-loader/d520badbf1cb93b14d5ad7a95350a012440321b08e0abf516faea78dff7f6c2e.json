{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Ta\\u0300i lie\\u0323\\u0302u CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/QuestionDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Card, Button, Alert, Badge, Row, Col } from 'react-bootstrap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuestionDisplay = ({\n  question,\n  groupQuestions,\n  answers,\n  userAnswer,\n  showAnswer,\n  onSubmitAnswer,\n  onRetryQuestion\n}) => {\n  _s();\n  var _groupQuestions$, _groupQuestions;\n  const [selectedAnswer, setSelectedAnswer] = useState(userAnswer || '');\n  const handleAnswerSelect = choice => {\n    if (showAnswer) return; // Prevent changing answer after submission\n    setSelectedAnswer(choice);\n  };\n  const handleSubmit = () => {\n    if (!selectedAnswer) return;\n    onSubmitAnswer(question.questionNumber, selectedAnswer);\n  };\n  const handleRetry = () => {\n    setSelectedAnswer('');\n    if (onRetryQuestion) {\n      onRetryQuestion(question.questionNumber);\n    }\n  };\n  const getAnswerData = () => {\n    const answerData = answers[question.questionNumber] || {};\n\n    // Debug logging\n    if (showAnswer) {\n      console.log(`Answer data for question ${question.questionNumber}:`, answerData);\n      console.log(`Has explanation: ${!!answerData.explanation}`);\n      if (answerData.explanation) {\n        console.log(`Explanation length: ${answerData.explanation.length}`);\n        console.log(`Explanation preview: ${answerData.explanation.substring(0, 200)}...`);\n      }\n    }\n    return answerData;\n  };\n  const isCorrect = () => {\n    const answerData = getAnswerData();\n    return userAnswer === answerData.correctAnswer;\n  };\n  const getChoiceVariant = choice => {\n    if (!showAnswer) {\n      return selectedAnswer === choice ? 'primary' : 'outline-secondary';\n    }\n    const answerData = getAnswerData();\n    if (choice === answerData.correctAnswer) {\n      return 'success';\n    } else if (choice === userAnswer && choice !== answerData.correctAnswer) {\n      return 'danger';\n    }\n    return 'outline-secondary';\n  };\n  const getChoiceIcon = choice => {\n    if (!showAnswer) {\n      return selectedAnswer === choice ? 'fas fa-check-circle' : 'far fa-circle';\n    }\n    const answerData = getAnswerData();\n    if (choice === answerData.correctAnswer) {\n      return 'fas fa-check-circle';\n    } else if (choice === userAnswer && choice !== answerData.correctAnswer) {\n      return 'fas fa-times-circle';\n    }\n    return 'far fa-circle';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"question-display\",\n    children: [question.isGroupQuestion && question.context && /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-4 border-info\",\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        className: \"bg-info text-white\",\n        children: /*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-info-circle me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 15\n          }, this), \"Th\\xF4ng tin chung cho c\\xE2u h\\u1ECFi \", (_groupQuestions$ = groupQuestions[0]) === null || _groupQuestions$ === void 0 ? void 0 : _groupQuestions$.questionNumber, \" - \", (_groupQuestions = groupQuestions[groupQuestions.length - 1]) === null || _groupQuestions === void 0 ? void 0 : _groupQuestions.questionNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"context-content\",\n          style: {\n            whiteSpace: 'pre-line',\n            lineHeight: '1.6'\n          },\n          children: question.context\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-4 shadow\",\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        className: \"d-flex justify-content-between align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"mb-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-question-circle me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), \"C\\xE2u h\\u1ECFi \", question.questionNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Badge, {\n            bg: \"secondary\",\n            className: \"me-2\",\n            children: [\"ID: \", question.questionId]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), question.isGroupQuestion && /*#__PURE__*/_jsxDEV(Badge, {\n            bg: \"info\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-layer-group me-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 17\n            }, this), \"Nh\\xF3m c\\xE2u h\\u1ECFi\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"question-text mb-4\",\n          style: {\n            fontSize: '1.1rem',\n            lineHeight: '1.6'\n          },\n          children: question.questionText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"choices-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-3\",\n            children: \"Ch\\u1ECDn \\u0111\\xE1p \\xE1n:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: Object.entries(question.choices).map(([choice, text]) => /*#__PURE__*/_jsxDEV(Col, {\n              xs: 12,\n              className: \"mb-2\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: getChoiceVariant(choice),\n                className: \"w-100 text-start p-3\",\n                onClick: () => handleAnswerSelect(choice),\n                disabled: showAnswer,\n                style: {\n                  minHeight: '60px',\n                  border: selectedAnswer === choice && !showAnswer ? '2px solid #0d6efd' : undefined\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"me-3 mt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: getChoiceIcon(choice)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 144,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      className: \"me-2\",\n                      children: [choice, \")\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 147,\n                      columnNumber: 25\n                    }, this), text]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this)\n            }, choice, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), !showAnswer && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"success\",\n            size: \"lg\",\n            onClick: handleSubmit,\n            disabled: !selectedAnswer,\n            className: \"px-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-check me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this), \"X\\xE1c nh\\u1EADn \\u0111\\xE1p \\xE1n\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: [\"Ho\\u1EB7c nh\\u1EA5n ph\\xEDm \", Object.keys(question.choices).map((choice, index) => `${index + 1} (${choice})`).join(', ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this), showAnswer && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            variant: isCorrect() ? 'success' : 'danger',\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas ${isCorrect() ? 'fa-check-circle' : 'fa-times-circle'} me-2`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: isCorrect() ? 'Chính xác!' : 'Không chính xác'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [\"\\u0110\\xE1p \\xE1n \\u0111\\xFAng: \", getAnswerData().correctAnswer]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), userAnswer && userAnswer !== getAnswerData().correctAnswer && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-1\",\n                children: [\"B\\u1EA1n \\u0111\\xE3 ch\\u1ECDn: \", userAnswer]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            className: \"mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n              className: \"bg-light\",\n              children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"mb-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-lightbulb me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this), \"Gi\\u1EA3i th\\xEDch \\u0111\\xE1p \\xE1n\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: getAnswerData().explanation ? /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    whiteSpace: 'pre-line',\n                    lineHeight: '1.6',\n                    fontSize: '0.95rem'\n                  },\n                  children: getAnswerData().explanation\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n                  className: \"mt-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n                    className: \"text-muted small\",\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-bug me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 27\n                    }, this), \"Debug Info (Click to expand)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2 p-2 bg-light rounded small\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Question ID:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 27\n                    }, this), \" \", question.questionId, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 78\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Answer Data:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 27\n                    }, this), \" \", JSON.stringify(getAnswerData(), null, 2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-muted fst-italic\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-exclamation-triangle me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 25\n                  }, this), \"Kh\\xF4ng t\\xECm th\\u1EA5y gi\\u1EA3i th\\xEDch cho c\\xE2u h\\u1ECFi n\\xE0y trong file PDF.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: \"Vui l\\xF2ng ki\\u1EC3m tra file \\u0111\\xE1p \\xE1n ho\\u1EB7c tham kh\\u1EA3o t\\xE0i li\\u1EC7u g\\u1ED1c.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n                  className: \"mt-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n                    className: \"text-muted small\",\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-bug me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 27\n                    }, this), \"Debug Info - Why no explanation? (Click to expand)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2 p-2 bg-light rounded small\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Question Number:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 27\n                    }, this), \" \", question.questionNumber, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 86\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Question ID:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 27\n                    }, this), \" \", question.questionId, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 78\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Answer Data Found:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 27\n                    }, this), \" \", Object.keys(answers).includes(question.questionNumber.toString()) ? 'Yes' : 'No', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 145\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"All Answer Keys:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 244,\n                      columnNumber: 27\n                    }, this), \" \", Object.keys(answers).join(', '), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 244,\n                      columnNumber: 94\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Raw Answer Data:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 27\n                    }, this), \" \", JSON.stringify(getAnswerData(), null, 2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-warning\",\n              onClick: handleRetry,\n              className: \"me-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-redo me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this), \"L\\xE0m l\\u1EA1i c\\xE2u n\\xE0y\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted d-block mt-2\",\n              children: \"X\\xF3a \\u0111\\xE1p \\xE1n \\u0111\\xE3 ch\\u1ECDn v\\xE0 l\\xE0m l\\u1EA1i c\\xE2u h\\u1ECFi n\\xE0y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n};\n_s(QuestionDisplay, \"bY9+dTFMyj3mt1tT/hv3/doJV24=\");\n_c = QuestionDisplay;\nexport default QuestionDisplay;\nvar _c;\n$RefreshReg$(_c, \"QuestionDisplay\");", "map": {"version": 3, "names": ["React", "useState", "Card", "<PERSON><PERSON>", "<PERSON><PERSON>", "Badge", "Row", "Col", "jsxDEV", "_jsxDEV", "QuestionDisplay", "question", "groupQuestions", "answers", "userAnswer", "showAnswer", "onSubmitAnswer", "onRetryQuestion", "_s", "_groupQuestions$", "_groupQuestions", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedAnswer", "handleAnswerSelect", "choice", "handleSubmit", "questionNumber", "handleRetry", "getAnswerData", "answerData", "console", "log", "explanation", "length", "substring", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "getChoiceVariant", "getChoiceIcon", "className", "children", "isGroupQuestion", "context", "Header", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "style", "whiteSpace", "lineHeight", "bg", "questionId", "fontSize", "questionText", "Object", "entries", "choices", "map", "text", "xs", "variant", "onClick", "disabled", "minHeight", "border", "undefined", "size", "keys", "index", "join", "cursor", "JSON", "stringify", "includes", "toString", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/QuestionDisplay.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Card, Button, Alert, Badge, Row, Col } from 'react-bootstrap';\n\nconst QuestionDisplay = ({\n  question,\n  groupQuestions,\n  answers,\n  userAnswer,\n  showAnswer,\n  onSubmitAnswer,\n  onRetryQuestion\n}) => {\n  const [selectedAnswer, setSelectedAnswer] = useState(userAnswer || '');\n\n  const handleAnswerSelect = (choice) => {\n    if (showAnswer) return; // Prevent changing answer after submission\n    setSelectedAnswer(choice);\n  };\n\n  const handleSubmit = () => {\n    if (!selectedAnswer) return;\n    onSubmitAnswer(question.questionNumber, selectedAnswer);\n  };\n\n  const handleRetry = () => {\n    setSelectedAnswer('');\n    if (onRetryQuestion) {\n      onRetryQuestion(question.questionNumber);\n    }\n  };\n\n  const getAnswerData = () => {\n    const answerData = answers[question.questionNumber] || {};\n\n    // Debug logging\n    if (showAnswer) {\n      console.log(`Answer data for question ${question.questionNumber}:`, answerData);\n      console.log(`Has explanation: ${!!answerData.explanation}`);\n      if (answerData.explanation) {\n        console.log(`Explanation length: ${answerData.explanation.length}`);\n        console.log(`Explanation preview: ${answerData.explanation.substring(0, 200)}...`);\n      }\n    }\n\n    return answerData;\n  };\n\n  const isCorrect = () => {\n    const answerData = getAnswerData();\n    return userAnswer === answerData.correctAnswer;\n  };\n\n  const getChoiceVariant = (choice) => {\n    if (!showAnswer) {\n      return selectedAnswer === choice ? 'primary' : 'outline-secondary';\n    }\n    \n    const answerData = getAnswerData();\n    if (choice === answerData.correctAnswer) {\n      return 'success';\n    } else if (choice === userAnswer && choice !== answerData.correctAnswer) {\n      return 'danger';\n    }\n    return 'outline-secondary';\n  };\n\n  const getChoiceIcon = (choice) => {\n    if (!showAnswer) {\n      return selectedAnswer === choice ? 'fas fa-check-circle' : 'far fa-circle';\n    }\n    \n    const answerData = getAnswerData();\n    if (choice === answerData.correctAnswer) {\n      return 'fas fa-check-circle';\n    } else if (choice === userAnswer && choice !== answerData.correctAnswer) {\n      return 'fas fa-times-circle';\n    }\n    return 'far fa-circle';\n  };\n\n  return (\n    <div className=\"question-display\">\n      {/* Context Section (for group questions) */}\n      {question.isGroupQuestion && question.context && (\n        <Card className=\"mb-4 border-info\">\n          <Card.Header className=\"bg-info text-white\">\n            <h6 className=\"mb-0\">\n              <i className=\"fas fa-info-circle me-2\"></i>\n              Thông tin chung cho câu hỏi {groupQuestions[0]?.questionNumber} - {groupQuestions[groupQuestions.length - 1]?.questionNumber}\n            </h6>\n          </Card.Header>\n          <Card.Body>\n            <div className=\"context-content\" style={{ whiteSpace: 'pre-line', lineHeight: '1.6' }}>\n              {question.context}\n            </div>\n          </Card.Body>\n        </Card>\n      )}\n\n      {/* Question Section */}\n      <Card className=\"mb-4 shadow\">\n        <Card.Header className=\"d-flex justify-content-between align-items-center\">\n          <h5 className=\"mb-0\">\n            <i className=\"fas fa-question-circle me-2\"></i>\n            Câu hỏi {question.questionNumber}\n          </h5>\n          <div>\n            <Badge bg=\"secondary\" className=\"me-2\">\n              ID: {question.questionId}\n            </Badge>\n            {question.isGroupQuestion && (\n              <Badge bg=\"info\">\n                <i className=\"fas fa-layer-group me-1\"></i>\n                Nhóm câu hỏi\n              </Badge>\n            )}\n          </div>\n        </Card.Header>\n        \n        <Card.Body>\n          {/* Question Text */}\n          <div className=\"question-text mb-4\" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>\n            {question.questionText}\n          </div>\n\n          {/* Answer Choices */}\n          <div className=\"choices-section\">\n            <h6 className=\"mb-3\">Chọn đáp án:</h6>\n            <Row>\n              {Object.entries(question.choices).map(([choice, text]) => (\n                <Col key={choice} xs={12} className=\"mb-2\">\n                  <Button\n                    variant={getChoiceVariant(choice)}\n                    className=\"w-100 text-start p-3\"\n                    onClick={() => handleAnswerSelect(choice)}\n                    disabled={showAnswer}\n                    style={{ \n                      minHeight: '60px',\n                      border: selectedAnswer === choice && !showAnswer ? '2px solid #0d6efd' : undefined\n                    }}\n                  >\n                    <div className=\"d-flex align-items-start\">\n                      <div className=\"me-3 mt-1\">\n                        <i className={getChoiceIcon(choice)}></i>\n                      </div>\n                      <div>\n                        <strong className=\"me-2\">{choice})</strong>\n                        {text}\n                      </div>\n                    </div>\n                  </Button>\n                </Col>\n              ))}\n            </Row>\n          </div>\n\n          {/* Submit Button */}\n          {!showAnswer && (\n            <div className=\"text-center mt-4\">\n              <Button\n                variant=\"success\"\n                size=\"lg\"\n                onClick={handleSubmit}\n                disabled={!selectedAnswer}\n                className=\"px-5\"\n              >\n                <i className=\"fas fa-check me-2\"></i>\n                Xác nhận đáp án\n              </Button>\n              <div className=\"mt-2\">\n                <small className=\"text-muted\">\n                  Hoặc nhấn phím {Object.keys(question.choices).map((choice, index) => `${index + 1} (${choice})`).join(', ')}\n                </small>\n              </div>\n            </div>\n          )}\n\n          {/* Answer Explanation */}\n          {showAnswer && (\n            <div className=\"mt-4\">\n              <Alert variant={isCorrect() ? 'success' : 'danger'}>\n                <div className=\"d-flex align-items-center mb-2\">\n                  <i className={`fas ${isCorrect() ? 'fa-check-circle' : 'fa-times-circle'} me-2`}></i>\n                  <strong>\n                    {isCorrect() ? 'Chính xác!' : 'Không chính xác'}\n                  </strong>\n                </div>\n                <div>\n                  <strong>Đáp án đúng: {getAnswerData().correctAnswer}</strong>\n                  {userAnswer && userAnswer !== getAnswerData().correctAnswer && (\n                    <div className=\"mt-1\">\n                      Bạn đã chọn: {userAnswer}\n                    </div>\n                  )}\n                </div>\n              </Alert>\n\n              {/* Always show explanation section, even if empty */}\n              <Card className=\"mt-3\">\n                <Card.Header className=\"bg-light\">\n                  <h6 className=\"mb-0\">\n                    <i className=\"fas fa-lightbulb me-2\"></i>\n                    Giải thích đáp án\n                  </h6>\n                </Card.Header>\n                <Card.Body>\n                  {getAnswerData().explanation ? (\n                    <div>\n                      <div style={{ whiteSpace: 'pre-line', lineHeight: '1.6', fontSize: '0.95rem' }}>\n                        {getAnswerData().explanation}\n                      </div>\n\n                      {/* Debug info */}\n                      <details className=\"mt-3\">\n                        <summary className=\"text-muted small\" style={{ cursor: 'pointer' }}>\n                          <i className=\"fas fa-bug me-1\"></i>\n                          Debug Info (Click to expand)\n                        </summary>\n                        <div className=\"mt-2 p-2 bg-light rounded small\">\n                          <strong>Question ID:</strong> {question.questionId}<br />\n                          <strong>Answer Data:</strong> {JSON.stringify(getAnswerData(), null, 2)}\n                        </div>\n                      </details>\n                    </div>\n                  ) : (\n                    <div>\n                      <div className=\"text-muted fst-italic\">\n                        <i className=\"fas fa-exclamation-triangle me-2\"></i>\n                        Không tìm thấy giải thích cho câu hỏi này trong file PDF.\n                        <br />\n                        <small>Vui lòng kiểm tra file đáp án hoặc tham khảo tài liệu gốc.</small>\n                      </div>\n\n                      {/* Debug info for missing explanation */}\n                      <details className=\"mt-3\">\n                        <summary className=\"text-muted small\" style={{ cursor: 'pointer' }}>\n                          <i className=\"fas fa-bug me-1\"></i>\n                          Debug Info - Why no explanation? (Click to expand)\n                        </summary>\n                        <div className=\"mt-2 p-2 bg-light rounded small\">\n                          <strong>Question Number:</strong> {question.questionNumber}<br />\n                          <strong>Question ID:</strong> {question.questionId}<br />\n                          <strong>Answer Data Found:</strong> {Object.keys(answers).includes(question.questionNumber.toString()) ? 'Yes' : 'No'}<br />\n                          <strong>All Answer Keys:</strong> {Object.keys(answers).join(', ')}<br />\n                          <strong>Raw Answer Data:</strong> {JSON.stringify(getAnswerData(), null, 2)}\n                        </div>\n                      </details>\n                    </div>\n                  )}\n                </Card.Body>\n              </Card>\n\n              {/* Retry Button */}\n              <div className=\"text-center mt-3\">\n                <Button\n                  variant=\"outline-warning\"\n                  onClick={handleRetry}\n                  className=\"me-2\"\n                >\n                  <i className=\"fas fa-redo me-2\"></i>\n                  Làm lại câu này\n                </Button>\n                <small className=\"text-muted d-block mt-2\">\n                  Xóa đáp án đã chọn và làm lại câu hỏi này\n                </small>\n              </div>\n            </div>\n          )}\n        </Card.Body>\n      </Card>\n    </div>\n  );\n};\n\nexport default QuestionDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,eAAe,GAAGA,CAAC;EACvBC,QAAQ;EACRC,cAAc;EACdC,OAAO;EACPC,UAAU;EACVC,UAAU;EACVC,cAAc;EACdC;AACF,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,eAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAACa,UAAU,IAAI,EAAE,CAAC;EAEtE,MAAMS,kBAAkB,GAAIC,MAAM,IAAK;IACrC,IAAIT,UAAU,EAAE,OAAO,CAAC;IACxBO,iBAAiB,CAACE,MAAM,CAAC;EAC3B,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACJ,cAAc,EAAE;IACrBL,cAAc,CAACL,QAAQ,CAACe,cAAc,EAAEL,cAAc,CAAC;EACzD,CAAC;EAED,MAAMM,WAAW,GAAGA,CAAA,KAAM;IACxBL,iBAAiB,CAAC,EAAE,CAAC;IACrB,IAAIL,eAAe,EAAE;MACnBA,eAAe,CAACN,QAAQ,CAACe,cAAc,CAAC;IAC1C;EACF,CAAC;EAED,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,UAAU,GAAGhB,OAAO,CAACF,QAAQ,CAACe,cAAc,CAAC,IAAI,CAAC,CAAC;;IAEzD;IACA,IAAIX,UAAU,EAAE;MACde,OAAO,CAACC,GAAG,CAAC,4BAA4BpB,QAAQ,CAACe,cAAc,GAAG,EAAEG,UAAU,CAAC;MAC/EC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CAACF,UAAU,CAACG,WAAW,EAAE,CAAC;MAC3D,IAAIH,UAAU,CAACG,WAAW,EAAE;QAC1BF,OAAO,CAACC,GAAG,CAAC,uBAAuBF,UAAU,CAACG,WAAW,CAACC,MAAM,EAAE,CAAC;QACnEH,OAAO,CAACC,GAAG,CAAC,wBAAwBF,UAAU,CAACG,WAAW,CAACE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;MACpF;IACF;IAEA,OAAOL,UAAU;EACnB,CAAC;EAED,MAAMM,SAAS,GAAGA,CAAA,KAAM;IACtB,MAAMN,UAAU,GAAGD,aAAa,CAAC,CAAC;IAClC,OAAOd,UAAU,KAAKe,UAAU,CAACO,aAAa;EAChD,CAAC;EAED,MAAMC,gBAAgB,GAAIb,MAAM,IAAK;IACnC,IAAI,CAACT,UAAU,EAAE;MACf,OAAOM,cAAc,KAAKG,MAAM,GAAG,SAAS,GAAG,mBAAmB;IACpE;IAEA,MAAMK,UAAU,GAAGD,aAAa,CAAC,CAAC;IAClC,IAAIJ,MAAM,KAAKK,UAAU,CAACO,aAAa,EAAE;MACvC,OAAO,SAAS;IAClB,CAAC,MAAM,IAAIZ,MAAM,KAAKV,UAAU,IAAIU,MAAM,KAAKK,UAAU,CAACO,aAAa,EAAE;MACvE,OAAO,QAAQ;IACjB;IACA,OAAO,mBAAmB;EAC5B,CAAC;EAED,MAAME,aAAa,GAAId,MAAM,IAAK;IAChC,IAAI,CAACT,UAAU,EAAE;MACf,OAAOM,cAAc,KAAKG,MAAM,GAAG,qBAAqB,GAAG,eAAe;IAC5E;IAEA,MAAMK,UAAU,GAAGD,aAAa,CAAC,CAAC;IAClC,IAAIJ,MAAM,KAAKK,UAAU,CAACO,aAAa,EAAE;MACvC,OAAO,qBAAqB;IAC9B,CAAC,MAAM,IAAIZ,MAAM,KAAKV,UAAU,IAAIU,MAAM,KAAKK,UAAU,CAACO,aAAa,EAAE;MACvE,OAAO,qBAAqB;IAC9B;IACA,OAAO,eAAe;EACxB,CAAC;EAED,oBACE3B,OAAA;IAAK8B,SAAS,EAAC,kBAAkB;IAAAC,QAAA,GAE9B7B,QAAQ,CAAC8B,eAAe,IAAI9B,QAAQ,CAAC+B,OAAO,iBAC3CjC,OAAA,CAACP,IAAI;MAACqC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAChC/B,OAAA,CAACP,IAAI,CAACyC,MAAM;QAACJ,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACzC/B,OAAA;UAAI8B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAClB/B,OAAA;YAAG8B,SAAS,EAAC;UAAyB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,2CACf,GAAA5B,gBAAA,GAACP,cAAc,CAAC,CAAC,CAAC,cAAAO,gBAAA,uBAAjBA,gBAAA,CAAmBO,cAAc,EAAC,KAAG,GAAAN,eAAA,GAACR,cAAc,CAACA,cAAc,CAACqB,MAAM,GAAG,CAAC,CAAC,cAAAb,eAAA,uBAAzCA,eAAA,CAA2CM,cAAc;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1H;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eACdtC,OAAA,CAACP,IAAI,CAAC8C,IAAI;QAAAR,QAAA,eACR/B,OAAA;UAAK8B,SAAS,EAAC,iBAAiB;UAACU,KAAK,EAAE;YAAEC,UAAU,EAAE,UAAU;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAX,QAAA,EACnF7B,QAAQ,CAAC+B;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACP,eAGDtC,OAAA,CAACP,IAAI;MAACqC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC3B/B,OAAA,CAACP,IAAI,CAACyC,MAAM;QAACJ,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBACxE/B,OAAA;UAAI8B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAClB/B,OAAA;YAAG8B,SAAS,EAAC;UAA6B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,oBACvC,EAACpC,QAAQ,CAACe,cAAc;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACLtC,OAAA;UAAA+B,QAAA,gBACE/B,OAAA,CAACJ,KAAK;YAAC+C,EAAE,EAAC,WAAW;YAACb,SAAS,EAAC,MAAM;YAAAC,QAAA,GAAC,MACjC,EAAC7B,QAAQ,CAAC0C,UAAU;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,EACPpC,QAAQ,CAAC8B,eAAe,iBACvBhC,OAAA,CAACJ,KAAK;YAAC+C,EAAE,EAAC,MAAM;YAAAZ,QAAA,gBACd/B,OAAA;cAAG8B,SAAS,EAAC;YAAyB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,2BAE7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEdtC,OAAA,CAACP,IAAI,CAAC8C,IAAI;QAAAR,QAAA,gBAER/B,OAAA;UAAK8B,SAAS,EAAC,oBAAoB;UAACU,KAAK,EAAE;YAAEK,QAAQ,EAAE,QAAQ;YAAEH,UAAU,EAAE;UAAM,CAAE;UAAAX,QAAA,EAClF7B,QAAQ,CAAC4C;QAAY;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAGNtC,OAAA;UAAK8B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B/B,OAAA;YAAI8B,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtCtC,OAAA,CAACH,GAAG;YAAAkC,QAAA,EACDgB,MAAM,CAACC,OAAO,CAAC9C,QAAQ,CAAC+C,OAAO,CAAC,CAACC,GAAG,CAAC,CAAC,CAACnC,MAAM,EAAEoC,IAAI,CAAC,kBACnDnD,OAAA,CAACF,GAAG;cAAcsD,EAAE,EAAE,EAAG;cAACtB,SAAS,EAAC,MAAM;cAAAC,QAAA,eACxC/B,OAAA,CAACN,MAAM;gBACL2D,OAAO,EAAEzB,gBAAgB,CAACb,MAAM,CAAE;gBAClCe,SAAS,EAAC,sBAAsB;gBAChCwB,OAAO,EAAEA,CAAA,KAAMxC,kBAAkB,CAACC,MAAM,CAAE;gBAC1CwC,QAAQ,EAAEjD,UAAW;gBACrBkC,KAAK,EAAE;kBACLgB,SAAS,EAAE,MAAM;kBACjBC,MAAM,EAAE7C,cAAc,KAAKG,MAAM,IAAI,CAACT,UAAU,GAAG,mBAAmB,GAAGoD;gBAC3E,CAAE;gBAAA3B,QAAA,eAEF/B,OAAA;kBAAK8B,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvC/B,OAAA;oBAAK8B,SAAS,EAAC,WAAW;oBAAAC,QAAA,eACxB/B,OAAA;sBAAG8B,SAAS,EAAED,aAAa,CAACd,MAAM;oBAAE;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACNtC,OAAA;oBAAA+B,QAAA,gBACE/B,OAAA;sBAAQ8B,SAAS,EAAC,MAAM;sBAAAC,QAAA,GAAEhB,MAAM,EAAC,GAAC;oBAAA;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC1Ca,IAAI;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC,GApBDvB,MAAM;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBX,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAAChC,UAAU,iBACVN,OAAA;UAAK8B,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B/B,OAAA,CAACN,MAAM;YACL2D,OAAO,EAAC,SAAS;YACjBM,IAAI,EAAC,IAAI;YACTL,OAAO,EAAEtC,YAAa;YACtBuC,QAAQ,EAAE,CAAC3C,cAAe;YAC1BkB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAEhB/B,OAAA;cAAG8B,SAAS,EAAC;YAAmB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,sCAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtC,OAAA;YAAK8B,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB/B,OAAA;cAAO8B,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,8BACb,EAACgB,MAAM,CAACa,IAAI,CAAC1D,QAAQ,CAAC+C,OAAO,CAAC,CAACC,GAAG,CAAC,CAACnC,MAAM,EAAE8C,KAAK,KAAK,GAAGA,KAAK,GAAG,CAAC,KAAK9C,MAAM,GAAG,CAAC,CAAC+C,IAAI,CAAC,IAAI,CAAC;YAAA;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGAhC,UAAU,iBACTN,OAAA;UAAK8B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB/B,OAAA,CAACL,KAAK;YAAC0D,OAAO,EAAE3B,SAAS,CAAC,CAAC,GAAG,SAAS,GAAG,QAAS;YAAAK,QAAA,gBACjD/B,OAAA;cAAK8B,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7C/B,OAAA;gBAAG8B,SAAS,EAAE,OAAOJ,SAAS,CAAC,CAAC,GAAG,iBAAiB,GAAG,iBAAiB;cAAQ;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrFtC,OAAA;gBAAA+B,QAAA,EACGL,SAAS,CAAC,CAAC,GAAG,YAAY,GAAG;cAAiB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNtC,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAA+B,QAAA,GAAQ,kCAAa,EAACZ,aAAa,CAAC,CAAC,CAACQ,aAAa;cAAA;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,EAC5DjC,UAAU,IAAIA,UAAU,KAAKc,aAAa,CAAC,CAAC,CAACQ,aAAa,iBACzD3B,OAAA;gBAAK8B,SAAS,EAAC,MAAM;gBAAAC,QAAA,GAAC,iCACP,EAAC1B,UAAU;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGRtC,OAAA,CAACP,IAAI;YAACqC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACpB/B,OAAA,CAACP,IAAI,CAACyC,MAAM;cAACJ,SAAS,EAAC,UAAU;cAAAC,QAAA,eAC/B/B,OAAA;gBAAI8B,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAClB/B,OAAA;kBAAG8B,SAAS,EAAC;gBAAuB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,wCAE3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACdtC,OAAA,CAACP,IAAI,CAAC8C,IAAI;cAAAR,QAAA,EACPZ,aAAa,CAAC,CAAC,CAACI,WAAW,gBAC1BvB,OAAA;gBAAA+B,QAAA,gBACE/B,OAAA;kBAAKwC,KAAK,EAAE;oBAAEC,UAAU,EAAE,UAAU;oBAAEC,UAAU,EAAE,KAAK;oBAAEG,QAAQ,EAAE;kBAAU,CAAE;kBAAAd,QAAA,EAC5EZ,aAAa,CAAC,CAAC,CAACI;gBAAW;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eAGNtC,OAAA;kBAAS8B,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACvB/B,OAAA;oBAAS8B,SAAS,EAAC,kBAAkB;oBAACU,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBACjE/B,OAAA;sBAAG8B,SAAS,EAAC;oBAAiB;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,gCAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eACVtC,OAAA;oBAAK8B,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9C/B,OAAA;sBAAA+B,QAAA,EAAQ;oBAAY;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACpC,QAAQ,CAAC0C,UAAU,eAAC5C,OAAA;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzDtC,OAAA;sBAAA+B,QAAA,EAAQ;oBAAY;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC0B,IAAI,CAACC,SAAS,CAAC9C,aAAa,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;kBAAA;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,gBAENtC,OAAA;gBAAA+B,QAAA,gBACE/B,OAAA;kBAAK8B,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBACpC/B,OAAA;oBAAG8B,SAAS,EAAC;kBAAkC;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,2FAEpD,eAAAtC,OAAA;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNtC,OAAA;oBAAA+B,QAAA,EAAO;kBAA0D;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eAGNtC,OAAA;kBAAS8B,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACvB/B,OAAA;oBAAS8B,SAAS,EAAC,kBAAkB;oBAACU,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBACjE/B,OAAA;sBAAG8B,SAAS,EAAC;oBAAiB;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,sDAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eACVtC,OAAA;oBAAK8B,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9C/B,OAAA;sBAAA+B,QAAA,EAAQ;oBAAgB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACpC,QAAQ,CAACe,cAAc,eAACjB,OAAA;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjEtC,OAAA;sBAAA+B,QAAA,EAAQ;oBAAY;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACpC,QAAQ,CAAC0C,UAAU,eAAC5C,OAAA;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzDtC,OAAA;sBAAA+B,QAAA,EAAQ;oBAAkB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACS,MAAM,CAACa,IAAI,CAACxD,OAAO,CAAC,CAAC8D,QAAQ,CAAChE,QAAQ,CAACe,cAAc,CAACkD,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,eAACnE,OAAA;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC5HtC,OAAA;sBAAA+B,QAAA,EAAQ;oBAAgB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACS,MAAM,CAACa,IAAI,CAACxD,OAAO,CAAC,CAAC0D,IAAI,CAAC,IAAI,CAAC,eAAC9D,OAAA;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzEtC,OAAA;sBAAA+B,QAAA,EAAQ;oBAAgB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC0B,IAAI,CAACC,SAAS,CAAC9C,aAAa,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;kBAAA;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGPtC,OAAA;YAAK8B,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B/B,OAAA,CAACN,MAAM;cACL2D,OAAO,EAAC,iBAAiB;cACzBC,OAAO,EAAEpC,WAAY;cACrBY,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAEhB/B,OAAA;gBAAG8B,SAAS,EAAC;cAAkB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,iCAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtC,OAAA;cAAO8B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAE3C;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC7B,EAAA,CA7QIR,eAAe;AAAAmE,EAAA,GAAfnE,eAAe;AA+QrB,eAAeA,eAAe;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}