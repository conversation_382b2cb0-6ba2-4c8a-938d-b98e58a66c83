{"ast": null, "code": "export { default as useUncontrolled, useUncontrolledProp } from './hook';\nexport { default as uncontrollable } from './uncontrollable';", "map": {"version": 3, "names": ["default", "useUncontrolled", "useUncontrolledProp", "uncontrollable"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/uncontrollable/lib/esm/index.js"], "sourcesContent": ["export { default as useUncontrolled, useUncontrolledProp } from './hook';\nexport { default as uncontrollable } from './uncontrollable';"], "mappings": "AAAA,SAASA,OAAO,IAAIC,eAAe,EAAEC,mBAAmB,QAAQ,QAAQ;AACxE,SAASF,OAAO,IAAIG,cAAc,QAAQ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}