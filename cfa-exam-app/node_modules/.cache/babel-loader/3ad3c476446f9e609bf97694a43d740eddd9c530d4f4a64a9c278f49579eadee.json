{"ast": null, "code": "import contains from 'dom-helpers/contains';\nimport listen from 'dom-helpers/listen';\nimport ownerDocument from 'dom-helpers/ownerDocument';\nimport { useCallback, useEffect, useRef } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport warning from 'warning';\nconst noop = () => {};\nfunction isLeftClickEvent(event) {\n  return event.button === 0;\n}\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\nexport const getRefTarget = ref => ref && ('current' in ref ? ref.current : ref);\nconst InitialTriggerEvents = {\n  click: 'mousedown',\n  mouseup: 'mousedown',\n  pointerup: 'pointerdown'\n};\n\n/**\n * The `useClickOutside` hook registers your callback on the document that fires\n * when a pointer event is registered outside of the provided ref or element.\n *\n * @param {Ref<HTMLElement>| HTMLElement} ref  The element boundary\n * @param {function} onClickOutside\n * @param {object=}  options\n * @param {boolean=} options.disabled\n * @param {string=}  options.clickTrigger The DOM event name (click, mousedown, etc) to attach listeners on\n */\nfunction useClickOutside(ref, onClickOutside = noop, {\n  disabled,\n  clickTrigger = 'click'\n} = {}) {\n  const preventMouseClickOutsideRef = useRef(false);\n  const waitingForTrigger = useRef(false);\n  const handleMouseCapture = useCallback(e => {\n    const currentTarget = getRefTarget(ref);\n    warning(!!currentTarget, 'ClickOutside captured a close event but does not have a ref to compare it to. ' + 'useClickOutside(), should be passed a ref that resolves to a DOM node');\n    preventMouseClickOutsideRef.current = !currentTarget || isModifiedEvent(e) || !isLeftClickEvent(e) || !!contains(currentTarget, e.target) || waitingForTrigger.current;\n    waitingForTrigger.current = false;\n  }, [ref]);\n  const handleInitialMouse = useEventCallback(e => {\n    const currentTarget = getRefTarget(ref);\n    if (currentTarget && contains(currentTarget, e.target)) {\n      waitingForTrigger.current = true;\n    } else {\n      // When clicking on scrollbars within current target, click events are not triggered, so this ref\n      // is never reset inside `handleMouseCapture`. This would cause a bug where it requires 2 clicks\n      // to close the overlay.\n      waitingForTrigger.current = false;\n    }\n  });\n  const handleMouse = useEventCallback(e => {\n    if (!preventMouseClickOutsideRef.current) {\n      onClickOutside(e);\n    }\n  });\n  useEffect(() => {\n    var _ownerWindow$event, _ownerWindow$parent;\n    if (disabled || ref == null) return undefined;\n    const doc = ownerDocument(getRefTarget(ref));\n    const ownerWindow = doc.defaultView || window;\n\n    // Store the current event to avoid triggering handlers immediately\n    // For things rendered in an iframe, the event might originate on the parent window\n    // so we should fall back to that global event if the local one doesn't exist\n    // https://github.com/facebook/react/issues/20074\n    let currentEvent = (_ownerWindow$event = ownerWindow.event) != null ? _ownerWindow$event : (_ownerWindow$parent = ownerWindow.parent) == null ? void 0 : _ownerWindow$parent.event;\n    let removeInitialTriggerListener = null;\n    if (InitialTriggerEvents[clickTrigger]) {\n      removeInitialTriggerListener = listen(doc, InitialTriggerEvents[clickTrigger], handleInitialMouse, true);\n    }\n\n    // Use capture for this listener so it fires before React's listener, to\n    // avoid false positives in the contains() check below if the target DOM\n    // element is removed in the React mouse callback.\n    const removeMouseCaptureListener = listen(doc, clickTrigger, handleMouseCapture, true);\n    const removeMouseListener = listen(doc, clickTrigger, e => {\n      // skip if this event is the same as the one running when we added the handlers\n      if (e === currentEvent) {\n        currentEvent = undefined;\n        return;\n      }\n      handleMouse(e);\n    });\n    let mobileSafariHackListeners = [];\n    if ('ontouchstart' in doc.documentElement) {\n      mobileSafariHackListeners = [].slice.call(doc.body.children).map(el => listen(el, 'mousemove', noop));\n    }\n    return () => {\n      removeInitialTriggerListener == null ? void 0 : removeInitialTriggerListener();\n      removeMouseCaptureListener();\n      removeMouseListener();\n      mobileSafariHackListeners.forEach(remove => remove());\n    };\n  }, [ref, disabled, clickTrigger, handleMouseCapture, handleInitialMouse, handleMouse]);\n}\nexport default useClickOutside;", "map": {"version": 3, "names": ["contains", "listen", "ownerDocument", "useCallback", "useEffect", "useRef", "useEventCallback", "warning", "noop", "isLeftClickEvent", "event", "button", "isModifiedEvent", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "getRefTarget", "ref", "current", "InitialTriggerEvents", "click", "mouseup", "pointerup", "useClickOutside", "onClickOutside", "disabled", "clickTrigger", "preventMouseClickOutsideRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleMouseCapture", "e", "currentTarget", "target", "handleInitialMouse", "handleMouse", "_ownerWindow$event", "_ownerWindow$parent", "undefined", "doc", "ownerWindow", "defaultView", "window", "currentEvent", "parent", "removeInitialTriggerListener", "removeMouseCaptureListener", "removeMouseListener", "mobileSafariHackListeners", "documentElement", "slice", "call", "body", "children", "map", "el", "for<PERSON>ach", "remove"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/@restart/ui/esm/useClickOutside.js"], "sourcesContent": ["import contains from 'dom-helpers/contains';\nimport listen from 'dom-helpers/listen';\nimport ownerDocument from 'dom-helpers/ownerDocument';\nimport { useCallback, useEffect, useRef } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport warning from 'warning';\nconst noop = () => {};\nfunction isLeftClickEvent(event) {\n  return event.button === 0;\n}\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\nexport const getRefTarget = ref => ref && ('current' in ref ? ref.current : ref);\nconst InitialTriggerEvents = {\n  click: 'mousedown',\n  mouseup: 'mousedown',\n  pointerup: 'pointerdown'\n};\n\n/**\n * The `useClickOutside` hook registers your callback on the document that fires\n * when a pointer event is registered outside of the provided ref or element.\n *\n * @param {Ref<HTMLElement>| HTMLElement} ref  The element boundary\n * @param {function} onClickOutside\n * @param {object=}  options\n * @param {boolean=} options.disabled\n * @param {string=}  options.clickTrigger The DOM event name (click, mousedown, etc) to attach listeners on\n */\nfunction useClickOutside(ref, onClickOutside = noop, {\n  disabled,\n  clickTrigger = 'click'\n} = {}) {\n  const preventMouseClickOutsideRef = useRef(false);\n  const waitingForTrigger = useRef(false);\n  const handleMouseCapture = useCallback(e => {\n    const currentTarget = getRefTarget(ref);\n    warning(!!currentTarget, 'ClickOutside captured a close event but does not have a ref to compare it to. ' + 'useClickOutside(), should be passed a ref that resolves to a DOM node');\n    preventMouseClickOutsideRef.current = !currentTarget || isModifiedEvent(e) || !isLeftClickEvent(e) || !!contains(currentTarget, e.target) || waitingForTrigger.current;\n    waitingForTrigger.current = false;\n  }, [ref]);\n  const handleInitialMouse = useEventCallback(e => {\n    const currentTarget = getRefTarget(ref);\n    if (currentTarget && contains(currentTarget, e.target)) {\n      waitingForTrigger.current = true;\n    } else {\n      // When clicking on scrollbars within current target, click events are not triggered, so this ref\n      // is never reset inside `handleMouseCapture`. This would cause a bug where it requires 2 clicks\n      // to close the overlay.\n      waitingForTrigger.current = false;\n    }\n  });\n  const handleMouse = useEventCallback(e => {\n    if (!preventMouseClickOutsideRef.current) {\n      onClickOutside(e);\n    }\n  });\n  useEffect(() => {\n    var _ownerWindow$event, _ownerWindow$parent;\n    if (disabled || ref == null) return undefined;\n    const doc = ownerDocument(getRefTarget(ref));\n    const ownerWindow = doc.defaultView || window;\n\n    // Store the current event to avoid triggering handlers immediately\n    // For things rendered in an iframe, the event might originate on the parent window\n    // so we should fall back to that global event if the local one doesn't exist\n    // https://github.com/facebook/react/issues/20074\n    let currentEvent = (_ownerWindow$event = ownerWindow.event) != null ? _ownerWindow$event : (_ownerWindow$parent = ownerWindow.parent) == null ? void 0 : _ownerWindow$parent.event;\n    let removeInitialTriggerListener = null;\n    if (InitialTriggerEvents[clickTrigger]) {\n      removeInitialTriggerListener = listen(doc, InitialTriggerEvents[clickTrigger], handleInitialMouse, true);\n    }\n\n    // Use capture for this listener so it fires before React's listener, to\n    // avoid false positives in the contains() check below if the target DOM\n    // element is removed in the React mouse callback.\n    const removeMouseCaptureListener = listen(doc, clickTrigger, handleMouseCapture, true);\n    const removeMouseListener = listen(doc, clickTrigger, e => {\n      // skip if this event is the same as the one running when we added the handlers\n      if (e === currentEvent) {\n        currentEvent = undefined;\n        return;\n      }\n      handleMouse(e);\n    });\n    let mobileSafariHackListeners = [];\n    if ('ontouchstart' in doc.documentElement) {\n      mobileSafariHackListeners = [].slice.call(doc.body.children).map(el => listen(el, 'mousemove', noop));\n    }\n    return () => {\n      removeInitialTriggerListener == null ? void 0 : removeInitialTriggerListener();\n      removeMouseCaptureListener();\n      removeMouseListener();\n      mobileSafariHackListeners.forEach(remove => remove());\n    };\n  }, [ref, disabled, clickTrigger, handleMouseCapture, handleInitialMouse, handleMouse]);\n}\nexport default useClickOutside;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,aAAa,MAAM,2BAA2B;AACrD,SAASC,WAAW,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACtD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,OAAO,MAAM,SAAS;AAC7B,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACrB,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,OAAOA,KAAK,CAACC,MAAM,KAAK,CAAC;AAC3B;AACA,SAASC,eAAeA,CAACF,KAAK,EAAE;EAC9B,OAAO,CAAC,EAAEA,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,MAAM,IAAIJ,KAAK,CAACK,OAAO,IAAIL,KAAK,CAACM,QAAQ,CAAC;AAC7E;AACA,OAAO,MAAMC,YAAY,GAAGC,GAAG,IAAIA,GAAG,KAAK,SAAS,IAAIA,GAAG,GAAGA,GAAG,CAACC,OAAO,GAAGD,GAAG,CAAC;AAChF,MAAME,oBAAoB,GAAG;EAC3BC,KAAK,EAAE,WAAW;EAClBC,OAAO,EAAE,WAAW;EACpBC,SAAS,EAAE;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACN,GAAG,EAAEO,cAAc,GAAGjB,IAAI,EAAE;EACnDkB,QAAQ;EACRC,YAAY,GAAG;AACjB,CAAC,GAAG,CAAC,CAAC,EAAE;EACN,MAAMC,2BAA2B,GAAGvB,MAAM,CAAC,KAAK,CAAC;EACjD,MAAMwB,iBAAiB,GAAGxB,MAAM,CAAC,KAAK,CAAC;EACvC,MAAMyB,kBAAkB,GAAG3B,WAAW,CAAC4B,CAAC,IAAI;IAC1C,MAAMC,aAAa,GAAGf,YAAY,CAACC,GAAG,CAAC;IACvCX,OAAO,CAAC,CAAC,CAACyB,aAAa,EAAE,gFAAgF,GAAG,uEAAuE,CAAC;IACpLJ,2BAA2B,CAACT,OAAO,GAAG,CAACa,aAAa,IAAIpB,eAAe,CAACmB,CAAC,CAAC,IAAI,CAACtB,gBAAgB,CAACsB,CAAC,CAAC,IAAI,CAAC,CAAC/B,QAAQ,CAACgC,aAAa,EAAED,CAAC,CAACE,MAAM,CAAC,IAAIJ,iBAAiB,CAACV,OAAO;IACtKU,iBAAiB,CAACV,OAAO,GAAG,KAAK;EACnC,CAAC,EAAE,CAACD,GAAG,CAAC,CAAC;EACT,MAAMgB,kBAAkB,GAAG5B,gBAAgB,CAACyB,CAAC,IAAI;IAC/C,MAAMC,aAAa,GAAGf,YAAY,CAACC,GAAG,CAAC;IACvC,IAAIc,aAAa,IAAIhC,QAAQ,CAACgC,aAAa,EAAED,CAAC,CAACE,MAAM,CAAC,EAAE;MACtDJ,iBAAiB,CAACV,OAAO,GAAG,IAAI;IAClC,CAAC,MAAM;MACL;MACA;MACA;MACAU,iBAAiB,CAACV,OAAO,GAAG,KAAK;IACnC;EACF,CAAC,CAAC;EACF,MAAMgB,WAAW,GAAG7B,gBAAgB,CAACyB,CAAC,IAAI;IACxC,IAAI,CAACH,2BAA2B,CAACT,OAAO,EAAE;MACxCM,cAAc,CAACM,CAAC,CAAC;IACnB;EACF,CAAC,CAAC;EACF3B,SAAS,CAAC,MAAM;IACd,IAAIgC,kBAAkB,EAAEC,mBAAmB;IAC3C,IAAIX,QAAQ,IAAIR,GAAG,IAAI,IAAI,EAAE,OAAOoB,SAAS;IAC7C,MAAMC,GAAG,GAAGrC,aAAa,CAACe,YAAY,CAACC,GAAG,CAAC,CAAC;IAC5C,MAAMsB,WAAW,GAAGD,GAAG,CAACE,WAAW,IAAIC,MAAM;;IAE7C;IACA;IACA;IACA;IACA,IAAIC,YAAY,GAAG,CAACP,kBAAkB,GAAGI,WAAW,CAAC9B,KAAK,KAAK,IAAI,GAAG0B,kBAAkB,GAAG,CAACC,mBAAmB,GAAGG,WAAW,CAACI,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,mBAAmB,CAAC3B,KAAK;IAClL,IAAImC,4BAA4B,GAAG,IAAI;IACvC,IAAIzB,oBAAoB,CAACO,YAAY,CAAC,EAAE;MACtCkB,4BAA4B,GAAG5C,MAAM,CAACsC,GAAG,EAAEnB,oBAAoB,CAACO,YAAY,CAAC,EAAEO,kBAAkB,EAAE,IAAI,CAAC;IAC1G;;IAEA;IACA;IACA;IACA,MAAMY,0BAA0B,GAAG7C,MAAM,CAACsC,GAAG,EAAEZ,YAAY,EAAEG,kBAAkB,EAAE,IAAI,CAAC;IACtF,MAAMiB,mBAAmB,GAAG9C,MAAM,CAACsC,GAAG,EAAEZ,YAAY,EAAEI,CAAC,IAAI;MACzD;MACA,IAAIA,CAAC,KAAKY,YAAY,EAAE;QACtBA,YAAY,GAAGL,SAAS;QACxB;MACF;MACAH,WAAW,CAACJ,CAAC,CAAC;IAChB,CAAC,CAAC;IACF,IAAIiB,yBAAyB,GAAG,EAAE;IAClC,IAAI,cAAc,IAAIT,GAAG,CAACU,eAAe,EAAE;MACzCD,yBAAyB,GAAG,EAAE,CAACE,KAAK,CAACC,IAAI,CAACZ,GAAG,CAACa,IAAI,CAACC,QAAQ,CAAC,CAACC,GAAG,CAACC,EAAE,IAAItD,MAAM,CAACsD,EAAE,EAAE,WAAW,EAAE/C,IAAI,CAAC,CAAC;IACvG;IACA,OAAO,MAAM;MACXqC,4BAA4B,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,4BAA4B,CAAC,CAAC;MAC9EC,0BAA0B,CAAC,CAAC;MAC5BC,mBAAmB,CAAC,CAAC;MACrBC,yBAAyB,CAACQ,OAAO,CAACC,MAAM,IAAIA,MAAM,CAAC,CAAC,CAAC;IACvD,CAAC;EACH,CAAC,EAAE,CAACvC,GAAG,EAAEQ,QAAQ,EAAEC,YAAY,EAAEG,kBAAkB,EAAEI,kBAAkB,EAAEC,WAAW,CAAC,CAAC;AACxF;AACA,eAAeX,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}