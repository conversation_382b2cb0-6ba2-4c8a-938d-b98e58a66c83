{"ast": null, "code": "import PropTypes from 'prop-types';\nconst alignDirection = PropTypes.oneOf(['start', 'end']);\nexport const alignPropType = PropTypes.oneOfType([alignDirection, PropTypes.shape({\n  sm: alignDirection\n}), PropTypes.shape({\n  md: alignDirection\n}), PropTypes.shape({\n  lg: alignDirection\n}), PropTypes.shape({\n  xl: alignDirection\n}), PropTypes.shape({\n  xxl: alignDirection\n}), PropTypes.object]);", "map": {"version": 3, "names": ["PropTypes", "alignDirection", "oneOf", "alignPropType", "oneOfType", "shape", "sm", "md", "lg", "xl", "xxl", "object"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/react-bootstrap/esm/types.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nconst alignDirection = PropTypes.oneOf(['start', 'end']);\nexport const alignPropType = PropTypes.oneOfType([alignDirection, PropTypes.shape({\n  sm: alignDirection\n}), PropTypes.shape({\n  md: alignDirection\n}), PropTypes.shape({\n  lg: alignDirection\n}), PropTypes.shape({\n  xl: alignDirection\n}), PropTypes.shape({\n  xxl: alignDirection\n}), PropTypes.object]);"], "mappings": "AAAA,OAAOA,SAAS,MAAM,YAAY;AAClC,MAAMC,cAAc,GAAGD,SAAS,CAACE,KAAK,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACxD,OAAO,MAAMC,aAAa,GAAGH,SAAS,CAACI,SAAS,CAAC,CAACH,cAAc,EAAED,SAAS,CAACK,KAAK,CAAC;EAChFC,EAAE,EAAEL;AACN,CAAC,CAAC,EAAED,SAAS,CAACK,KAAK,CAAC;EAClBE,EAAE,EAAEN;AACN,CAAC,CAAC,EAAED,SAAS,CAACK,KAAK,CAAC;EAClBG,EAAE,EAAEP;AACN,CAAC,CAAC,EAAED,SAAS,CAACK,KAAK,CAAC;EAClBI,EAAE,EAAER;AACN,CAAC,CAAC,EAAED,SAAS,CAACK,KAAK,CAAC;EAClBK,GAAG,EAAET;AACP,CAAC,CAAC,EAAED,SAAS,CAACW,MAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}