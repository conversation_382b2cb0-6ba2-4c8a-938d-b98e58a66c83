{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useDropdownItem } from '@restart/ui/DropdownItem';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DropdownItem = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  eventKey,\n  disabled = false,\n  onClick,\n  active,\n  as: Component = Anchor,\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'dropdown-item');\n  const [dropdownItemProps, meta] = useDropdownItem({\n    key: eventKey,\n    href: props.href,\n    disabled,\n    onClick,\n    active\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ...dropdownItemProps,\n    ref: ref,\n    className: classNames(className, prefix, meta.isActive && 'active', disabled && 'disabled')\n  });\n});\nDropdownItem.displayName = 'DropdownItem';\nexport default DropdownItem;", "map": {"version": 3, "names": ["classNames", "React", "useDropdownItem", "<PERSON><PERSON>", "useBootstrapPrefix", "jsx", "_jsx", "DropdownItem", "forwardRef", "bsPrefix", "className", "eventKey", "disabled", "onClick", "active", "as", "Component", "props", "ref", "prefix", "dropdownItemProps", "meta", "key", "href", "isActive", "displayName"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/react-bootstrap/esm/DropdownItem.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useDropdownItem } from '@restart/ui/DropdownItem';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DropdownItem = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  eventKey,\n  disabled = false,\n  onClick,\n  active,\n  as: Component = Anchor,\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'dropdown-item');\n  const [dropdownItemProps, meta] = useDropdownItem({\n    key: eventKey,\n    href: props.href,\n    disabled,\n    onClick,\n    active\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ...dropdownItemProps,\n    ref: ref,\n    className: classNames(className, prefix, meta.isActive && 'active', disabled && 'disabled')\n  });\n});\nDropdownItem.displayName = 'DropdownItem';\nexport default DropdownItem;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,MAAM,MAAM,oBAAoB;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAC;EAClDC,QAAQ;EACRC,SAAS;EACTC,QAAQ;EACRC,QAAQ,GAAG,KAAK;EAChBC,OAAO;EACPC,MAAM;EACNC,EAAE,EAAEC,SAAS,GAAGb,MAAM;EACtB,GAAGc;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,MAAM,GAAGf,kBAAkB,CAACK,QAAQ,EAAE,eAAe,CAAC;EAC5D,MAAM,CAACW,iBAAiB,EAAEC,IAAI,CAAC,GAAGnB,eAAe,CAAC;IAChDoB,GAAG,EAAEX,QAAQ;IACbY,IAAI,EAAEN,KAAK,CAACM,IAAI;IAChBX,QAAQ;IACRC,OAAO;IACPC;EACF,CAAC,CAAC;EACF,OAAO,aAAaR,IAAI,CAACU,SAAS,EAAE;IAClC,GAAGC,KAAK;IACR,GAAGG,iBAAiB;IACpBF,GAAG,EAAEA,GAAG;IACRR,SAAS,EAAEV,UAAU,CAACU,SAAS,EAAES,MAAM,EAAEE,IAAI,CAACG,QAAQ,IAAI,QAAQ,EAAEZ,QAAQ,IAAI,UAAU;EAC5F,CAAC,CAAC;AACJ,CAAC,CAAC;AACFL,YAAY,CAACkB,WAAW,GAAG,cAAc;AACzC,eAAelB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}