{"ast": null, "code": "import { useCallback, useState } from 'react';\n\n/**\n * Updates state, partial updates are merged into existing state values\n */\n\n/**\n * Mimics a React class component's state model, of having a single unified\n * `state` object and an updater that merges updates into the existing state, as\n * opposed to replacing it.\n *\n * ```js\n * const [state, setState] = useMergeState({ name: '<PERSON>', age: 24 })\n *\n * setState({ name: '<PERSON>' }) // { name: '<PERSON>', age: 24 }\n *\n * setState(state => ({ age: state.age + 10 })) // { name: '<PERSON>', age: 34 }\n * ```\n *\n * @param initialState The initial state object\n */\nexport default function useMergeState(initialState) {\n  const [state, setState] = useState(initialState);\n  const updater = useCallback(update => {\n    if (update === null) return;\n    if (typeof update === 'function') {\n      setState(state => {\n        const nextState = update(state);\n        return nextState == null ? state : Object.assign({}, state, nextState);\n      });\n    } else {\n      setState(state => Object.assign({}, state, update));\n    }\n  }, [setState]);\n  return [state, updater];\n}", "map": {"version": 3, "names": ["useCallback", "useState", "useMergeState", "initialState", "state", "setState", "updater", "update", "nextState", "Object", "assign"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/@restart/ui/node_modules/@restart/hooks/esm/useMergeState.js"], "sourcesContent": ["import { useCallback, useState } from 'react';\n\n/**\n * Updates state, partial updates are merged into existing state values\n */\n\n/**\n * Mimics a React class component's state model, of having a single unified\n * `state` object and an updater that merges updates into the existing state, as\n * opposed to replacing it.\n *\n * ```js\n * const [state, setState] = useMergeState({ name: '<PERSON>', age: 24 })\n *\n * setState({ name: '<PERSON>' }) // { name: '<PERSON>', age: 24 }\n *\n * setState(state => ({ age: state.age + 10 })) // { name: '<PERSON>', age: 34 }\n * ```\n *\n * @param initialState The initial state object\n */\nexport default function useMergeState(initialState) {\n  const [state, setState] = useState(initialState);\n  const updater = useCallback(update => {\n    if (update === null) return;\n    if (typeof update === 'function') {\n      setState(state => {\n        const nextState = update(state);\n        return nextState == null ? state : Object.assign({}, state, nextState);\n      });\n    } else {\n      setState(state => Object.assign({}, state, update));\n    }\n  }, [setState]);\n  return [state, updater];\n}"], "mappings": "AAAA,SAASA,WAAW,EAAEC,QAAQ,QAAQ,OAAO;;AAE7C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,YAAY,EAAE;EAClD,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGJ,QAAQ,CAACE,YAAY,CAAC;EAChD,MAAMG,OAAO,GAAGN,WAAW,CAACO,MAAM,IAAI;IACpC,IAAIA,MAAM,KAAK,IAAI,EAAE;IACrB,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;MAChCF,QAAQ,CAACD,KAAK,IAAI;QAChB,MAAMI,SAAS,GAAGD,MAAM,CAACH,KAAK,CAAC;QAC/B,OAAOI,SAAS,IAAI,IAAI,GAAGJ,KAAK,GAAGK,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,KAAK,EAAEI,SAAS,CAAC;MACxE,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,QAAQ,CAACD,KAAK,IAAIK,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,KAAK,EAAEG,MAAM,CAAC,CAAC;IACrD;EACF,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC;EACd,OAAO,CAACD,KAAK,EAAEE,OAAO,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}