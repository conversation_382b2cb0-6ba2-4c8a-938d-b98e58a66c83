{"ast": null, "code": "import ReactDOM from 'react-dom';\nexport default function safeFindDOMNode(componentOrElement) {\n  if (componentOrElement && 'setState' in componentOrElement) {\n    // TODO: Remove in next major.\n    // eslint-disable-next-line react/no-find-dom-node\n    return ReactDOM.findDOMNode(componentOrElement);\n  }\n  return componentOrElement != null ? componentOrElement : null;\n}", "map": {"version": 3, "names": ["ReactDOM", "safeFindDOMNode", "componentOrElement", "findDOMNode"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/react-bootstrap/esm/safeFindDOMNode.js"], "sourcesContent": ["import ReactDOM from 'react-dom';\nexport default function safeFindDOMNode(componentOrElement) {\n  if (componentOrElement && 'setState' in componentOrElement) {\n    // TODO: Remove in next major.\n    // eslint-disable-next-line react/no-find-dom-node\n    return ReactDOM.findDOMNode(componentOrElement);\n  }\n  return componentOrElement != null ? componentOrElement : null;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,WAAW;AAChC,eAAe,SAASC,eAAeA,CAACC,kBAAkB,EAAE;EAC1D,IAAIA,kBAAkB,IAAI,UAAU,IAAIA,kBAAkB,EAAE;IAC1D;IACA;IACA,OAAOF,QAAQ,CAACG,WAAW,CAACD,kBAAkB,CAAC;EACjD;EACA,OAAOA,kBAAkB,IAAI,IAAI,GAAGA,kBAAkB,GAAG,IAAI;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}