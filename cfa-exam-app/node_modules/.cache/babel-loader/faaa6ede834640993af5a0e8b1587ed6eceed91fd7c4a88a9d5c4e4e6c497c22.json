{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst propTypes = {\n  /**\n   * Specify whether the feedback is for valid or invalid fields\n   *\n   * @type {('valid'|'invalid')}\n   */\n  type: PropTypes.string,\n  /** Display feedback as a tooltip. */\n  tooltip: PropTypes.bool,\n  as: PropTypes.elementType\n};\nconst Feedback = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  as: Component = 'div',\n  className,\n  type = 'valid',\n  tooltip = false,\n  ...props\n}, ref) => /*#__PURE__*/_jsx(Component, {\n  ...props,\n  ref: ref,\n  className: classNames(className, `${type}-${tooltip ? 'tooltip' : 'feedback'}`)\n}));\nFeedback.displayName = 'Feedback';\nFeedback.propTypes = propTypes;\nexport default Feedback;", "map": {"version": 3, "names": ["classNames", "React", "PropTypes", "jsx", "_jsx", "propTypes", "type", "string", "tooltip", "bool", "as", "elementType", "<PERSON><PERSON><PERSON>", "forwardRef", "Component", "className", "props", "ref", "displayName"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/react-bootstrap/esm/Feedback.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst propTypes = {\n  /**\n   * Specify whether the feedback is for valid or invalid fields\n   *\n   * @type {('valid'|'invalid')}\n   */\n  type: PropTypes.string,\n  /** Display feedback as a tooltip. */\n  tooltip: PropTypes.bool,\n  as: PropTypes.elementType\n};\nconst Feedback = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  as: Component = 'div',\n  className,\n  type = 'valid',\n  tooltip = false,\n  ...props\n}, ref) => /*#__PURE__*/_jsx(Component, {\n  ...props,\n  ref: ref,\n  className: classNames(className, `${type}-${tooltip ? 'tooltip' : 'feedback'}`)\n}));\nFeedback.displayName = 'Feedback';\nFeedback.propTypes = propTypes;\nexport default Feedback;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,SAAS,GAAG;EAChB;AACF;AACA;AACA;AACA;EACEC,IAAI,EAAEJ,SAAS,CAACK,MAAM;EACtB;EACAC,OAAO,EAAEN,SAAS,CAACO,IAAI;EACvBC,EAAE,EAAER,SAAS,CAACS;AAChB,CAAC;AACD,MAAMC,QAAQ,GAAG,aAAaX,KAAK,CAACY,UAAU;AAC9C;AACA,CAAC;EACCH,EAAE,EAAEI,SAAS,GAAG,KAAK;EACrBC,SAAS;EACTT,IAAI,GAAG,OAAO;EACdE,OAAO,GAAG,KAAK;EACf,GAAGQ;AACL,CAAC,EAAEC,GAAG,KAAK,aAAab,IAAI,CAACU,SAAS,EAAE;EACtC,GAAGE,KAAK;EACRC,GAAG,EAAEA,GAAG;EACRF,SAAS,EAAEf,UAAU,CAACe,SAAS,EAAE,GAAGT,IAAI,IAAIE,OAAO,GAAG,SAAS,GAAG,UAAU,EAAE;AAChF,CAAC,CAAC,CAAC;AACHI,QAAQ,CAACM,WAAW,GAAG,UAAU;AACjCN,QAAQ,CAACP,SAAS,GAAGA,SAAS;AAC9B,eAAeO,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}