{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport FormGroup from './FormGroup';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst FloatingLabel = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  children,\n  controlId,\n  label,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-floating');\n  return /*#__PURE__*/_jsxs(FormGroup, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    controlId: controlId,\n    ...props,\n    children: [children, /*#__PURE__*/_jsx(\"label\", {\n      htmlFor: controlId,\n      children: label\n    })]\n  });\n});\nFloatingLabel.displayName = 'FloatingLabel';\nexport default FloatingLabel;", "map": {"version": 3, "names": ["classNames", "React", "FormGroup", "useBootstrapPrefix", "jsx", "_jsx", "jsxs", "_jsxs", "FloatingLabel", "forwardRef", "bsPrefix", "className", "children", "controlId", "label", "props", "ref", "htmlFor", "displayName"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/react-bootstrap/esm/FloatingLabel.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport FormGroup from './FormGroup';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst FloatingLabel = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  children,\n  controlId,\n  label,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-floating');\n  return /*#__PURE__*/_jsxs(FormGroup, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    controlId: controlId,\n    ...props,\n    children: [children, /*#__PURE__*/_jsx(\"label\", {\n      htmlFor: controlId,\n      children: label\n    })]\n  });\n});\nFloatingLabel.displayName = 'FloatingLabel';\nexport default FloatingLabel;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,aAAa,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAC;EACnDC,QAAQ;EACRC,SAAS;EACTC,QAAQ;EACRC,SAAS;EACTC,KAAK;EACL,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTN,QAAQ,GAAGP,kBAAkB,CAACO,QAAQ,EAAE,eAAe,CAAC;EACxD,OAAO,aAAaH,KAAK,CAACL,SAAS,EAAE;IACnCc,GAAG,EAAEA,GAAG;IACRL,SAAS,EAAEX,UAAU,CAACW,SAAS,EAAED,QAAQ,CAAC;IAC1CG,SAAS,EAAEA,SAAS;IACpB,GAAGE,KAAK;IACRH,QAAQ,EAAE,CAACA,QAAQ,EAAE,aAAaP,IAAI,CAAC,OAAO,EAAE;MAC9CY,OAAO,EAAEJ,SAAS;MAClBD,QAAQ,EAAEE;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,aAAa,CAACU,WAAW,GAAG,eAAe;AAC3C,eAAeV,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}