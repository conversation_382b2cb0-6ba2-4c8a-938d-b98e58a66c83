{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Ta\\u0300i lie\\u0323\\u0302u CFA II 2025/Question Bank 2025/cfa-exam-app/src/App.js\";\nimport React from 'react';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport './App.css';\nimport CFAExamApp from './components/CFAExamApp';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: /*#__PURE__*/_jsxDEV(CFAExamApp, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "CFAExamApp", "jsxDEV", "_jsxDEV", "App", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/App.js"], "sourcesContent": ["import React from 'react';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport './App.css';\nimport CFAExamApp from './components/CFAExamApp';\n\nfunction App() {\n  return (\n    <div className=\"App\">\n      <CFAExamApp />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,sCAAsC;AAC7C,OAAO,WAAW;AAClB,OAAOC,UAAU,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAKE,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBH,OAAA,CAACF,UAAU;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAAC;AAEV;AAACC,EAAA,GANQP,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}