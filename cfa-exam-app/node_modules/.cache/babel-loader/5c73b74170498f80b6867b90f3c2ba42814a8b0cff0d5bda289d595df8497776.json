{"ast": null, "code": "import * as React from 'react';\nconst TabContext = /*#__PURE__*/React.createContext(null);\nexport default TabContext;", "map": {"version": 3, "names": ["React", "TabContext", "createContext"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/node_modules/@restart/ui/esm/TabContext.js"], "sourcesContent": ["import * as React from 'react';\nconst TabContext = /*#__PURE__*/React.createContext(null);\nexport default TabContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,UAAU,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AACzD,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}