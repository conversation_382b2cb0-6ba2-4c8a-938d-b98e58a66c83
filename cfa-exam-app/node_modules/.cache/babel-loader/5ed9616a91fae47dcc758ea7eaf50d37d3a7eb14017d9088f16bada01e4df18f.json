{"ast": null, "code": "// PDF Parser for CFA Questions and Answers\nimport * as pdfjsLib from 'pdfjs-dist';\n\n// Set worker path - use the worker from the same version\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;\nclass CFAQuestionParser {\n  constructor() {\n    this.questions = [];\n    this.answers = {};\n  }\n  async parseQuestionFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n        fullText += pageText + '\\n\\n';\n      }\n\n      // Debug: Log extracted text\n      console.log('Extracted text length:', fullText.length);\n      console.log('First 2000 characters:', fullText.substring(0, 2000));\n\n      // Parse questions from text\n      const questions = this.extractQuestions(fullText);\n      console.log('Extracted questions:', questions.length);\n      return questions;\n    } catch (error) {\n      console.error('Error parsing question file:', error);\n      throw error;\n    }\n  }\n  async parseAnswerFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n        fullText += pageText + '\\n\\n';\n      }\n      console.log('Answer file text length:', fullText.length);\n      console.log('Answer file preview:', fullText.substring(0, 1000));\n\n      // Parse answers from text\n      const answers = this.extractAnswers(fullText);\n      console.log('Extracted answers:', Object.keys(answers).length);\n      return answers;\n    } catch (error) {\n      console.error('Error parsing answer file:', error);\n      throw error;\n    }\n  }\n  extractQuestions(text) {\n    const questions = [];\n    console.log('Looking for question patterns in text...');\n\n    // First, try to find all question markers\n    const questionMarkers = [];\n    const patterns = [/Question #(\\d+) of (\\d+) Question ID: (\\d+)/g, /Question #(\\d+) - (\\d+) of (\\d+) Question ID: (\\d+)/g];\n    for (const pattern of patterns) {\n      let match;\n      while ((match = pattern.exec(text)) !== null) {\n        questionMarkers.push({\n          index: match.index,\n          fullMatch: match[0],\n          questionNum: parseInt(match[1]),\n          endNum: match[2] ? parseInt(match[2]) : null,\n          totalQ: parseInt(match[match.length - 2]),\n          questionId: match[match.length - 1],\n          isGroup: match[0].includes(' - ')\n        });\n      }\n    }\n    console.log(`Found ${questionMarkers.length} question markers`);\n    if (questionMarkers.length === 0) {\n      console.log('No question patterns found. Trying alternative extraction...');\n      return this.extractQuestionsAlternative(text);\n    }\n\n    // Sort by position in text\n    questionMarkers.sort((a, b) => a.index - b.index);\n\n    // Extract content for each question\n    for (let i = 0; i < questionMarkers.length; i++) {\n      const marker = questionMarkers[i];\n      const nextMarker = questionMarkers[i + 1];\n\n      // Get content from current marker to next marker (or end of text)\n      const startIndex = marker.index + marker.fullMatch.length;\n      const endIndex = nextMarker ? nextMarker.index : text.length;\n      const content = text.substring(startIndex, endIndex).trim();\n      console.log(`Processing question ${marker.questionNum}, content length: ${content.length}`);\n      if (marker.isGroup) {\n        // Handle group questions\n        const groupQuestions = this.extractGroupQuestions(content, marker.questionNum, marker.endNum, marker.totalQ, '' // Context will be extracted within the method\n        );\n        questions.push(...groupQuestions);\n      } else {\n        // Handle single question\n        const questionData = this.parseSingleQuestion(content, marker.questionNum, marker.totalQ, marker.questionId);\n        if (questionData) {\n          questions.push(questionData);\n        }\n      }\n    }\n    console.log(`Extracted ${questions.length} questions total`);\n    return questions;\n  }\n  extractQuestionsAlternative(text) {\n    console.log('Using alternative extraction method...');\n    const questions = [];\n\n    // Look for any numbered patterns that might be questions\n    const patterns = [/(\\d+)\\.\\s+([^0-9]+?)(?=\\d+\\.|$)/gs,\n    // 1. Question text\n    /Question\\s+(\\d+)[:\\.]?\\s*([^Q]+?)(?=Question\\s+\\d+|$)/gis // Question 1: text\n    ];\n    for (const pattern of patterns) {\n      const matches = [...text.matchAll(pattern)];\n      console.log(`Alternative pattern found ${matches.length} matches`);\n      for (const match of matches) {\n        const questionNum = parseInt(match[1]);\n        const content = match[2].trim();\n        if (content.length > 20) {\n          // Filter out very short matches\n          const questionData = this.parseSingleQuestion(content, questionNum, 100,\n          // Default total\n          'alt-' + questionNum);\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n      if (questions.length > 0) break; // Use first successful pattern\n    }\n    return questions;\n  }\n  extractGroupQuestions(content, startQ, endQ, totalQ, context) {\n    const questions = [];\n\n    // Split by individual question patterns within the group\n    const subQuestionPattern = /Question #(\\d+) - \\d+ of \\d+ Question ID: (\\d+)\\s*(.*?)(?=Question #\\d+|$)/gs;\n    let match;\n    while ((match = subQuestionPattern.exec(content)) !== null) {\n      const [, questionNum, questionId, questionContent] = match;\n      const questionData = this.parseSingleQuestion(questionContent, parseInt(questionNum), totalQ, questionId, context);\n      if (questionData) {\n        questions.push(questionData);\n      }\n    }\n    return questions;\n  }\n  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {\n    if (!content.trim()) return null;\n    console.log(`Parsing question ${questionNum}, content length: ${content.length}`);\n    console.log(`Content preview: ${content.substring(0, 200)}...`);\n\n    // Preserve line breaks for better parsing\n    const originalContent = content;\n    let questionText = '';\n    const choices = {};\n\n    // Find all choice markers in the content\n    const choiceMarkers = [];\n    const choicePattern = /([A-E])\\)\\s*/g;\n    let match;\n    while ((match = choicePattern.exec(content)) !== null) {\n      choiceMarkers.push({\n        choice: match[1],\n        index: match.index,\n        fullMatch: match[0]\n      });\n    }\n    console.log(`Found ${choiceMarkers.length} choice markers for question ${questionNum}`);\n    if (choiceMarkers.length >= 2) {\n      // Extract question text (everything before first choice)\n      questionText = content.substring(0, choiceMarkers[0].index).trim();\n\n      // Extract each choice\n      for (let i = 0; i < choiceMarkers.length; i++) {\n        const marker = choiceMarkers[i];\n        const nextMarker = choiceMarkers[i + 1];\n        const startIndex = marker.index + marker.fullMatch.length;\n        const endIndex = nextMarker ? nextMarker.index : content.length;\n        const choiceText = content.substring(startIndex, endIndex).trim();\n        if (choiceText.length > 0) {\n          choices[marker.choice] = choiceText;\n        }\n      }\n    } else {\n      // Fallback: try to parse line by line\n      console.log('Using line-by-line parsing for question', questionNum);\n      const lines = originalContent.split('\\n').map(line => line.trim()).filter(line => line);\n      let currentChoice = null;\n      let choiceText = '';\n      let inChoices = false;\n      for (const line of lines) {\n        // Check if line starts with choice pattern\n        const choiceMatch = line.match(/^([A-E])\\)\\s*(.*)/);\n        if (choiceMatch) {\n          // Save previous choice\n          if (currentChoice) {\n            choices[currentChoice] = choiceText.trim();\n          }\n          currentChoice = choiceMatch[1];\n          choiceText = choiceMatch[2];\n          inChoices = true;\n        } else if (currentChoice && inChoices) {\n          // Continue choice text\n          choiceText += ' ' + line;\n        } else if (!inChoices) {\n          // Question text\n          questionText += ' ' + line;\n        }\n      }\n\n      // Save last choice\n      if (currentChoice) {\n        choices[currentChoice] = choiceText.trim();\n      }\n    }\n\n    // Clean up question text\n    questionText = questionText.replace(/\\s+/g, ' ').trim();\n\n    // Clean up choices\n    Object.keys(choices).forEach(key => {\n      choices[key] = choices[key].replace(/\\s+/g, ' ').trim();\n    });\n    console.log(`Question ${questionNum} parsed:`, {\n      questionTextLength: questionText.length,\n      choicesCount: Object.keys(choices).length,\n      choices: Object.keys(choices)\n    });\n\n    // Validate we have minimum required content\n    if (!questionText.trim() && Object.keys(choices).length === 0) {\n      console.log(`Question ${questionNum}: No content found`);\n      return null;\n    }\n\n    // Ensure we have at least some choices\n    if (Object.keys(choices).length === 0) {\n      console.log(`Question ${questionNum}: No choices found, creating placeholders`);\n      choices['A'] = 'Choice A (parsing failed)';\n      choices['B'] = 'Choice B (parsing failed)';\n      choices['C'] = 'Choice C (parsing failed)';\n    }\n\n    // Ensure we have question text\n    if (!questionText.trim()) {\n      questionText = `Question ${questionNum} (content parsing incomplete)`;\n    }\n    return {\n      questionNumber: questionNum,\n      totalQuestions: totalQ,\n      questionId: questionId,\n      context: context,\n      questionText: questionText,\n      choices: choices,\n      isGroupQuestion: Boolean(context)\n    };\n  }\n  extractAnswers(text) {\n    const answers = {};\n    console.log('Extracting answers from text...');\n\n    // Find all question markers in answer file\n    const questionMarkers = [];\n    const patterns = [/Question #(\\d+) of (\\d+) Question ID: (\\d+)/g, /Question #(\\d+)/g];\n    for (const pattern of patterns) {\n      let match;\n      while ((match = pattern.exec(text)) !== null) {\n        questionMarkers.push({\n          index: match.index,\n          fullMatch: match[0],\n          questionNum: parseInt(match[1])\n        });\n      }\n    }\n    console.log(`Found ${questionMarkers.length} question markers in answer file`);\n\n    // Sort by position\n    questionMarkers.sort((a, b) => a.index - b.index);\n\n    // Extract answer content for each question\n    for (let i = 0; i < questionMarkers.length; i++) {\n      const marker = questionMarkers[i];\n      const nextMarker = questionMarkers[i + 1];\n\n      // Get content from current marker to next marker (or end of text)\n      const startIndex = marker.index + marker.fullMatch.length;\n      const endIndex = nextMarker ? nextMarker.index : text.length;\n      const content = text.substring(startIndex, endIndex).trim();\n      console.log(`Processing answer for question ${marker.questionNum}, content length: ${content.length}`);\n\n      // Find correct answer\n      let correctAnswer = null;\n      const answerPatterns = [/The correct answer is ([A-E])\\./i, /The correct answer is ([A-E])\\s/i, /Correct answer:\\s*([A-E])/i, /Answer:\\s*([A-E])/i, /([A-E])\\s*is correct/i, /Choice\\s*([A-E])\\s*is correct/i];\n      for (const pattern of answerPatterns) {\n        const match = content.match(pattern);\n        if (match) {\n          correctAnswer = match[1].toUpperCase();\n          console.log(`Found correct answer ${correctAnswer} for question ${marker.questionNum}`);\n          break;\n        }\n      }\n\n      // Find explanation - be more flexible\n      let explanation = '';\n      const explanationPatterns = [/Explanation[:\\s]+(.*?)(?=Question #|\\(Module|\\Z)/is, /Explanation[:\\s]+(.*?)(?=\\n\\s*\\n|\\Z)/is, /Explanation[:\\s]+(.*)/is];\n      for (const pattern of explanationPatterns) {\n        const match = content.match(pattern);\n        if (match) {\n          explanation = match[1].trim();\n          // Clean up explanation\n          explanation = explanation.replace(/\\s+/g, ' ');\n          // Remove common suffixes\n          explanation = explanation.replace(/\\(Module.*?\\)$/i, '');\n          explanation = explanation.replace(/\\(LOS.*?\\)$/i, '');\n          explanation = explanation.trim();\n          console.log(`Found explanation for question ${marker.questionNum}, length: ${explanation.length}`);\n          break;\n        }\n      }\n\n      // If no explanation found, try to extract everything after \"Explanation\"\n      if (!explanation && content.toLowerCase().includes('explanation')) {\n        const explIndex = content.toLowerCase().indexOf('explanation');\n        if (explIndex !== -1) {\n          explanation = content.substring(explIndex + 11).trim();\n          explanation = explanation.replace(/\\s+/g, ' ');\n          console.log(`Fallback explanation extraction for question ${marker.questionNum}`);\n        }\n      }\n      if (correctAnswer) {\n        answers[marker.questionNum] = {\n          correctAnswer: correctAnswer,\n          explanation: explanation || 'No explanation available'\n        };\n      } else {\n        console.log(`No correct answer found for question ${marker.questionNum}`);\n      }\n    }\n    console.log(`Extracted answers for ${Object.keys(answers).length} questions`);\n    return answers;\n  }\n  async parseFiles(questionFile, answerFile) {\n    try {\n      const [questions, answers] = await Promise.all([this.parseQuestionFile(questionFile), this.parseAnswerFile(answerFile)]);\n      return {\n        questions,\n        answers\n      };\n    } catch (error) {\n      console.error('Error parsing files:', error);\n      throw error;\n    }\n  }\n}\nexport default CFAQuestionParser;", "map": {"version": 3, "names": ["pdfjsLib", "GlobalWorkerOptions", "workerSrc", "CFAQuestionParser", "constructor", "questions", "answers", "parseQuestionFile", "file", "arrayBuffer", "pdf", "getDocument", "promise", "fullText", "i", "numPages", "page", "getPage", "textContent", "getTextContent", "pageText", "lastY", "item", "items", "Math", "abs", "transform", "str", "console", "log", "length", "substring", "extractQuestions", "error", "parseAnswerFile", "extractAnswers", "Object", "keys", "text", "questionMarkers", "patterns", "pattern", "match", "exec", "push", "index", "fullMatch", "questionNum", "parseInt", "endNum", "totalQ", "questionId", "isGroup", "includes", "extractQuestionsAlternative", "sort", "a", "b", "marker", "nextM<PERSON><PERSON>", "startIndex", "endIndex", "content", "trim", "groupQuestions", "extractGroupQuestions", "questionData", "parseSingleQuestion", "matches", "matchAll", "startQ", "endQ", "context", "subQuestionPattern", "questionContent", "originalContent", "questionText", "choices", "choiceMarkers", "choicePattern", "choice", "choiceText", "lines", "split", "map", "line", "filter", "currentChoice", "inChoices", "choiceMatch", "replace", "for<PERSON>ach", "key", "questionTextLength", "choicesCount", "questionNumber", "totalQuestions", "isGroupQuestion", "Boolean", "<PERSON><PERSON><PERSON><PERSON>", "answerPatterns", "toUpperCase", "explanation", "explanationPatterns", "toLowerCase", "explIndex", "indexOf", "parseFiles", "questionFile", "answerFile", "Promise", "all"], "sources": ["/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/utils/pdfParser.js"], "sourcesContent": ["// PDF Parser for CFA Questions and Answers\nimport * as pdfjsLib from 'pdfjs-dist';\n\n// Set worker path - use the worker from the same version\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;\n\nclass CFAQuestionParser {\n  constructor() {\n    this.questions = [];\n    this.answers = {};\n  }\n\n  async parseQuestionFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n\n        fullText += pageText + '\\n\\n';\n      }\n\n      // Debug: Log extracted text\n      console.log('Extracted text length:', fullText.length);\n      console.log('First 2000 characters:', fullText.substring(0, 2000));\n\n      // Parse questions from text\n      const questions = this.extractQuestions(fullText);\n      console.log('Extracted questions:', questions.length);\n\n      return questions;\n    } catch (error) {\n      console.error('Error parsing question file:', error);\n      throw error;\n    }\n  }\n\n  async parseAnswerFile(file) {\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;\n\n      let fullText = '';\n\n      // Extract text from all pages with better formatting\n      for (let i = 1; i <= pdf.numPages; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n\n        // Better text extraction preserving structure\n        let pageText = '';\n        let lastY = null;\n\n        for (const item of textContent.items) {\n          // Add line break if Y position changed significantly (new line)\n          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {\n            pageText += '\\n';\n          }\n\n          pageText += item.str + ' ';\n          lastY = item.transform[5];\n        }\n\n        fullText += pageText + '\\n\\n';\n      }\n\n      console.log('Answer file text length:', fullText.length);\n      console.log('Answer file preview:', fullText.substring(0, 1000));\n\n      // Parse answers from text\n      const answers = this.extractAnswers(fullText);\n      console.log('Extracted answers:', Object.keys(answers).length);\n\n      return answers;\n    } catch (error) {\n      console.error('Error parsing answer file:', error);\n      throw error;\n    }\n  }\n\n  extractQuestions(text) {\n    const questions = [];\n\n    console.log('Looking for question patterns in text...');\n\n    // First, try to find all question markers\n    const questionMarkers = [];\n    const patterns = [\n      /Question #(\\d+) of (\\d+) Question ID: (\\d+)/g,\n      /Question #(\\d+) - (\\d+) of (\\d+) Question ID: (\\d+)/g,\n    ];\n\n    for (const pattern of patterns) {\n      let match;\n      while ((match = pattern.exec(text)) !== null) {\n        questionMarkers.push({\n          index: match.index,\n          fullMatch: match[0],\n          questionNum: parseInt(match[1]),\n          endNum: match[2] ? parseInt(match[2]) : null,\n          totalQ: parseInt(match[match.length - 2]),\n          questionId: match[match.length - 1],\n          isGroup: match[0].includes(' - ')\n        });\n      }\n    }\n\n    console.log(`Found ${questionMarkers.length} question markers`);\n\n    if (questionMarkers.length === 0) {\n      console.log('No question patterns found. Trying alternative extraction...');\n      return this.extractQuestionsAlternative(text);\n    }\n\n    // Sort by position in text\n    questionMarkers.sort((a, b) => a.index - b.index);\n\n    // Extract content for each question\n    for (let i = 0; i < questionMarkers.length; i++) {\n      const marker = questionMarkers[i];\n      const nextMarker = questionMarkers[i + 1];\n\n      // Get content from current marker to next marker (or end of text)\n      const startIndex = marker.index + marker.fullMatch.length;\n      const endIndex = nextMarker ? nextMarker.index : text.length;\n      const content = text.substring(startIndex, endIndex).trim();\n\n      console.log(`Processing question ${marker.questionNum}, content length: ${content.length}`);\n\n      if (marker.isGroup) {\n        // Handle group questions\n        const groupQuestions = this.extractGroupQuestions(\n          content,\n          marker.questionNum,\n          marker.endNum,\n          marker.totalQ,\n          '' // Context will be extracted within the method\n        );\n        questions.push(...groupQuestions);\n      } else {\n        // Handle single question\n        const questionData = this.parseSingleQuestion(\n          content,\n          marker.questionNum,\n          marker.totalQ,\n          marker.questionId\n        );\n\n        if (questionData) {\n          questions.push(questionData);\n        }\n      }\n    }\n\n    console.log(`Extracted ${questions.length} questions total`);\n    return questions;\n  }\n\n  extractQuestionsAlternative(text) {\n    console.log('Using alternative extraction method...');\n    const questions = [];\n\n    // Look for any numbered patterns that might be questions\n    const patterns = [\n      /(\\d+)\\.\\s+([^0-9]+?)(?=\\d+\\.|$)/gs,  // 1. Question text\n      /Question\\s+(\\d+)[:\\.]?\\s*([^Q]+?)(?=Question\\s+\\d+|$)/gis,  // Question 1: text\n    ];\n\n    for (const pattern of patterns) {\n      const matches = [...text.matchAll(pattern)];\n      console.log(`Alternative pattern found ${matches.length} matches`);\n\n      for (const match of matches) {\n        const questionNum = parseInt(match[1]);\n        const content = match[2].trim();\n\n        if (content.length > 20) { // Filter out very short matches\n          const questionData = this.parseSingleQuestion(\n            content,\n            questionNum,\n            100, // Default total\n            'alt-' + questionNum\n          );\n\n          if (questionData) {\n            questions.push(questionData);\n          }\n        }\n      }\n\n      if (questions.length > 0) break; // Use first successful pattern\n    }\n\n    return questions;\n  }\n\n  extractGroupQuestions(content, startQ, endQ, totalQ, context) {\n    const questions = [];\n    \n    // Split by individual question patterns within the group\n    const subQuestionPattern = /Question #(\\d+) - \\d+ of \\d+ Question ID: (\\d+)\\s*(.*?)(?=Question #\\d+|$)/gs;\n    let match;\n    \n    while ((match = subQuestionPattern.exec(content)) !== null) {\n      const [, questionNum, questionId, questionContent] = match;\n      \n      const questionData = this.parseSingleQuestion(\n        questionContent,\n        parseInt(questionNum),\n        totalQ,\n        questionId,\n        context\n      );\n      \n      if (questionData) {\n        questions.push(questionData);\n      }\n    }\n    \n    return questions;\n  }\n\n  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {\n    if (!content.trim()) return null;\n\n    console.log(`Parsing question ${questionNum}, content length: ${content.length}`);\n    console.log(`Content preview: ${content.substring(0, 200)}...`);\n\n    // Preserve line breaks for better parsing\n    const originalContent = content;\n\n    let questionText = '';\n    const choices = {};\n\n    // Find all choice markers in the content\n    const choiceMarkers = [];\n    const choicePattern = /([A-E])\\)\\s*/g;\n    let match;\n\n    while ((match = choicePattern.exec(content)) !== null) {\n      choiceMarkers.push({\n        choice: match[1],\n        index: match.index,\n        fullMatch: match[0]\n      });\n    }\n\n    console.log(`Found ${choiceMarkers.length} choice markers for question ${questionNum}`);\n\n    if (choiceMarkers.length >= 2) {\n      // Extract question text (everything before first choice)\n      questionText = content.substring(0, choiceMarkers[0].index).trim();\n\n      // Extract each choice\n      for (let i = 0; i < choiceMarkers.length; i++) {\n        const marker = choiceMarkers[i];\n        const nextMarker = choiceMarkers[i + 1];\n\n        const startIndex = marker.index + marker.fullMatch.length;\n        const endIndex = nextMarker ? nextMarker.index : content.length;\n        const choiceText = content.substring(startIndex, endIndex).trim();\n\n        if (choiceText.length > 0) {\n          choices[marker.choice] = choiceText;\n        }\n      }\n    } else {\n      // Fallback: try to parse line by line\n      console.log('Using line-by-line parsing for question', questionNum);\n\n      const lines = originalContent.split('\\n').map(line => line.trim()).filter(line => line);\n\n      let currentChoice = null;\n      let choiceText = '';\n      let inChoices = false;\n\n      for (const line of lines) {\n        // Check if line starts with choice pattern\n        const choiceMatch = line.match(/^([A-E])\\)\\s*(.*)/);\n\n        if (choiceMatch) {\n          // Save previous choice\n          if (currentChoice) {\n            choices[currentChoice] = choiceText.trim();\n          }\n\n          currentChoice = choiceMatch[1];\n          choiceText = choiceMatch[2];\n          inChoices = true;\n        } else if (currentChoice && inChoices) {\n          // Continue choice text\n          choiceText += ' ' + line;\n        } else if (!inChoices) {\n          // Question text\n          questionText += ' ' + line;\n        }\n      }\n\n      // Save last choice\n      if (currentChoice) {\n        choices[currentChoice] = choiceText.trim();\n      }\n    }\n\n    // Clean up question text\n    questionText = questionText.replace(/\\s+/g, ' ').trim();\n\n    // Clean up choices\n    Object.keys(choices).forEach(key => {\n      choices[key] = choices[key].replace(/\\s+/g, ' ').trim();\n    });\n\n    console.log(`Question ${questionNum} parsed:`, {\n      questionTextLength: questionText.length,\n      choicesCount: Object.keys(choices).length,\n      choices: Object.keys(choices)\n    });\n\n    // Validate we have minimum required content\n    if (!questionText.trim() && Object.keys(choices).length === 0) {\n      console.log(`Question ${questionNum}: No content found`);\n      return null;\n    }\n\n    // Ensure we have at least some choices\n    if (Object.keys(choices).length === 0) {\n      console.log(`Question ${questionNum}: No choices found, creating placeholders`);\n      choices['A'] = 'Choice A (parsing failed)';\n      choices['B'] = 'Choice B (parsing failed)';\n      choices['C'] = 'Choice C (parsing failed)';\n    }\n\n    // Ensure we have question text\n    if (!questionText.trim()) {\n      questionText = `Question ${questionNum} (content parsing incomplete)`;\n    }\n\n    return {\n      questionNumber: questionNum,\n      totalQuestions: totalQ,\n      questionId: questionId,\n      context: context,\n      questionText: questionText,\n      choices: choices,\n      isGroupQuestion: Boolean(context)\n    };\n  }\n\n  extractAnswers(text) {\n    const answers = {};\n\n    console.log('Extracting answers from text...');\n\n    // Find all question markers in answer file\n    const questionMarkers = [];\n    const patterns = [\n      /Question #(\\d+) of (\\d+) Question ID: (\\d+)/g,\n      /Question #(\\d+)/g\n    ];\n\n    for (const pattern of patterns) {\n      let match;\n      while ((match = pattern.exec(text)) !== null) {\n        questionMarkers.push({\n          index: match.index,\n          fullMatch: match[0],\n          questionNum: parseInt(match[1])\n        });\n      }\n    }\n\n    console.log(`Found ${questionMarkers.length} question markers in answer file`);\n\n    // Sort by position\n    questionMarkers.sort((a, b) => a.index - b.index);\n\n    // Extract answer content for each question\n    for (let i = 0; i < questionMarkers.length; i++) {\n      const marker = questionMarkers[i];\n      const nextMarker = questionMarkers[i + 1];\n\n      // Get content from current marker to next marker (or end of text)\n      const startIndex = marker.index + marker.fullMatch.length;\n      const endIndex = nextMarker ? nextMarker.index : text.length;\n      const content = text.substring(startIndex, endIndex).trim();\n\n      console.log(`Processing answer for question ${marker.questionNum}, content length: ${content.length}`);\n\n      // Find correct answer\n      let correctAnswer = null;\n      const answerPatterns = [\n        /The correct answer is ([A-E])\\./i,\n        /The correct answer is ([A-E])\\s/i,\n        /Correct answer:\\s*([A-E])/i,\n        /Answer:\\s*([A-E])/i,\n        /([A-E])\\s*is correct/i,\n        /Choice\\s*([A-E])\\s*is correct/i\n      ];\n\n      for (const pattern of answerPatterns) {\n        const match = content.match(pattern);\n        if (match) {\n          correctAnswer = match[1].toUpperCase();\n          console.log(`Found correct answer ${correctAnswer} for question ${marker.questionNum}`);\n          break;\n        }\n      }\n\n      // Find explanation - be more flexible\n      let explanation = '';\n      const explanationPatterns = [\n        /Explanation[:\\s]+(.*?)(?=Question #|\\(Module|\\Z)/is,\n        /Explanation[:\\s]+(.*?)(?=\\n\\s*\\n|\\Z)/is,\n        /Explanation[:\\s]+(.*)/is\n      ];\n\n      for (const pattern of explanationPatterns) {\n        const match = content.match(pattern);\n        if (match) {\n          explanation = match[1].trim();\n          // Clean up explanation\n          explanation = explanation.replace(/\\s+/g, ' ');\n          // Remove common suffixes\n          explanation = explanation.replace(/\\(Module.*?\\)$/i, '');\n          explanation = explanation.replace(/\\(LOS.*?\\)$/i, '');\n          explanation = explanation.trim();\n\n          console.log(`Found explanation for question ${marker.questionNum}, length: ${explanation.length}`);\n          break;\n        }\n      }\n\n      // If no explanation found, try to extract everything after \"Explanation\"\n      if (!explanation && content.toLowerCase().includes('explanation')) {\n        const explIndex = content.toLowerCase().indexOf('explanation');\n        if (explIndex !== -1) {\n          explanation = content.substring(explIndex + 11).trim();\n          explanation = explanation.replace(/\\s+/g, ' ');\n          console.log(`Fallback explanation extraction for question ${marker.questionNum}`);\n        }\n      }\n\n      if (correctAnswer) {\n        answers[marker.questionNum] = {\n          correctAnswer: correctAnswer,\n          explanation: explanation || 'No explanation available'\n        };\n      } else {\n        console.log(`No correct answer found for question ${marker.questionNum}`);\n      }\n    }\n\n    console.log(`Extracted answers for ${Object.keys(answers).length} questions`);\n    return answers;\n  }\n\n  async parseFiles(questionFile, answerFile) {\n    try {\n      const [questions, answers] = await Promise.all([\n        this.parseQuestionFile(questionFile),\n        this.parseAnswerFile(answerFile)\n      ]);\n      \n      return { questions, answers };\n    } catch (error) {\n      console.error('Error parsing files:', error);\n      throw error;\n    }\n  }\n}\n\nexport default CFAQuestionParser;\n"], "mappings": "AAAA;AACA,OAAO,KAAKA,QAAQ,MAAM,YAAY;;AAEtC;AACAA,QAAQ,CAACC,mBAAmB,CAACC,SAAS,GAAG,+DAA+D;AAExG,MAAMC,iBAAiB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;EACnB;EAEA,MAAMC,iBAAiBA,CAACC,IAAI,EAAE;IAC5B,IAAI;MACF,MAAMC,WAAW,GAAG,MAAMD,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5C,MAAMC,GAAG,GAAG,MAAMV,QAAQ,CAACW,WAAW,CAACF,WAAW,CAAC,CAACG,OAAO;MAE3D,IAAIC,QAAQ,GAAG,EAAE;;MAEjB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,GAAG,CAACK,QAAQ,EAAED,CAAC,EAAE,EAAE;QACtC,MAAME,IAAI,GAAG,MAAMN,GAAG,CAACO,OAAO,CAACH,CAAC,CAAC;QACjC,MAAMI,WAAW,GAAG,MAAMF,IAAI,CAACG,cAAc,CAAC,CAAC;;QAE/C;QACA,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAIC,KAAK,GAAG,IAAI;QAEhB,KAAK,MAAMC,IAAI,IAAIJ,WAAW,CAACK,KAAK,EAAE;UACpC;UACA,IAAIF,KAAK,KAAK,IAAI,IAAIG,IAAI,CAACC,GAAG,CAACH,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,GAAGL,KAAK,CAAC,GAAG,CAAC,EAAE;YAC7DD,QAAQ,IAAI,IAAI;UAClB;UAEAA,QAAQ,IAAIE,IAAI,CAACK,GAAG,GAAG,GAAG;UAC1BN,KAAK,GAAGC,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC;QAC3B;QAEAb,QAAQ,IAAIO,QAAQ,GAAG,MAAM;MAC/B;;MAEA;MACAQ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEhB,QAAQ,CAACiB,MAAM,CAAC;MACtDF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEhB,QAAQ,CAACkB,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;;MAElE;MACA,MAAM1B,SAAS,GAAG,IAAI,CAAC2B,gBAAgB,CAACnB,QAAQ,CAAC;MACjDe,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAExB,SAAS,CAACyB,MAAM,CAAC;MAErD,OAAOzB,SAAS;IAClB,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;EAEA,MAAMC,eAAeA,CAAC1B,IAAI,EAAE;IAC1B,IAAI;MACF,MAAMC,WAAW,GAAG,MAAMD,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5C,MAAMC,GAAG,GAAG,MAAMV,QAAQ,CAACW,WAAW,CAACF,WAAW,CAAC,CAACG,OAAO;MAE3D,IAAIC,QAAQ,GAAG,EAAE;;MAEjB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,GAAG,CAACK,QAAQ,EAAED,CAAC,EAAE,EAAE;QACtC,MAAME,IAAI,GAAG,MAAMN,GAAG,CAACO,OAAO,CAACH,CAAC,CAAC;QACjC,MAAMI,WAAW,GAAG,MAAMF,IAAI,CAACG,cAAc,CAAC,CAAC;;QAE/C;QACA,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAIC,KAAK,GAAG,IAAI;QAEhB,KAAK,MAAMC,IAAI,IAAIJ,WAAW,CAACK,KAAK,EAAE;UACpC;UACA,IAAIF,KAAK,KAAK,IAAI,IAAIG,IAAI,CAACC,GAAG,CAACH,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,GAAGL,KAAK,CAAC,GAAG,CAAC,EAAE;YAC7DD,QAAQ,IAAI,IAAI;UAClB;UAEAA,QAAQ,IAAIE,IAAI,CAACK,GAAG,GAAG,GAAG;UAC1BN,KAAK,GAAGC,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC;QAC3B;QAEAb,QAAQ,IAAIO,QAAQ,GAAG,MAAM;MAC/B;MAEAQ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEhB,QAAQ,CAACiB,MAAM,CAAC;MACxDF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEhB,QAAQ,CAACkB,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;;MAEhE;MACA,MAAMzB,OAAO,GAAG,IAAI,CAAC6B,cAAc,CAACtB,QAAQ,CAAC;MAC7Ce,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,MAAM,CAACC,IAAI,CAAC/B,OAAO,CAAC,CAACwB,MAAM,CAAC;MAE9D,OAAOxB,OAAO;IAChB,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF;EAEAD,gBAAgBA,CAACM,IAAI,EAAE;IACrB,MAAMjC,SAAS,GAAG,EAAE;IAEpBuB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;IAEvD;IACA,MAAMU,eAAe,GAAG,EAAE;IAC1B,MAAMC,QAAQ,GAAG,CACf,8CAA8C,EAC9C,sDAAsD,CACvD;IAED,KAAK,MAAMC,OAAO,IAAID,QAAQ,EAAE;MAC9B,IAAIE,KAAK;MACT,OAAO,CAACA,KAAK,GAAGD,OAAO,CAACE,IAAI,CAACL,IAAI,CAAC,MAAM,IAAI,EAAE;QAC5CC,eAAe,CAACK,IAAI,CAAC;UACnBC,KAAK,EAAEH,KAAK,CAACG,KAAK;UAClBC,SAAS,EAAEJ,KAAK,CAAC,CAAC,CAAC;UACnBK,WAAW,EAAEC,QAAQ,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC;UAC/BO,MAAM,EAAEP,KAAK,CAAC,CAAC,CAAC,GAAGM,QAAQ,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;UAC5CQ,MAAM,EAAEF,QAAQ,CAACN,KAAK,CAACA,KAAK,CAACZ,MAAM,GAAG,CAAC,CAAC,CAAC;UACzCqB,UAAU,EAAET,KAAK,CAACA,KAAK,CAACZ,MAAM,GAAG,CAAC,CAAC;UACnCsB,OAAO,EAAEV,KAAK,CAAC,CAAC,CAAC,CAACW,QAAQ,CAAC,KAAK;QAClC,CAAC,CAAC;MACJ;IACF;IAEAzB,OAAO,CAACC,GAAG,CAAC,SAASU,eAAe,CAACT,MAAM,mBAAmB,CAAC;IAE/D,IAAIS,eAAe,CAACT,MAAM,KAAK,CAAC,EAAE;MAChCF,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;MAC3E,OAAO,IAAI,CAACyB,2BAA2B,CAAChB,IAAI,CAAC;IAC/C;;IAEA;IACAC,eAAe,CAACgB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACX,KAAK,GAAGY,CAAC,CAACZ,KAAK,CAAC;;IAEjD;IACA,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,eAAe,CAACT,MAAM,EAAEhB,CAAC,EAAE,EAAE;MAC/C,MAAM4C,MAAM,GAAGnB,eAAe,CAACzB,CAAC,CAAC;MACjC,MAAM6C,UAAU,GAAGpB,eAAe,CAACzB,CAAC,GAAG,CAAC,CAAC;;MAEzC;MACA,MAAM8C,UAAU,GAAGF,MAAM,CAACb,KAAK,GAAGa,MAAM,CAACZ,SAAS,CAAChB,MAAM;MACzD,MAAM+B,QAAQ,GAAGF,UAAU,GAAGA,UAAU,CAACd,KAAK,GAAGP,IAAI,CAACR,MAAM;MAC5D,MAAMgC,OAAO,GAAGxB,IAAI,CAACP,SAAS,CAAC6B,UAAU,EAAEC,QAAQ,CAAC,CAACE,IAAI,CAAC,CAAC;MAE3DnC,OAAO,CAACC,GAAG,CAAC,uBAAuB6B,MAAM,CAACX,WAAW,qBAAqBe,OAAO,CAAChC,MAAM,EAAE,CAAC;MAE3F,IAAI4B,MAAM,CAACN,OAAO,EAAE;QAClB;QACA,MAAMY,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAC/CH,OAAO,EACPJ,MAAM,CAACX,WAAW,EAClBW,MAAM,CAACT,MAAM,EACbS,MAAM,CAACR,MAAM,EACb,EAAE,CAAC;QACL,CAAC;QACD7C,SAAS,CAACuC,IAAI,CAAC,GAAGoB,cAAc,CAAC;MACnC,CAAC,MAAM;QACL;QACA,MAAME,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CL,OAAO,EACPJ,MAAM,CAACX,WAAW,EAClBW,MAAM,CAACR,MAAM,EACbQ,MAAM,CAACP,UACT,CAAC;QAED,IAAIe,YAAY,EAAE;UAChB7D,SAAS,CAACuC,IAAI,CAACsB,YAAY,CAAC;QAC9B;MACF;IACF;IAEAtC,OAAO,CAACC,GAAG,CAAC,aAAaxB,SAAS,CAACyB,MAAM,kBAAkB,CAAC;IAC5D,OAAOzB,SAAS;EAClB;EAEAiD,2BAA2BA,CAAChB,IAAI,EAAE;IAChCV,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACrD,MAAMxB,SAAS,GAAG,EAAE;;IAEpB;IACA,MAAMmC,QAAQ,GAAG,CACf,mCAAmC;IAAG;IACtC,0DAA0D,CAAG;IAAA,CAC9D;IAED,KAAK,MAAMC,OAAO,IAAID,QAAQ,EAAE;MAC9B,MAAM4B,OAAO,GAAG,CAAC,GAAG9B,IAAI,CAAC+B,QAAQ,CAAC5B,OAAO,CAAC,CAAC;MAC3Cb,OAAO,CAACC,GAAG,CAAC,6BAA6BuC,OAAO,CAACtC,MAAM,UAAU,CAAC;MAElE,KAAK,MAAMY,KAAK,IAAI0B,OAAO,EAAE;QAC3B,MAAMrB,WAAW,GAAGC,QAAQ,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC;QACtC,MAAMoB,OAAO,GAAGpB,KAAK,CAAC,CAAC,CAAC,CAACqB,IAAI,CAAC,CAAC;QAE/B,IAAID,OAAO,CAAChC,MAAM,GAAG,EAAE,EAAE;UAAE;UACzB,MAAMoC,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CL,OAAO,EACPf,WAAW,EACX,GAAG;UAAE;UACL,MAAM,GAAGA,WACX,CAAC;UAED,IAAImB,YAAY,EAAE;YAChB7D,SAAS,CAACuC,IAAI,CAACsB,YAAY,CAAC;UAC9B;QACF;MACF;MAEA,IAAI7D,SAAS,CAACyB,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC;IACnC;IAEA,OAAOzB,SAAS;EAClB;EAEA4D,qBAAqBA,CAACH,OAAO,EAAEQ,MAAM,EAAEC,IAAI,EAAErB,MAAM,EAAEsB,OAAO,EAAE;IAC5D,MAAMnE,SAAS,GAAG,EAAE;;IAEpB;IACA,MAAMoE,kBAAkB,GAAG,8EAA8E;IACzG,IAAI/B,KAAK;IAET,OAAO,CAACA,KAAK,GAAG+B,kBAAkB,CAAC9B,IAAI,CAACmB,OAAO,CAAC,MAAM,IAAI,EAAE;MAC1D,MAAM,GAAGf,WAAW,EAAEI,UAAU,EAAEuB,eAAe,CAAC,GAAGhC,KAAK;MAE1D,MAAMwB,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CO,eAAe,EACf1B,QAAQ,CAACD,WAAW,CAAC,EACrBG,MAAM,EACNC,UAAU,EACVqB,OACF,CAAC;MAED,IAAIN,YAAY,EAAE;QAChB7D,SAAS,CAACuC,IAAI,CAACsB,YAAY,CAAC;MAC9B;IACF;IAEA,OAAO7D,SAAS;EAClB;EAEA8D,mBAAmBA,CAACL,OAAO,EAAEf,WAAW,EAAEG,MAAM,EAAEC,UAAU,EAAEqB,OAAO,GAAG,EAAE,EAAE;IAC1E,IAAI,CAACV,OAAO,CAACC,IAAI,CAAC,CAAC,EAAE,OAAO,IAAI;IAEhCnC,OAAO,CAACC,GAAG,CAAC,oBAAoBkB,WAAW,qBAAqBe,OAAO,CAAChC,MAAM,EAAE,CAAC;IACjFF,OAAO,CAACC,GAAG,CAAC,oBAAoBiC,OAAO,CAAC/B,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;;IAE/D;IACA,MAAM4C,eAAe,GAAGb,OAAO;IAE/B,IAAIc,YAAY,GAAG,EAAE;IACrB,MAAMC,OAAO,GAAG,CAAC,CAAC;;IAElB;IACA,MAAMC,aAAa,GAAG,EAAE;IACxB,MAAMC,aAAa,GAAG,eAAe;IACrC,IAAIrC,KAAK;IAET,OAAO,CAACA,KAAK,GAAGqC,aAAa,CAACpC,IAAI,CAACmB,OAAO,CAAC,MAAM,IAAI,EAAE;MACrDgB,aAAa,CAAClC,IAAI,CAAC;QACjBoC,MAAM,EAAEtC,KAAK,CAAC,CAAC,CAAC;QAChBG,KAAK,EAAEH,KAAK,CAACG,KAAK;QAClBC,SAAS,EAAEJ,KAAK,CAAC,CAAC;MACpB,CAAC,CAAC;IACJ;IAEAd,OAAO,CAACC,GAAG,CAAC,SAASiD,aAAa,CAAChD,MAAM,gCAAgCiB,WAAW,EAAE,CAAC;IAEvF,IAAI+B,aAAa,CAAChD,MAAM,IAAI,CAAC,EAAE;MAC7B;MACA8C,YAAY,GAAGd,OAAO,CAAC/B,SAAS,CAAC,CAAC,EAAE+C,aAAa,CAAC,CAAC,CAAC,CAACjC,KAAK,CAAC,CAACkB,IAAI,CAAC,CAAC;;MAElE;MACA,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgE,aAAa,CAAChD,MAAM,EAAEhB,CAAC,EAAE,EAAE;QAC7C,MAAM4C,MAAM,GAAGoB,aAAa,CAAChE,CAAC,CAAC;QAC/B,MAAM6C,UAAU,GAAGmB,aAAa,CAAChE,CAAC,GAAG,CAAC,CAAC;QAEvC,MAAM8C,UAAU,GAAGF,MAAM,CAACb,KAAK,GAAGa,MAAM,CAACZ,SAAS,CAAChB,MAAM;QACzD,MAAM+B,QAAQ,GAAGF,UAAU,GAAGA,UAAU,CAACd,KAAK,GAAGiB,OAAO,CAAChC,MAAM;QAC/D,MAAMmD,UAAU,GAAGnB,OAAO,CAAC/B,SAAS,CAAC6B,UAAU,EAAEC,QAAQ,CAAC,CAACE,IAAI,CAAC,CAAC;QAEjE,IAAIkB,UAAU,CAACnD,MAAM,GAAG,CAAC,EAAE;UACzB+C,OAAO,CAACnB,MAAM,CAACsB,MAAM,CAAC,GAAGC,UAAU;QACrC;MACF;IACF,CAAC,MAAM;MACL;MACArD,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEkB,WAAW,CAAC;MAEnE,MAAMmC,KAAK,GAAGP,eAAe,CAACQ,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACtB,IAAI,CAAC,CAAC,CAAC,CAACuB,MAAM,CAACD,IAAI,IAAIA,IAAI,CAAC;MAEvF,IAAIE,aAAa,GAAG,IAAI;MACxB,IAAIN,UAAU,GAAG,EAAE;MACnB,IAAIO,SAAS,GAAG,KAAK;MAErB,KAAK,MAAMH,IAAI,IAAIH,KAAK,EAAE;QACxB;QACA,MAAMO,WAAW,GAAGJ,IAAI,CAAC3C,KAAK,CAAC,mBAAmB,CAAC;QAEnD,IAAI+C,WAAW,EAAE;UACf;UACA,IAAIF,aAAa,EAAE;YACjBV,OAAO,CAACU,aAAa,CAAC,GAAGN,UAAU,CAAClB,IAAI,CAAC,CAAC;UAC5C;UAEAwB,aAAa,GAAGE,WAAW,CAAC,CAAC,CAAC;UAC9BR,UAAU,GAAGQ,WAAW,CAAC,CAAC,CAAC;UAC3BD,SAAS,GAAG,IAAI;QAClB,CAAC,MAAM,IAAID,aAAa,IAAIC,SAAS,EAAE;UACrC;UACAP,UAAU,IAAI,GAAG,GAAGI,IAAI;QAC1B,CAAC,MAAM,IAAI,CAACG,SAAS,EAAE;UACrB;UACAZ,YAAY,IAAI,GAAG,GAAGS,IAAI;QAC5B;MACF;;MAEA;MACA,IAAIE,aAAa,EAAE;QACjBV,OAAO,CAACU,aAAa,CAAC,GAAGN,UAAU,CAAClB,IAAI,CAAC,CAAC;MAC5C;IACF;;IAEA;IACAa,YAAY,GAAGA,YAAY,CAACc,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC3B,IAAI,CAAC,CAAC;;IAEvD;IACA3B,MAAM,CAACC,IAAI,CAACwC,OAAO,CAAC,CAACc,OAAO,CAACC,GAAG,IAAI;MAClCf,OAAO,CAACe,GAAG,CAAC,GAAGf,OAAO,CAACe,GAAG,CAAC,CAACF,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC3B,IAAI,CAAC,CAAC;IACzD,CAAC,CAAC;IAEFnC,OAAO,CAACC,GAAG,CAAC,YAAYkB,WAAW,UAAU,EAAE;MAC7C8C,kBAAkB,EAAEjB,YAAY,CAAC9C,MAAM;MACvCgE,YAAY,EAAE1D,MAAM,CAACC,IAAI,CAACwC,OAAO,CAAC,CAAC/C,MAAM;MACzC+C,OAAO,EAAEzC,MAAM,CAACC,IAAI,CAACwC,OAAO;IAC9B,CAAC,CAAC;;IAEF;IACA,IAAI,CAACD,YAAY,CAACb,IAAI,CAAC,CAAC,IAAI3B,MAAM,CAACC,IAAI,CAACwC,OAAO,CAAC,CAAC/C,MAAM,KAAK,CAAC,EAAE;MAC7DF,OAAO,CAACC,GAAG,CAAC,YAAYkB,WAAW,oBAAoB,CAAC;MACxD,OAAO,IAAI;IACb;;IAEA;IACA,IAAIX,MAAM,CAACC,IAAI,CAACwC,OAAO,CAAC,CAAC/C,MAAM,KAAK,CAAC,EAAE;MACrCF,OAAO,CAACC,GAAG,CAAC,YAAYkB,WAAW,2CAA2C,CAAC;MAC/E8B,OAAO,CAAC,GAAG,CAAC,GAAG,2BAA2B;MAC1CA,OAAO,CAAC,GAAG,CAAC,GAAG,2BAA2B;MAC1CA,OAAO,CAAC,GAAG,CAAC,GAAG,2BAA2B;IAC5C;;IAEA;IACA,IAAI,CAACD,YAAY,CAACb,IAAI,CAAC,CAAC,EAAE;MACxBa,YAAY,GAAG,YAAY7B,WAAW,+BAA+B;IACvE;IAEA,OAAO;MACLgD,cAAc,EAAEhD,WAAW;MAC3BiD,cAAc,EAAE9C,MAAM;MACtBC,UAAU,EAAEA,UAAU;MACtBqB,OAAO,EAAEA,OAAO;MAChBI,YAAY,EAAEA,YAAY;MAC1BC,OAAO,EAAEA,OAAO;MAChBoB,eAAe,EAAEC,OAAO,CAAC1B,OAAO;IAClC,CAAC;EACH;EAEArC,cAAcA,CAACG,IAAI,EAAE;IACnB,MAAMhC,OAAO,GAAG,CAAC,CAAC;IAElBsB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;;IAE9C;IACA,MAAMU,eAAe,GAAG,EAAE;IAC1B,MAAMC,QAAQ,GAAG,CACf,8CAA8C,EAC9C,kBAAkB,CACnB;IAED,KAAK,MAAMC,OAAO,IAAID,QAAQ,EAAE;MAC9B,IAAIE,KAAK;MACT,OAAO,CAACA,KAAK,GAAGD,OAAO,CAACE,IAAI,CAACL,IAAI,CAAC,MAAM,IAAI,EAAE;QAC5CC,eAAe,CAACK,IAAI,CAAC;UACnBC,KAAK,EAAEH,KAAK,CAACG,KAAK;UAClBC,SAAS,EAAEJ,KAAK,CAAC,CAAC,CAAC;UACnBK,WAAW,EAAEC,QAAQ,CAACN,KAAK,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC;MACJ;IACF;IAEAd,OAAO,CAACC,GAAG,CAAC,SAASU,eAAe,CAACT,MAAM,kCAAkC,CAAC;;IAE9E;IACAS,eAAe,CAACgB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACX,KAAK,GAAGY,CAAC,CAACZ,KAAK,CAAC;;IAEjD;IACA,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,eAAe,CAACT,MAAM,EAAEhB,CAAC,EAAE,EAAE;MAC/C,MAAM4C,MAAM,GAAGnB,eAAe,CAACzB,CAAC,CAAC;MACjC,MAAM6C,UAAU,GAAGpB,eAAe,CAACzB,CAAC,GAAG,CAAC,CAAC;;MAEzC;MACA,MAAM8C,UAAU,GAAGF,MAAM,CAACb,KAAK,GAAGa,MAAM,CAACZ,SAAS,CAAChB,MAAM;MACzD,MAAM+B,QAAQ,GAAGF,UAAU,GAAGA,UAAU,CAACd,KAAK,GAAGP,IAAI,CAACR,MAAM;MAC5D,MAAMgC,OAAO,GAAGxB,IAAI,CAACP,SAAS,CAAC6B,UAAU,EAAEC,QAAQ,CAAC,CAACE,IAAI,CAAC,CAAC;MAE3DnC,OAAO,CAACC,GAAG,CAAC,kCAAkC6B,MAAM,CAACX,WAAW,qBAAqBe,OAAO,CAAChC,MAAM,EAAE,CAAC;;MAEtG;MACA,IAAIqE,aAAa,GAAG,IAAI;MACxB,MAAMC,cAAc,GAAG,CACrB,kCAAkC,EAClC,kCAAkC,EAClC,4BAA4B,EAC5B,oBAAoB,EACpB,uBAAuB,EACvB,gCAAgC,CACjC;MAED,KAAK,MAAM3D,OAAO,IAAI2D,cAAc,EAAE;QACpC,MAAM1D,KAAK,GAAGoB,OAAO,CAACpB,KAAK,CAACD,OAAO,CAAC;QACpC,IAAIC,KAAK,EAAE;UACTyD,aAAa,GAAGzD,KAAK,CAAC,CAAC,CAAC,CAAC2D,WAAW,CAAC,CAAC;UACtCzE,OAAO,CAACC,GAAG,CAAC,wBAAwBsE,aAAa,iBAAiBzC,MAAM,CAACX,WAAW,EAAE,CAAC;UACvF;QACF;MACF;;MAEA;MACA,IAAIuD,WAAW,GAAG,EAAE;MACpB,MAAMC,mBAAmB,GAAG,CAC1B,oDAAoD,EACpD,wCAAwC,EACxC,yBAAyB,CAC1B;MAED,KAAK,MAAM9D,OAAO,IAAI8D,mBAAmB,EAAE;QACzC,MAAM7D,KAAK,GAAGoB,OAAO,CAACpB,KAAK,CAACD,OAAO,CAAC;QACpC,IAAIC,KAAK,EAAE;UACT4D,WAAW,GAAG5D,KAAK,CAAC,CAAC,CAAC,CAACqB,IAAI,CAAC,CAAC;UAC7B;UACAuC,WAAW,GAAGA,WAAW,CAACZ,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;UAC9C;UACAY,WAAW,GAAGA,WAAW,CAACZ,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;UACxDY,WAAW,GAAGA,WAAW,CAACZ,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;UACrDY,WAAW,GAAGA,WAAW,CAACvC,IAAI,CAAC,CAAC;UAEhCnC,OAAO,CAACC,GAAG,CAAC,kCAAkC6B,MAAM,CAACX,WAAW,aAAauD,WAAW,CAACxE,MAAM,EAAE,CAAC;UAClG;QACF;MACF;;MAEA;MACA,IAAI,CAACwE,WAAW,IAAIxC,OAAO,CAAC0C,WAAW,CAAC,CAAC,CAACnD,QAAQ,CAAC,aAAa,CAAC,EAAE;QACjE,MAAMoD,SAAS,GAAG3C,OAAO,CAAC0C,WAAW,CAAC,CAAC,CAACE,OAAO,CAAC,aAAa,CAAC;QAC9D,IAAID,SAAS,KAAK,CAAC,CAAC,EAAE;UACpBH,WAAW,GAAGxC,OAAO,CAAC/B,SAAS,CAAC0E,SAAS,GAAG,EAAE,CAAC,CAAC1C,IAAI,CAAC,CAAC;UACtDuC,WAAW,GAAGA,WAAW,CAACZ,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;UAC9C9D,OAAO,CAACC,GAAG,CAAC,gDAAgD6B,MAAM,CAACX,WAAW,EAAE,CAAC;QACnF;MACF;MAEA,IAAIoD,aAAa,EAAE;QACjB7F,OAAO,CAACoD,MAAM,CAACX,WAAW,CAAC,GAAG;UAC5BoD,aAAa,EAAEA,aAAa;UAC5BG,WAAW,EAAEA,WAAW,IAAI;QAC9B,CAAC;MACH,CAAC,MAAM;QACL1E,OAAO,CAACC,GAAG,CAAC,wCAAwC6B,MAAM,CAACX,WAAW,EAAE,CAAC;MAC3E;IACF;IAEAnB,OAAO,CAACC,GAAG,CAAC,yBAAyBO,MAAM,CAACC,IAAI,CAAC/B,OAAO,CAAC,CAACwB,MAAM,YAAY,CAAC;IAC7E,OAAOxB,OAAO;EAChB;EAEA,MAAMqG,UAAUA,CAACC,YAAY,EAAEC,UAAU,EAAE;IACzC,IAAI;MACF,MAAM,CAACxG,SAAS,EAAEC,OAAO,CAAC,GAAG,MAAMwG,OAAO,CAACC,GAAG,CAAC,CAC7C,IAAI,CAACxG,iBAAiB,CAACqG,YAAY,CAAC,EACpC,IAAI,CAAC1E,eAAe,CAAC2E,UAAU,CAAC,CACjC,CAAC;MAEF,OAAO;QAAExG,SAAS;QAAEC;MAAQ,CAAC;IAC/B,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF;AACF;AAEA,eAAe9B,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}