[{"/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/index.js": "1", "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/App.js": "2", "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/reportWebVitals.js": "3", "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/CFAExamApp.js": "4", "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/ExamHeader.js": "5", "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/QuestionDisplay.js": "6", "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/FileUploader.js": "7", "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/NavigationControls.js": "8", "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/utils/pdfParser.js": "9"}, {"size": 535, "mtime": *************, "results": "10", "hashOfConfig": "11"}, {"size": 259, "mtime": *************, "results": "12", "hashOfConfig": "11"}, {"size": 362, "mtime": *************, "results": "13", "hashOfConfig": "11"}, {"size": 7166, "mtime": *************, "results": "14", "hashOfConfig": "11"}, {"size": 3834, "mtime": *************, "results": "15", "hashOfConfig": "11"}, {"size": 6871, "mtime": *************, "results": "16", "hashOfConfig": "11"}, {"size": 5777, "mtime": *************, "results": "17", "hashOfConfig": "11"}, {"size": 5613, "mtime": *************, "results": "18", "hashOfConfig": "11"}, {"size": 7701, "mtime": *************, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mi01ty", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/index.js", [], [], "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/App.js", [], [], "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/reportWebVitals.js", [], [], "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/CFAExamApp.js", ["47"], [], "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/ExamHeader.js", [], [], "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/QuestionDisplay.js", [], [], "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/FileUploader.js", [], [], "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/components/NavigationControls.js", ["48"], [], "/Users/<USER>/Documents/Tài liệu CFA II 2025/Question Bank 2025/cfa-exam-app/src/utils/pdfParser.js", ["49"], [], {"ruleId": "50", "severity": 1, "message": "51", "line": 146, "column": 6, "nodeType": "52", "endLine": 146, "endColumn": 53, "suggestions": "53"}, {"ruleId": "54", "severity": 1, "message": "55", "line": 33, "column": 13, "nodeType": "56", "messageId": "57", "endLine": 33, "endColumn": 23}, {"ruleId": "54", "severity": 1, "message": "58", "line": 82, "column": 40, "nodeType": "56", "messageId": "57", "endLine": 82, "endColumn": 50}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'getCurrentQuestion'. Either include it or remove the dependency array.", "ArrayExpression", ["59"], "no-unused-vars", "'userAnswer' is assigned a value but never used.", "Identifier", "unusedVar", "'questionId' is assigned a value but never used.", {"desc": "60", "fix": "61"}, "Update the dependencies array to be: [examState, currentQuestionIndex, goToQuestion, getCurrentQuestion]", {"range": "62", "text": "63"}, [4723, 4770], "[examState, currentQuestionIndex, goToQuestion, getCurrentQuestion]"]