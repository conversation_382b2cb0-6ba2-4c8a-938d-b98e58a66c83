<a name="4.0.3"></a>
## [4.0.3](https://github.com/BerkeleyTrue/warning/compare/v4.0.2...v4.0.3) (2019-02-09)


### Bug Fixes

* incorrect formatting of message with arguments ([b188176](https://github.com/BerkeleyTrue/warning/commit/b188176))



<a name="4.0.2"></a>
## [4.0.2](https://github.com/BerkeleyTrue/warning/compare/v4.0.1...v4.0.2) (2018-08-17)


### Bug Fixes

* **use jest instead of tap:** tap is a PITA to debug ([c4c026b](https://github.com/BerkeleyTrue/warning/commit/c4c026b))
* remove [@provides](https://github.com/provides)Module annotation ([1d808f1](https://github.com/BerkeleyTrue/warning/commit/1d808f1))



# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

<a name="4.0.1"></a>
## [4.0.1](https://github.com/BerkeleyTrue/warning/compare/v4.0.0...v4.0.1) (2018-05-30)


### Bug Fixes

* add `var printWarning =` to comply with ES5 strict mode ([677dcfa](https://github.com/BerkeleyTrue/warning/commit/677dcfa)), closes [#25](https://github.com/BerkeleyTrue/warning/issues/25)

<a name="4.0.0"></a>
## [4.0.0](https://github.com/BerkeleyTrue/warning/compare/v3.0.0...v4.0.0) (2018-05-22)


### Bug Fixes

* Remove "browser" version ([521f5f5](https://github.com/BerkeleyTrue/warning/commit/521f5f5)), closes [#18](https://github.com/BerkeleyTrue/warning/issues/18) [/github.com/facebook/fbjs/pull/86#issuecomment-285204734](https://github.com//github.com/facebook/fbjs/pull/86/issues/issuecomment-285204734)
* Update warning to use the latest version from facebook/fbjs ([0572ddd](https://github.com/BerkeleyTrue/warning/commit/0572ddd))


### Chores

* **LICENSE:** Change from BSD modified to MIT ([5a63a1b](https://github.com/BerkeleyTrue/warning/commit/5a63a1b))


### BREAKING CHANGES

* **LICENSE:** Change License to MIT from BSD+patents
* This changes the internal workings. A major release is
made to ensure minimal effect on downstream users.


<a name="3.0.0"></a>
## [3.0.0](https://github.com/BerkeleyTrue/warning/compare/v2.1.0...v3.0.0) (2015-10-04)

### BREAKING CHANGE

* **package.json** correct license field ([6bd7ad5](https://github.com/BerkeleyTrue/warning/commit/6bd7ad5))

<a name="2.1.0"></a>
## [2.1.0](https://github.com/BerkeleyTrue/warning/compare/v2.0.0...v2.1.0) (2015-10-04)

### Features

* switch to loose-envify ([dacc2da](https://github.com/BerkeleyTrue/warning/commit/dacc2da))

<a name="2.0.0"></a>
## [2.0.0](https://github.com/BerkeleyTrue/warning/compare/v1.0.2...v2.0.0) (2015-07-11)

### BREAKING CHANGE

* add browser(ify) friendly version ([1a33d40fa1](https://github.com/BerkeleyTrue/warning/commit/1a33d40fa1))

<a name="1.0.2"></a>
## [1.0.2](https://github.com/BerkeleyTrue/warning/compare/v1.0.1...v1.0.2) (2015-05-30)

### Bug Fixes

* return args in replace ([2ac6962](https://github.com/BerkeleyTrue/warning/commit/2ac6962263))
