import React, { useState } from 'react';
import { <PERSON>, <PERSON>ton, Alert, Badge, Row, Col } from 'react-bootstrap';

const QuestionDisplay = ({
  question,
  groupQuestions,
  answers,
  userAnswer,
  showAnswer,
  onSubmitAnswer,
  onRetryQuestion
}) => {
  const [selectedAnswer, setSelectedAnswer] = useState(userAnswer || '');

  const handleAnswerSelect = (choice) => {
    if (showAnswer) return; // Prevent changing answer after submission
    setSelectedAnswer(choice);
  };

  const handleSubmit = () => {
    if (!selectedAnswer) return;
    onSubmitAnswer(question.questionNumber, selectedAnswer);
  };

  const handleRetry = () => {
    setSelectedAnswer('');
    if (onRetryQuestion) {
      onRetryQuestion(question.questionNumber);
    }
  };

  const getAnswerData = () => {
    return answers[question.questionNumber] || {};
  };

  const isCorrect = () => {
    const answerData = getAnswerData();
    return userAnswer === answerData.correctAnswer;
  };

  const getChoiceVariant = (choice) => {
    if (!showAnswer) {
      return selectedAnswer === choice ? 'primary' : 'outline-secondary';
    }
    
    const answerData = getAnswerData();
    if (choice === answerData.correctAnswer) {
      return 'success';
    } else if (choice === userAnswer && choice !== answerData.correctAnswer) {
      return 'danger';
    }
    return 'outline-secondary';
  };

  const getChoiceIcon = (choice) => {
    if (!showAnswer) {
      return selectedAnswer === choice ? 'fas fa-check-circle' : 'far fa-circle';
    }
    
    const answerData = getAnswerData();
    if (choice === answerData.correctAnswer) {
      return 'fas fa-check-circle';
    } else if (choice === userAnswer && choice !== answerData.correctAnswer) {
      return 'fas fa-times-circle';
    }
    return 'far fa-circle';
  };

  return (
    <div className="question-display">
      {/* Context Section (for group questions) */}
      {question.isGroupQuestion && question.context && (
        <Card className="mb-4 border-info">
          <Card.Header className="bg-info text-white">
            <h6 className="mb-0">
              <i className="fas fa-info-circle me-2"></i>
              Thông tin chung cho câu hỏi {groupQuestions[0]?.questionNumber} - {groupQuestions[groupQuestions.length - 1]?.questionNumber}
            </h6>
          </Card.Header>
          <Card.Body>
            <div className="context-content" style={{ whiteSpace: 'pre-line', lineHeight: '1.6' }}>
              {question.context}
            </div>
          </Card.Body>
        </Card>
      )}

      {/* Question Section */}
      <Card className="mb-4 shadow">
        <Card.Header className="d-flex justify-content-between align-items-center">
          <h5 className="mb-0">
            <i className="fas fa-question-circle me-2"></i>
            Câu hỏi {question.questionNumber}
          </h5>
          <div>
            <Badge bg="secondary" className="me-2">
              ID: {question.questionId}
            </Badge>
            {question.isGroupQuestion && (
              <Badge bg="info">
                <i className="fas fa-layer-group me-1"></i>
                Nhóm câu hỏi
              </Badge>
            )}
          </div>
        </Card.Header>
        
        <Card.Body>
          {/* Question Text */}
          <div className="question-text mb-4" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
            {question.questionText}
          </div>

          {/* Answer Choices */}
          <div className="choices-section">
            <h6 className="mb-3">Chọn đáp án:</h6>
            <Row>
              {Object.entries(question.choices).map(([choice, text]) => (
                <Col key={choice} xs={12} className="mb-2">
                  <Button
                    variant={getChoiceVariant(choice)}
                    className="w-100 text-start p-3"
                    onClick={() => handleAnswerSelect(choice)}
                    disabled={showAnswer}
                    style={{ 
                      minHeight: '60px',
                      border: selectedAnswer === choice && !showAnswer ? '2px solid #0d6efd' : undefined
                    }}
                  >
                    <div className="d-flex align-items-start">
                      <div className="me-3 mt-1">
                        <i className={getChoiceIcon(choice)}></i>
                      </div>
                      <div>
                        <strong className="me-2">{choice})</strong>
                        {text}
                      </div>
                    </div>
                  </Button>
                </Col>
              ))}
            </Row>
          </div>

          {/* Submit Button */}
          {!showAnswer && (
            <div className="text-center mt-4">
              <Button
                variant="success"
                size="lg"
                onClick={handleSubmit}
                disabled={!selectedAnswer}
                className="px-5"
              >
                <i className="fas fa-check me-2"></i>
                Xác nhận đáp án
              </Button>
              <div className="mt-2">
                <small className="text-muted">
                  Hoặc nhấn phím {Object.keys(question.choices).map((choice, index) => `${index + 1} (${choice})`).join(', ')}
                </small>
              </div>
            </div>
          )}

          {/* Answer Explanation */}
          {showAnswer && (
            <div className="mt-4">
              <Alert variant={isCorrect() ? 'success' : 'danger'}>
                <div className="d-flex align-items-center mb-2">
                  <i className={`fas ${isCorrect() ? 'fa-check-circle' : 'fa-times-circle'} me-2`}></i>
                  <strong>
                    {isCorrect() ? 'Chính xác!' : 'Không chính xác'}
                  </strong>
                </div>
                <div>
                  <strong>Đáp án đúng: {getAnswerData().correctAnswer}</strong>
                  {userAnswer && userAnswer !== getAnswerData().correctAnswer && (
                    <div className="mt-1">
                      Bạn đã chọn: {userAnswer}
                    </div>
                  )}
                </div>
              </Alert>

              {getAnswerData().explanation && (
                <Card className="mt-3">
                  <Card.Header className="bg-light">
                    <h6 className="mb-0">
                      <i className="fas fa-lightbulb me-2"></i>
                      Giải thích
                    </h6>
                  </Card.Header>
                  <Card.Body>
                    <div style={{ whiteSpace: 'pre-line', lineHeight: '1.6' }}>
                      {getAnswerData().explanation}
                    </div>
                  </Card.Body>
                </Card>
              )}

              {/* Retry Button */}
              <div className="text-center mt-3">
                <Button
                  variant="outline-warning"
                  onClick={handleRetry}
                  className="me-2"
                >
                  <i className="fas fa-redo me-2"></i>
                  Làm lại câu này
                </Button>
                <small className="text-muted d-block mt-2">
                  Xóa đáp án đã chọn và làm lại câu hỏi này
                </small>
              </div>
            </div>
          )}
        </Card.Body>
      </Card>
    </div>
  );
};

export default QuestionDisplay;
