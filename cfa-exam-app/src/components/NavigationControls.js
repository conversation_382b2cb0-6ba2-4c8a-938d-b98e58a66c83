import React, { useState } from 'react';
import { Card, Button, Row, Col, Modal, Badge, ButtonGroup } from 'react-bootstrap';

const NavigationControls = ({ 
  currentIndex, 
  totalQuestions, 
  onNavigate, 
  questions, 
  completedQuestions, 
  userAnswers 
}) => {
  const [showQuestionList, setShowQuestionList] = useState(false);

  const handlePrevious = () => {
    if (currentIndex > 0) {
      onNavigate(currentIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentIndex < totalQuestions - 1) {
      onNavigate(currentIndex + 1);
    }
  };

  const handleQuestionSelect = (index) => {
    onNavigate(index);
    setShowQuestionList(false);
  };

  const getQuestionStatus = (questionNumber) => {
    if (completedQuestions.has(questionNumber)) {
      const userAnswer = userAnswers[questionNumber];
      // You could add logic here to check if answer is correct
      return 'completed';
    }
    return 'not-started';
  };

  const getStatusBadge = (questionNumber) => {
    const status = getQuestionStatus(questionNumber);
    switch (status) {
      case 'completed':
        return <Badge bg="success">✓</Badge>;
      default:
        return <Badge bg="secondary">-</Badge>;
    }
  };

  const getStatusVariant = (questionNumber) => {
    const status = getQuestionStatus(questionNumber);
    switch (status) {
      case 'completed':
        return 'success';
      default:
        return 'outline-secondary';
    }
  };

  return (
    <>
      {/* Navigation Controls */}
      <Card className="mt-4 sticky-bottom shadow">
        <Card.Body>
          <Row className="align-items-center">
            <Col md={3}>
              <Button
                variant="outline-primary"
                onClick={handlePrevious}
                disabled={currentIndex === 0}
                className="w-100"
              >
                <i className="fas fa-chevron-left me-2"></i>
                Câu trước
              </Button>
            </Col>
            
            <Col md={6} className="text-center">
              <ButtonGroup>
                <Button
                  variant="outline-info"
                  onClick={() => setShowQuestionList(true)}
                >
                  <i className="fas fa-list me-2"></i>
                  Danh sách câu hỏi
                </Button>
                
                <Button
                  variant="outline-secondary"
                  onClick={() => onNavigate(Math.floor(Math.random() * totalQuestions))}
                >
                  <i className="fas fa-random me-2"></i>
                  Ngẫu nhiên
                </Button>
              </ButtonGroup>
              
              <div className="mt-2">
                <small className="text-muted">
                  Sử dụng phím ← → để điều hướng
                </small>
              </div>
            </Col>
            
            <Col md={3}>
              <Button
                variant="outline-primary"
                onClick={handleNext}
                disabled={currentIndex === totalQuestions - 1}
                className="w-100"
              >
                Câu tiếp
                <i className="fas fa-chevron-right ms-2"></i>
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Question List Modal */}
      <Modal 
        show={showQuestionList} 
        onHide={() => setShowQuestionList(false)}
        size="lg"
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-list me-2"></i>
            Danh sách câu hỏi
          </Modal.Title>
        </Modal.Header>
        <Modal.Body style={{ maxHeight: '60vh', overflowY: 'auto' }}>
          <div className="mb-3">
            <Row>
              <Col>
                <Badge bg="success" className="me-2">✓ Đã làm ({completedQuestions.size})</Badge>
                <Badge bg="secondary">- Chưa làm ({totalQuestions - completedQuestions.size})</Badge>
              </Col>
            </Row>
          </div>
          
          <Row>
            {questions.map((question, index) => (
              <Col key={question.questionNumber} xs={6} md={4} lg={3} className="mb-2">
                <Button
                  variant={index === currentIndex ? 'primary' : getStatusVariant(question.questionNumber)}
                  className="w-100 d-flex justify-content-between align-items-center"
                  onClick={() => handleQuestionSelect(index)}
                  style={{ minHeight: '45px' }}
                >
                  <span>
                    {question.isGroupQuestion && (
                      <i className="fas fa-layer-group me-1" title="Câu hỏi nhóm"></i>
                    )}
                    Q{question.questionNumber}
                  </span>
                  {getStatusBadge(question.questionNumber)}
                </Button>
              </Col>
            ))}
          </Row>
        </Modal.Body>
        <Modal.Footer>
          <div className="w-100 d-flex justify-content-between align-items-center">
            <div>
              <small className="text-muted">
                <i className="fas fa-layer-group me-1"></i>
                = Câu hỏi có context chung
              </small>
            </div>
            <Button variant="secondary" onClick={() => setShowQuestionList(false)}>
              Đóng
            </Button>
          </div>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default NavigationControls;
