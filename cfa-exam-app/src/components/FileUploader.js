import React, { useState } from 'react';
import { Card, Form, Button, Row, Col, Alert, Spinner } from 'react-bootstrap';

const FileUploader = ({ onFilesUploaded, loading }) => {
  const [questionFile, setQuestionFile] = useState(null);
  const [answerFile, setAnswerFile] = useState(null);
  const [error, setError] = useState('');

  const handleQuestionFileChange = (e) => {
    const file = e.target.files[0];
    if (file && file.type === 'application/pdf') {
      setQuestionFile(file);
      setError('');
    } else {
      setError('Vui lòng chọn file PDF hợp lệ cho câu hỏi');
      setQuestionFile(null);
    }
  };

  const handleAnswerFileChange = (e) => {
    const file = e.target.files[0];
    if (file && file.type === 'application/pdf') {
      setAnswerFile(file);
      setError('');
    } else {
      setError('Vui lòng chọn file PDF hợp lệ cho đáp án');
      setAnswerFile(null);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!questionFile || !answerFile) {
      setError('Vui lòng chọn cả file câu hỏi và file đáp án');
      return;
    }

    // Validate file names
    const questionFileName = questionFile.name.toLowerCase();
    const answerFileName = answerFile.name.toLowerCase();
    
    if (questionFileName.includes('answer') || answerFileName.includes('answer')) {
      if (questionFileName.includes('answer') && !answerFileName.includes('answer')) {
        setError('Có vẻ như bạn đã chọn nhầm file. File câu hỏi không nên chứa từ "answer"');
        return;
      }
    }

    onFilesUploaded(questionFile, answerFile);
  };

  return (
    <Card className="shadow-lg">
      <Card.Header className="bg-primary text-white">
        <h4 className="mb-0">
          <i className="fas fa-upload me-2"></i>
          Tải file câu hỏi và đáp án CFA
        </h4>
      </Card.Header>
      <Card.Body className="p-4">
        {error && (
          <Alert variant="danger" className="mb-3">
            {error}
          </Alert>
        )}

        <Form onSubmit={handleSubmit}>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label className="fw-bold">
                  <i className="fas fa-question-circle me-2"></i>
                  File câu hỏi (PDF)
                </Form.Label>
                <Form.Control
                  type="file"
                  accept=".pdf"
                  onChange={handleQuestionFileChange}
                  disabled={loading}
                />
                <Form.Text className="text-muted">
                  Chọn file PDF chứa câu hỏi (không chứa từ "answer")
                </Form.Text>
                {questionFile && (
                  <div className="mt-2">
                    <small className="text-success">
                      <i className="fas fa-check me-1"></i>
                      Đã chọn: {questionFile.name}
                    </small>
                  </div>
                )}
              </Form.Group>
            </Col>

            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label className="fw-bold">
                  <i className="fas fa-check-circle me-2"></i>
                  File đáp án (PDF)
                </Form.Label>
                <Form.Control
                  type="file"
                  accept=".pdf"
                  onChange={handleAnswerFileChange}
                  disabled={loading}
                />
                <Form.Text className="text-muted">
                  Chọn file PDF chứa đáp án (thường có từ "answer")
                </Form.Text>
                {answerFile && (
                  <div className="mt-2">
                    <small className="text-success">
                      <i className="fas fa-check me-1"></i>
                      Đã chọn: {answerFile.name}
                    </small>
                  </div>
                )}
              </Form.Group>
            </Col>
          </Row>

          <div className="text-center mt-4">
            <Button
              type="submit"
              variant="primary"
              size="lg"
              disabled={!questionFile || !answerFile || loading}
              className="px-5"
            >
              {loading ? (
                <>
                  <Spinner
                    as="span"
                    animation="border"
                    size="sm"
                    role="status"
                    className="me-2"
                  />
                  Đang xử lý file...
                </>
              ) : (
                <>
                  <i className="fas fa-play me-2"></i>
                  Tải và bắt đầu
                </>
              )}
            </Button>
          </div>
        </Form>

        <div className="mt-4 p-3 bg-light rounded">
          <h6 className="fw-bold mb-2">
            <i className="fas fa-info-circle me-2"></i>
            Hướng dẫn sử dụng:
          </h6>
          <ul className="mb-0 small">
            <li>Chọn file PDF chứa câu hỏi (thường không có từ "answer" trong tên)</li>
            <li>Chọn file PDF chứa đáp án (thường có từ "answer" hoặc "answers" trong tên)</li>
            <li>Ứng dụng sẽ tự động phân tích và tách câu hỏi, đáp án</li>
            <li>Hỗ trợ cả câu hỏi đơn lẻ và câu hỏi theo nhóm có context chung</li>
            <li>Sử dụng phím mũi tên để điều hướng, phím 1-5 để chọn đáp án</li>
          </ul>
        </div>
      </Card.Body>
    </Card>
  );
};

export default FileUploader;
