import React, { useState, useEffect, useCallback } from 'react';
import { Container, Row, Col, Alert } from 'react-bootstrap';
import FileUploader from './FileUploader';
import ExamHeader from './ExamHeader';
import QuestionDisplay from './QuestionDisplay';
import NavigationControls from './NavigationControls';
import CFAQuestionParser from '../utils/pdfParser';

const CFAExamApp = () => {
  // State management
  const [examState, setExamState] = useState('file-selection'); // 'file-selection', 'exam-loaded', 'exam-started'
  const [questions, setQuestions] = useState([]);
  const [answers, setAnswers] = useState({});
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [userAnswers, setUserAnswers] = useState({});
  const [completedQuestions, setCompletedQuestions] = useState(new Set());
  const [showAnswer, setShowAnswer] = useState({});
  const [startTime, setStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [examInfo, setExamInfo] = useState(null);

  // Timer effect
  useEffect(() => {
    let interval = null;
    if (startTime && examState === 'exam-started') {
      interval = setInterval(() => {
        setElapsedTime(Date.now() - startTime);
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [startTime, examState]);

  // Handle file upload and parsing
  const handleFilesUploaded = async (questionFile, answerFile) => {
    setLoading(true);
    setError(null);
    
    try {
      const parser = new CFAQuestionParser();
      const { questions: parsedQuestions, answers: parsedAnswers } = await parser.parseFiles(questionFile, answerFile);
      
      if (parsedQuestions.length === 0) {
        throw new Error('Không tìm thấy câu hỏi nào trong file PDF');
      }
      
      setQuestions(parsedQuestions);
      setAnswers(parsedAnswers);
      setExamInfo({
        questionFileName: questionFile.name,
        answerFileName: answerFile.name,
        totalQuestions: parsedQuestions.length
      });
      setExamState('exam-loaded');
      
    } catch (err) {
      setError(`Lỗi khi xử lý file: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Start exam
  const startExam = () => {
    setStartTime(Date.now());
    setExamState('exam-started');
    setCurrentQuestionIndex(0);
  };

  // Navigate to question
  const goToQuestion = useCallback((index) => {
    if (index >= 0 && index < questions.length) {
      setCurrentQuestionIndex(index);
    }
  }, [questions.length]);

  // Submit answer
  const submitAnswer = (questionNumber, selectedAnswer) => {
    setUserAnswers(prev => ({
      ...prev,
      [questionNumber]: selectedAnswer
    }));

    setCompletedQuestions(prev => new Set([...prev, questionNumber]));

    setShowAnswer(prev => ({
      ...prev,
      [questionNumber]: true
    }));
  };

  // Retry question
  const retryQuestion = (questionNumber) => {
    setUserAnswers(prev => {
      const newAnswers = { ...prev };
      delete newAnswers[questionNumber];
      return newAnswers;
    });

    setCompletedQuestions(prev => {
      const newCompleted = new Set(prev);
      newCompleted.delete(questionNumber);
      return newCompleted;
    });

    setShowAnswer(prev => ({
      ...prev,
      [questionNumber]: false
    }));
  };

  // Get current question
  const getCurrentQuestion = () => {
    if (questions.length === 0 || currentQuestionIndex >= questions.length) {
      return null;
    }
    return questions[currentQuestionIndex];
  };

  // Get questions in same group (for group questions)
  const getGroupQuestions = (question) => {
    if (!question || !question.isGroupQuestion) {
      return [question];
    }
    
    return questions.filter(q => 
      q.isGroupQuestion && 
      q.context === question.context
    );
  };

  // Format time
  const formatTime = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (examState !== 'exam-started') return;
      
      if (e.key === 'ArrowLeft') {
        goToQuestion(currentQuestionIndex - 1);
      } else if (e.key === 'ArrowRight') {
        goToQuestion(currentQuestionIndex + 1);
      } else if (e.key >= '1' && e.key <= '5') {
        const choices = ['A', 'B', 'C', 'D', 'E'];
        const choice = choices[parseInt(e.key) - 1];
        const currentQuestion = getCurrentQuestion();
        if (currentQuestion && currentQuestion.choices[choice]) {
          submitAnswer(currentQuestion.questionNumber, choice);
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [examState, currentQuestionIndex, goToQuestion]);

  const currentQuestion = getCurrentQuestion();
  const groupQuestions = currentQuestion ? getGroupQuestions(currentQuestion) : [];

  return (
    <Container fluid className="cfa-exam-app">
      {/* Header */}
      <ExamHeader 
        examState={examState}
        examInfo={examInfo}
        currentQuestionNumber={currentQuestion?.questionNumber || 0}
        totalQuestions={questions.length}
        completedCount={completedQuestions.size}
        elapsedTime={formatTime(elapsedTime)}
      />

      {/* Error Alert */}
      {error && (
        <Row className="mt-3">
          <Col>
            <Alert variant="danger" onClose={() => setError(null)} dismissible>
              {error}
            </Alert>
          </Col>
        </Row>
      )}

      {/* Main Content */}
      <Row className="mt-3">
        <Col>
          {examState === 'file-selection' && (
            <FileUploader 
              onFilesUploaded={handleFilesUploaded}
              loading={loading}
            />
          )}

          {examState === 'exam-loaded' && (
            <div className="text-center">
              <h3>Sẵn sàng bắt đầu làm bài</h3>
              <p>Đã tải {questions.length} câu hỏi từ {examInfo?.questionFileName}</p>
              <button 
                className="btn btn-primary btn-lg"
                onClick={startExam}
              >
                Bắt đầu làm bài
              </button>
            </div>
          )}

          {examState === 'exam-started' && currentQuestion && (
            <>
              <QuestionDisplay
                question={currentQuestion}
                groupQuestions={groupQuestions}
                answers={answers}
                userAnswer={userAnswers[currentQuestion.questionNumber]}
                showAnswer={showAnswer[currentQuestion.questionNumber]}
                onSubmitAnswer={submitAnswer}
                onRetryQuestion={retryQuestion}
              />
              
              <NavigationControls
                currentIndex={currentQuestionIndex}
                totalQuestions={questions.length}
                onNavigate={goToQuestion}
                questions={questions}
                completedQuestions={completedQuestions}
                userAnswers={userAnswers}
              />
            </>
          )}
        </Col>
      </Row>
    </Container>
  );
};

export default CFAExamApp;
