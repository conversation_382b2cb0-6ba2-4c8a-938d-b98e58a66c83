import React from 'react';
import { Navbar, Container, Row, Col, ProgressBar, Badge } from 'react-bootstrap';

const ExamHeader = ({ 
  examState, 
  examInfo, 
  currentQuestionNumber, 
  totalQuestions, 
  completedCount, 
  elapsedTime 
}) => {
  const getProgressPercentage = () => {
    if (totalQuestions === 0) return 0;
    return (completedCount / totalQuestions) * 100;
  };

  return (
    <>
      {/* Main Header */}
      <Navbar bg="primary" variant="dark" className="shadow">
        <Container fluid>
          <Navbar.Brand>
            <i className="fas fa-graduation-cap me-2"></i>
            CFA Level II Question Bank
          </Navbar.Brand>
          
          {examState === 'exam-started' && (
            <div className="d-flex align-items-center">
              <Badge bg="light" text="dark" className="me-3 fs-6">
                <i className="fas fa-clock me-1"></i>
                {elapsedTime}
              </Badge>
            </div>
          )}
        </Container>
      </Navbar>

      {/* Progress Info */}
      {examState === 'exam-started' && examInfo && (
        <div className="bg-light border-bottom py-3">
          <Container fluid>
            <Row className="align-items-center">
              <Col md={3}>
                <h6 className="mb-0 text-primary">
                  <i className="fas fa-book me-2"></i>
                  {examInfo.questionFileName.replace('.pdf', '')}
                </h6>
              </Col>
              
              <Col md={2}>
                <div className="text-center">
                  <div className="fw-bold text-primary">Câu hỏi</div>
                  <div className="fs-5">
                    {currentQuestionNumber} / {totalQuestions}
                  </div>
                </div>
              </Col>
              
              <Col md={2}>
                <div className="text-center">
                  <div className="fw-bold text-success">Đã làm</div>
                  <div className="fs-5">
                    {completedCount}
                  </div>
                </div>
              </Col>
              
              <Col md={3}>
                <div>
                  <div className="d-flex justify-content-between mb-1">
                    <small className="fw-bold">Tiến độ</small>
                    <small>{Math.round(getProgressPercentage())}%</small>
                  </div>
                  <ProgressBar 
                    now={getProgressPercentage()} 
                    variant="success"
                    style={{ height: '8px' }}
                  />
                </div>
              </Col>
              
              <Col md={2}>
                <div className="text-center">
                  <div className="fw-bold text-info">Thời gian</div>
                  <div className="fs-6 font-monospace">
                    {elapsedTime}
                  </div>
                </div>
              </Col>
            </Row>
          </Container>
        </div>
      )}

      {/* File Info (when exam is loaded but not started) */}
      {examState === 'exam-loaded' && examInfo && (
        <div className="bg-light border-bottom py-3">
          <Container fluid>
            <Row className="align-items-center justify-content-center">
              <Col md={8} className="text-center">
                <h5 className="mb-1 text-primary">
                  <i className="fas fa-file-pdf me-2"></i>
                  {examInfo.questionFileName.replace('.pdf', '')}
                </h5>
                <p className="mb-0 text-muted">
                  Đã tải thành công {examInfo.totalQuestions} câu hỏi
                </p>
              </Col>
            </Row>
          </Container>
        </div>
      )}
    </>
  );
};

export default ExamHeader;
