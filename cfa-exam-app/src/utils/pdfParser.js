// PDF Parser for CFA Questions and Answers
import * as pdfjsLib from 'pdfjs-dist';

// Set worker path - use the worker from the same version
pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;

class CFAQuestionParser {
  constructor() {
    this.questions = [];
    this.answers = {};
  }

  async parseQuestionFile(file) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;

      let fullText = '';

      // Extract text from all pages with better formatting
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();

        // Better text extraction preserving structure
        let pageText = '';
        let lastY = null;

        for (const item of textContent.items) {
          // Add line break if Y position changed significantly (new line)
          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {
            pageText += '\n';
          }

          pageText += item.str + ' ';
          lastY = item.transform[5];
        }

        fullText += pageText + '\n\n';
      }

      // Debug: Log extracted text
      console.log('Extracted text length:', fullText.length);
      console.log('First 2000 characters:', fullText.substring(0, 2000));

      // Parse questions from text
      const questions = this.extractQuestions(fullText);
      console.log('Extracted questions:', questions.length);

      return questions;
    } catch (error) {
      console.error('Error parsing question file:', error);
      throw error;
    }
  }

  async parseAnswerFile(file) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;

      let fullText = '';

      // Extract text from all pages with better formatting
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();

        // Better text extraction preserving structure
        let pageText = '';
        let lastY = null;

        for (const item of textContent.items) {
          // Add line break if Y position changed significantly (new line)
          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {
            pageText += '\n';
          }

          pageText += item.str + ' ';
          lastY = item.transform[5];
        }

        fullText += pageText + '\n\n';
      }

      console.log('Answer file text length:', fullText.length);
      console.log('Answer file preview:', fullText.substring(0, 1000));

      // Parse answers from text
      const answers = this.extractAnswers(fullText);
      console.log('Extracted answers:', Object.keys(answers).length);

      return answers;
    } catch (error) {
      console.error('Error parsing answer file:', error);
      throw error;
    }
  }

  extractQuestions(text) {
    const questions = [];

    console.log('Looking for question patterns in text...');
    console.log('Text length:', text.length);

    // Try simpler approach - split by "Question #" and process each block
    const questionBlocks = text.split(/(?=Question #\d+)/);
    console.log(`Split into ${questionBlocks.length} blocks`);

    for (let i = 0; i < questionBlocks.length; i++) {
      const block = questionBlocks[i].trim();
      if (!block || block.length < 20) continue;

      console.log(`\n--- Processing block ${i} ---`);
      console.log('Block start:', block.substring(0, 100));

      // Try to extract question info from block
      const patterns = [
        /Question #(\d+) of (\d+) Question ID: (\d+)/,
        /Question #(\d+) - (\d+) of (\d+) Question ID: (\d+)/,
        /Question #(\d+) of (\d+)/,
        /Question #(\d+)/
      ];

      let questionInfo = null;
      for (const pattern of patterns) {
        const match = block.match(pattern);
        if (match) {
          questionInfo = {
            questionNum: parseInt(match[1]),
            endNum: match[2] && !isNaN(parseInt(match[2])) ? parseInt(match[2]) : null,
            totalQ: match[3] && !isNaN(parseInt(match[3])) ? parseInt(match[3]) : 100,
            questionId: match[4] || 'unknown',
            isGroup: match[0].includes(' - '),
            fullMatch: match[0]
          };
          break;
        }
      }

      if (!questionInfo) {
        console.log('No question pattern found in block');
        continue;
      }

      console.log('Found question info:', questionInfo);

      // Extract content after the question header
      const headerIndex = block.indexOf(questionInfo.fullMatch);
      const content = block.substring(headerIndex + questionInfo.fullMatch.length).trim();

      if (questionInfo.isGroup) {
        // Handle group questions - extract context and individual questions
        console.log(`Processing group questions ${questionInfo.questionNum}-${questionInfo.endNum}`);

        // For group questions, try to find individual question markers within the content
        const groupQuestions = this.extractGroupQuestions(
          content,
          questionInfo.questionNum,
          questionInfo.endNum,
          questionInfo.totalQ,
          questionInfo.questionId
        );

        if (groupQuestions.length > 0) {
          questions.push(...groupQuestions);
        } else {
          // Fallback: treat as single question
          const questionData = this.parseSingleQuestion(
            content,
            questionInfo.questionNum,
            questionInfo.totalQ,
            questionInfo.questionId
          );
          if (questionData) {
            questions.push(questionData);
          }
        }
      } else {
        // Handle single question
        console.log(`Processing single question ${questionInfo.questionNum}`);

        const questionData = this.parseSingleQuestion(
          content,
          questionInfo.questionNum,
          questionInfo.totalQ,
          questionInfo.questionId
        );

        if (questionData) {
          questions.push(questionData);
          console.log(`Successfully parsed question ${questionInfo.questionNum}`);
        } else {
          console.log(`Failed to parse question ${questionInfo.questionNum}`);
        }
      }
    }

    console.log(`\n=== EXTRACTION COMPLETE ===`);
    console.log(`Total questions extracted: ${questions.length}`);
    console.log('Question numbers:', questions.map(q => q.questionNumber).sort((a, b) => a - b));

    return questions;
  }

  extractQuestionsAlternative(text) {
    console.log('Using alternative extraction method...');
    const questions = [];

    // Look for any numbered patterns that might be questions
    const patterns = [
      /(\d+)\.\s+([^0-9]+?)(?=\d+\.|$)/gs,  // 1. Question text
      /Question\s+(\d+)[:\.]?\s*([^Q]+?)(?=Question\s+\d+|$)/gis,  // Question 1: text
    ];

    for (const pattern of patterns) {
      const matches = [...text.matchAll(pattern)];
      console.log(`Alternative pattern found ${matches.length} matches`);

      for (const match of matches) {
        const questionNum = parseInt(match[1]);
        const content = match[2].trim();

        if (content.length > 20) { // Filter out very short matches
          const questionData = this.parseSingleQuestion(
            content,
            questionNum,
            100, // Default total
            'alt-' + questionNum
          );

          if (questionData) {
            questions.push(questionData);
          }
        }
      }

      if (questions.length > 0) break; // Use first successful pattern
    }

    return questions;
  }

  extractGroupQuestions(content, startQ, endQ, totalQ, questionId) {
    const questions = [];

    console.log(`Extracting group questions ${startQ}-${endQ || startQ}`);
    console.log('Group content preview:', content.substring(0, 200));

    // First, try to extract context (text before any question-specific content)
    let context = '';
    const contextMatch = content.match(/^(.*?)(?=[A-E]\)|Question|$)/s);
    if (contextMatch && contextMatch[1].trim().length > 50) {
      context = contextMatch[1].trim();
      console.log('Extracted context:', context.substring(0, 100));
    }

    // If this is a range (e.g., Question #6-11), try to find individual questions
    if (endQ && endQ > startQ) {
      console.log(`Looking for individual questions ${startQ} to ${endQ}`);

      // Try to split content by question numbers
      for (let qNum = startQ; qNum <= endQ; qNum++) {
        // Look for patterns like "6.", "Question 6", etc.
        const patterns = [
          new RegExp(`${qNum}\\.(.*?)(?=${qNum + 1}\\.|$)`, 's'),
          new RegExp(`Question\\s+${qNum}[:\\.]?(.*?)(?=Question\\s+${qNum + 1}|$)`, 'is')
        ];

        let questionContent = '';
        for (const pattern of patterns) {
          const match = content.match(pattern);
          if (match) {
            questionContent = match[1].trim();
            break;
          }
        }

        if (questionContent) {
          console.log(`Found content for question ${qNum}`);
          const questionData = this.parseSingleQuestion(
            questionContent,
            qNum,
            totalQ,
            `${questionId}-${qNum}`,
            context
          );

          if (questionData) {
            questions.push(questionData);
          }
        }
      }
    }

    // If no individual questions found, treat the whole content as one question
    if (questions.length === 0) {
      console.log('No individual questions found, treating as single question with context');
      const questionData = this.parseSingleQuestion(
        content,
        startQ,
        totalQ,
        questionId,
        context
      );

      if (questionData) {
        questions.push(questionData);
      }
    }

    console.log(`Extracted ${questions.length} questions from group`);
    return questions;
  }

  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {
    if (!content.trim()) return null;

    console.log(`\n=== Parsing Question ${questionNum} ===`);
    console.log(`Content length: ${content.length}`);
    console.log(`Content preview: ${content.substring(0, 300)}...`);

    let questionText = '';
    const choices = {};

    // Method 1: Look for choice patterns A) B) C) D) E)
    const choicePattern = /([A-E])\)\s*([^A-E]*?)(?=\s*[A-E]\)|$)/g;
    const choiceMatches = [...content.matchAll(choicePattern)];

    console.log(`Found ${choiceMatches.length} choice matches`);

    if (choiceMatches.length >= 3) {
      // Extract question text (everything before first choice)
      const firstChoiceIndex = choiceMatches[0].index;
      questionText = content.substring(0, firstChoiceIndex).trim();

      // Extract choices
      choiceMatches.forEach(match => {
        const choice = match[1];
        const text = match[2].trim();
        if (text.length > 0) {
          choices[choice] = text;
        }
      });

      console.log(`Method 1 success: ${Object.keys(choices).length} choices extracted`);
    } else {
      // Method 2: Line-by-line parsing with better logic
      console.log('Using Method 2: Line-by-line parsing');

      const lines = content.split(/\n/).map(line => line.trim()).filter(line => line.length > 0);

      let currentChoice = null;
      let choiceText = '';
      let questionLines = [];
      let foundFirstChoice = false;

      for (const line of lines) {
        // Check for choice pattern at start of line
        const choiceMatch = line.match(/^([A-E])\)\s*(.*)/);

        if (choiceMatch) {
          // Save previous choice if exists
          if (currentChoice && choiceText.trim()) {
            choices[currentChoice] = choiceText.trim();
          }

          currentChoice = choiceMatch[1];
          choiceText = choiceMatch[2];
          foundFirstChoice = true;

        } else if (currentChoice && foundFirstChoice) {
          // Continue current choice text
          choiceText += ' ' + line;

        } else if (!foundFirstChoice) {
          // Still in question text area
          questionLines.push(line);
        }
      }

      // Save last choice
      if (currentChoice && choiceText.trim()) {
        choices[currentChoice] = choiceText.trim();
      }

      questionText = questionLines.join(' ').trim();
      console.log(`Method 2 result: ${Object.keys(choices).length} choices, question length: ${questionText.length}`);
    }

    // Method 3: If still no good results, try more aggressive parsing
    if (Object.keys(choices).length < 3 && content.length > 100) {
      console.log('Using Method 3: Aggressive parsing');

      // Look for any A) B) C) patterns anywhere in text
      const aggressivePattern = /([A-E])\)\s*([^A-E\n]{10,200}?)(?=\s*[A-E]\)|$)/g;
      const aggressiveMatches = [...content.matchAll(aggressivePattern)];

      if (aggressiveMatches.length >= 3) {
        // Clear previous results
        Object.keys(choices).forEach(key => delete choices[key]);

        // Extract question text (everything before first choice)
        const firstChoiceIndex = aggressiveMatches[0].index;
        questionText = content.substring(0, firstChoiceIndex).trim();

        // Extract choices
        aggressiveMatches.forEach(match => {
          const choice = match[1];
          const text = match[2].trim();
          choices[choice] = text;
        });

        console.log(`Method 3 success: ${Object.keys(choices).length} choices extracted`);
      }
    }

    // Clean up and validate
    questionText = questionText.replace(/\s+/g, ' ').trim();
    Object.keys(choices).forEach(key => {
      choices[key] = choices[key].replace(/\s+/g, ' ').trim();
      // Remove empty choices
      if (!choices[key]) {
        delete choices[key];
      }
    });

    console.log(`Final result for Question ${questionNum}:`);
    console.log(`- Question text: ${questionText.length} chars`);
    console.log(`- Choices: ${Object.keys(choices).join(', ')}`);
    console.log(`- Context: ${context ? 'Yes' : 'No'}`);

    // Validation and fallbacks
    if (!questionText && Object.keys(choices).length === 0) {
      console.log(`Question ${questionNum}: Complete parsing failure`);
      return null;
    }

    // Ensure minimum choices
    const expectedChoices = ['A', 'B', 'C', 'D', 'E'];
    const missingChoices = expectedChoices.filter(c => !choices[c]);

    if (missingChoices.length > 2) {
      console.log(`Question ${questionNum}: Missing too many choices, adding placeholders`);
      missingChoices.forEach(choice => {
        choices[choice] = `Choice ${choice} (not found in PDF)`;
      });
    }

    // Ensure question text
    if (!questionText.trim()) {
      questionText = `Question ${questionNum} content (parsing incomplete - check PDF)`;
    }

    return {
      questionNumber: questionNum,
      totalQuestions: totalQ,
      questionId: questionId,
      context: context,
      questionText: questionText,
      choices: choices,
      isGroupQuestion: Boolean(context)
    };
  }

  extractAnswers(text) {
    const answers = {};

    console.log('Extracting answers from text...');

    // Find all question markers in answer file
    const questionMarkers = [];
    const patterns = [
      /Question #(\d+) of (\d+) Question ID: (\d+)/g,
      /Question #(\d+)/g
    ];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        questionMarkers.push({
          index: match.index,
          fullMatch: match[0],
          questionNum: parseInt(match[1])
        });
      }
    }

    console.log(`Found ${questionMarkers.length} question markers in answer file`);

    // Sort by position
    questionMarkers.sort((a, b) => a.index - b.index);

    // Extract answer content for each question
    for (let i = 0; i < questionMarkers.length; i++) {
      const marker = questionMarkers[i];
      const nextMarker = questionMarkers[i + 1];

      // Get content from current marker to next marker (or end of text)
      const startIndex = marker.index + marker.fullMatch.length;
      const endIndex = nextMarker ? nextMarker.index : text.length;
      const content = text.substring(startIndex, endIndex).trim();

      console.log(`Processing answer for question ${marker.questionNum}, content length: ${content.length}`);

      // Find correct answer
      let correctAnswer = null;
      const answerPatterns = [
        /The correct answer is ([A-E])\./i,
        /The correct answer is ([A-E])\s/i,
        /Correct answer:\s*([A-E])/i,
        /Answer:\s*([A-E])/i,
        /([A-E])\s*is correct/i,
        /Choice\s*([A-E])\s*is correct/i
      ];

      for (const pattern of answerPatterns) {
        const match = content.match(pattern);
        if (match) {
          correctAnswer = match[1].toUpperCase();
          console.log(`Found correct answer ${correctAnswer} for question ${marker.questionNum}`);
          break;
        }
      }

      // Find explanation - be more flexible
      let explanation = '';
      const explanationPatterns = [
        /Explanation[:\s]+(.*?)(?=Question #|\(Module|\Z)/is,
        /Explanation[:\s]+(.*?)(?=\n\s*\n|\Z)/is,
        /Explanation[:\s]+(.*)/is
      ];

      for (const pattern of explanationPatterns) {
        const match = content.match(pattern);
        if (match) {
          explanation = match[1].trim();
          // Clean up explanation
          explanation = explanation.replace(/\s+/g, ' ');
          // Remove common suffixes
          explanation = explanation.replace(/\(Module.*?\)$/i, '');
          explanation = explanation.replace(/\(LOS.*?\)$/i, '');
          explanation = explanation.trim();

          console.log(`Found explanation for question ${marker.questionNum}, length: ${explanation.length}`);
          break;
        }
      }

      // If no explanation found, try to extract everything after "Explanation"
      if (!explanation && content.toLowerCase().includes('explanation')) {
        const explIndex = content.toLowerCase().indexOf('explanation');
        if (explIndex !== -1) {
          explanation = content.substring(explIndex + 11).trim();
          explanation = explanation.replace(/\s+/g, ' ');
          console.log(`Fallback explanation extraction for question ${marker.questionNum}`);
        }
      }

      if (correctAnswer) {
        answers[marker.questionNum] = {
          correctAnswer: correctAnswer,
          explanation: explanation || 'No explanation available'
        };
      } else {
        console.log(`No correct answer found for question ${marker.questionNum}`);
      }
    }

    console.log(`Extracted answers for ${Object.keys(answers).length} questions`);
    return answers;
  }

  async parseFiles(questionFile, answerFile) {
    try {
      const [questions, answers] = await Promise.all([
        this.parseQuestionFile(questionFile),
        this.parseAnswerFile(answerFile)
      ]);
      
      return { questions, answers };
    } catch (error) {
      console.error('Error parsing files:', error);
      throw error;
    }
  }
}

export default CFAQuestionParser;
