// PDF Parser for CFA Questions and Answers
import * as pdfjsLib from 'pdfjs-dist';

// Set worker path - use the worker from the same version
pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;

class CFAQuestionParser {
  constructor() {
    this.questions = [];
    this.answers = {};
  }

  async parseQuestionFile(file) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;

      let fullText = '';

      // Extract text from all pages with better formatting
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();

        // Better text extraction preserving structure
        let pageText = '';
        let lastY = null;

        for (const item of textContent.items) {
          // Add line break if Y position changed significantly (new line)
          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {
            pageText += '\n';
          }

          pageText += item.str + ' ';
          lastY = item.transform[5];
        }

        fullText += pageText + '\n\n';
      }

      // Debug: Log extracted text
      console.log('Extracted text length:', fullText.length);
      console.log('First 2000 characters:', fullText.substring(0, 2000));

      // Parse questions from text
      const questions = this.extractQuestions(fullText);
      console.log('Extracted questions:', questions.length);

      return questions;
    } catch (error) {
      console.error('Error parsing question file:', error);
      throw error;
    }
  }

  async parseAnswerFile(file) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;

      let fullText = '';

      // Extract text from all pages with better formatting
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();

        // Better text extraction preserving structure
        let pageText = '';
        let lastY = null;

        for (const item of textContent.items) {
          // Add line break if Y position changed significantly (new line)
          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {
            pageText += '\n';
          }

          pageText += item.str + ' ';
          lastY = item.transform[5];
        }

        fullText += pageText + '\n\n';
      }

      console.log('Answer file text length:', fullText.length);
      console.log('Answer file preview:', fullText.substring(0, 1000));

      // Parse answers from text
      const answers = this.extractAnswers(fullText);
      console.log('Extracted answers:', Object.keys(answers).length);

      return answers;
    } catch (error) {
      console.error('Error parsing answer file:', error);
      throw error;
    }
  }

  extractQuestions(text) {
    const questions = [];

    console.log('Looking for question patterns in text...');

    // First, try to find all question markers
    const questionMarkers = [];
    const patterns = [
      /Question #(\d+) of (\d+) Question ID: (\d+)/g,
      /Question #(\d+) - (\d+) of (\d+) Question ID: (\d+)/g,
    ];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        questionMarkers.push({
          index: match.index,
          fullMatch: match[0],
          questionNum: parseInt(match[1]),
          endNum: match[2] ? parseInt(match[2]) : null,
          totalQ: parseInt(match[match.length - 2]),
          questionId: match[match.length - 1],
          isGroup: match[0].includes(' - ')
        });
      }
    }

    console.log(`Found ${questionMarkers.length} question markers`);

    if (questionMarkers.length === 0) {
      console.log('No question patterns found. Trying alternative extraction...');
      return this.extractQuestionsAlternative(text);
    }

    // Sort by position in text
    questionMarkers.sort((a, b) => a.index - b.index);

    // Extract content for each question
    for (let i = 0; i < questionMarkers.length; i++) {
      const marker = questionMarkers[i];
      const nextMarker = questionMarkers[i + 1];

      // Get content from current marker to next marker (or end of text)
      const startIndex = marker.index + marker.fullMatch.length;
      const endIndex = nextMarker ? nextMarker.index : text.length;
      const content = text.substring(startIndex, endIndex).trim();

      console.log(`Processing question ${marker.questionNum}, content length: ${content.length}`);

      if (marker.isGroup) {
        // Handle group questions
        const groupQuestions = this.extractGroupQuestions(
          content,
          marker.questionNum,
          marker.endNum,
          marker.totalQ,
          '' // Context will be extracted within the method
        );
        questions.push(...groupQuestions);
      } else {
        // Handle single question
        const questionData = this.parseSingleQuestion(
          content,
          marker.questionNum,
          marker.totalQ,
          marker.questionId
        );

        if (questionData) {
          questions.push(questionData);
        }
      }
    }

    console.log(`Extracted ${questions.length} questions total`);
    return questions;
  }

  extractQuestionsAlternative(text) {
    console.log('Using alternative extraction method...');
    const questions = [];

    // Look for any numbered patterns that might be questions
    const patterns = [
      /(\d+)\.\s+([^0-9]+?)(?=\d+\.|$)/gs,  // 1. Question text
      /Question\s+(\d+)[:\.]?\s*([^Q]+?)(?=Question\s+\d+|$)/gis,  // Question 1: text
    ];

    for (const pattern of patterns) {
      const matches = [...text.matchAll(pattern)];
      console.log(`Alternative pattern found ${matches.length} matches`);

      for (const match of matches) {
        const questionNum = parseInt(match[1]);
        const content = match[2].trim();

        if (content.length > 20) { // Filter out very short matches
          const questionData = this.parseSingleQuestion(
            content,
            questionNum,
            100, // Default total
            'alt-' + questionNum
          );

          if (questionData) {
            questions.push(questionData);
          }
        }
      }

      if (questions.length > 0) break; // Use first successful pattern
    }

    return questions;
  }

  extractGroupQuestions(content, startQ, endQ, totalQ, context) {
    const questions = [];
    
    // Split by individual question patterns within the group
    const subQuestionPattern = /Question #(\d+) - \d+ of \d+ Question ID: (\d+)\s*(.*?)(?=Question #\d+|$)/gs;
    let match;
    
    while ((match = subQuestionPattern.exec(content)) !== null) {
      const [, questionNum, questionId, questionContent] = match;
      
      const questionData = this.parseSingleQuestion(
        questionContent,
        parseInt(questionNum),
        totalQ,
        questionId,
        context
      );
      
      if (questionData) {
        questions.push(questionData);
      }
    }
    
    return questions;
  }

  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {
    if (!content.trim()) return null;

    console.log(`Parsing question ${questionNum}, content length: ${content.length}`);
    console.log(`Content preview: ${content.substring(0, 200)}...`);

    // Preserve line breaks for better parsing
    const originalContent = content;

    let questionText = '';
    const choices = {};

    // Find all choice markers in the content
    const choiceMarkers = [];
    const choicePattern = /([A-E])\)\s*/g;
    let match;

    while ((match = choicePattern.exec(content)) !== null) {
      choiceMarkers.push({
        choice: match[1],
        index: match.index,
        fullMatch: match[0]
      });
    }

    console.log(`Found ${choiceMarkers.length} choice markers for question ${questionNum}`);

    if (choiceMarkers.length >= 2) {
      // Extract question text (everything before first choice)
      questionText = content.substring(0, choiceMarkers[0].index).trim();

      // Extract each choice
      for (let i = 0; i < choiceMarkers.length; i++) {
        const marker = choiceMarkers[i];
        const nextMarker = choiceMarkers[i + 1];

        const startIndex = marker.index + marker.fullMatch.length;
        const endIndex = nextMarker ? nextMarker.index : content.length;
        const choiceText = content.substring(startIndex, endIndex).trim();

        if (choiceText.length > 0) {
          choices[marker.choice] = choiceText;
        }
      }
    } else {
      // Fallback: try to parse line by line
      console.log('Using line-by-line parsing for question', questionNum);

      const lines = originalContent.split('\n').map(line => line.trim()).filter(line => line);

      let currentChoice = null;
      let choiceText = '';
      let inChoices = false;

      for (const line of lines) {
        // Check if line starts with choice pattern
        const choiceMatch = line.match(/^([A-E])\)\s*(.*)/);

        if (choiceMatch) {
          // Save previous choice
          if (currentChoice) {
            choices[currentChoice] = choiceText.trim();
          }

          currentChoice = choiceMatch[1];
          choiceText = choiceMatch[2];
          inChoices = true;
        } else if (currentChoice && inChoices) {
          // Continue choice text
          choiceText += ' ' + line;
        } else if (!inChoices) {
          // Question text
          questionText += ' ' + line;
        }
      }

      // Save last choice
      if (currentChoice) {
        choices[currentChoice] = choiceText.trim();
      }
    }

    // Clean up question text
    questionText = questionText.replace(/\s+/g, ' ').trim();

    // Clean up choices
    Object.keys(choices).forEach(key => {
      choices[key] = choices[key].replace(/\s+/g, ' ').trim();
    });

    console.log(`Question ${questionNum} parsed:`, {
      questionTextLength: questionText.length,
      choicesCount: Object.keys(choices).length,
      choices: Object.keys(choices)
    });

    // Validate we have minimum required content
    if (!questionText.trim() && Object.keys(choices).length === 0) {
      console.log(`Question ${questionNum}: No content found`);
      return null;
    }

    // Ensure we have at least some choices
    if (Object.keys(choices).length === 0) {
      console.log(`Question ${questionNum}: No choices found, creating placeholders`);
      choices['A'] = 'Choice A (parsing failed)';
      choices['B'] = 'Choice B (parsing failed)';
      choices['C'] = 'Choice C (parsing failed)';
    }

    // Ensure we have question text
    if (!questionText.trim()) {
      questionText = `Question ${questionNum} (content parsing incomplete)`;
    }

    return {
      questionNumber: questionNum,
      totalQuestions: totalQ,
      questionId: questionId,
      context: context,
      questionText: questionText,
      choices: choices,
      isGroupQuestion: Boolean(context)
    };
  }

  extractAnswers(text) {
    const answers = {};

    console.log('Extracting answers from text...');

    // Find all question markers in answer file
    const questionMarkers = [];
    const patterns = [
      /Question #(\d+) of (\d+) Question ID: (\d+)/g,
      /Question #(\d+)/g
    ];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        questionMarkers.push({
          index: match.index,
          fullMatch: match[0],
          questionNum: parseInt(match[1])
        });
      }
    }

    console.log(`Found ${questionMarkers.length} question markers in answer file`);

    // Sort by position
    questionMarkers.sort((a, b) => a.index - b.index);

    // Extract answer content for each question
    for (let i = 0; i < questionMarkers.length; i++) {
      const marker = questionMarkers[i];
      const nextMarker = questionMarkers[i + 1];

      // Get content from current marker to next marker (or end of text)
      const startIndex = marker.index + marker.fullMatch.length;
      const endIndex = nextMarker ? nextMarker.index : text.length;
      const content = text.substring(startIndex, endIndex).trim();

      console.log(`Processing answer for question ${marker.questionNum}, content length: ${content.length}`);

      // Find correct answer
      let correctAnswer = null;
      const answerPatterns = [
        /The correct answer is ([A-E])\./i,
        /The correct answer is ([A-E])\s/i,
        /Correct answer:\s*([A-E])/i,
        /Answer:\s*([A-E])/i,
        /([A-E])\s*is correct/i,
        /Choice\s*([A-E])\s*is correct/i
      ];

      for (const pattern of answerPatterns) {
        const match = content.match(pattern);
        if (match) {
          correctAnswer = match[1].toUpperCase();
          console.log(`Found correct answer ${correctAnswer} for question ${marker.questionNum}`);
          break;
        }
      }

      // Find explanation - be more flexible
      let explanation = '';
      const explanationPatterns = [
        /Explanation[:\s]+(.*?)(?=Question #|\(Module|\Z)/is,
        /Explanation[:\s]+(.*?)(?=\n\s*\n|\Z)/is,
        /Explanation[:\s]+(.*)/is
      ];

      for (const pattern of explanationPatterns) {
        const match = content.match(pattern);
        if (match) {
          explanation = match[1].trim();
          // Clean up explanation
          explanation = explanation.replace(/\s+/g, ' ');
          // Remove common suffixes
          explanation = explanation.replace(/\(Module.*?\)$/i, '');
          explanation = explanation.replace(/\(LOS.*?\)$/i, '');
          explanation = explanation.trim();

          console.log(`Found explanation for question ${marker.questionNum}, length: ${explanation.length}`);
          break;
        }
      }

      // If no explanation found, try to extract everything after "Explanation"
      if (!explanation && content.toLowerCase().includes('explanation')) {
        const explIndex = content.toLowerCase().indexOf('explanation');
        if (explIndex !== -1) {
          explanation = content.substring(explIndex + 11).trim();
          explanation = explanation.replace(/\s+/g, ' ');
          console.log(`Fallback explanation extraction for question ${marker.questionNum}`);
        }
      }

      if (correctAnswer) {
        answers[marker.questionNum] = {
          correctAnswer: correctAnswer,
          explanation: explanation || 'No explanation available'
        };
      } else {
        console.log(`No correct answer found for question ${marker.questionNum}`);
      }
    }

    console.log(`Extracted answers for ${Object.keys(answers).length} questions`);
    return answers;
  }

  async parseFiles(questionFile, answerFile) {
    try {
      const [questions, answers] = await Promise.all([
        this.parseQuestionFile(questionFile),
        this.parseAnswerFile(answerFile)
      ]);
      
      return { questions, answers };
    } catch (error) {
      console.error('Error parsing files:', error);
      throw error;
    }
  }
}

export default CFAQuestionParser;
