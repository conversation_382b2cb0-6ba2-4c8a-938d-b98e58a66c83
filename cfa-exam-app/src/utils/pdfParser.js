// PDF Parser for CFA Questions and Answers
import * as pdfjsLib from 'pdfjs-dist';

// Set worker path
pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`;

class CFAQuestionParser {
  constructor() {
    this.questions = [];
    this.answers = {};
  }

  async parseQuestionFile(file) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;
      
      let fullText = '';
      
      // Extract text from all pages
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();
        const pageText = textContent.items.map(item => item.str).join(' ');
        fullText += pageText + '\n';
      }
      
      // Parse questions from text
      const questions = this.extractQuestions(fullText);
      return questions;
    } catch (error) {
      console.error('Error parsing question file:', error);
      throw error;
    }
  }

  async parseAnswerFile(file) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;
      
      let fullText = '';
      
      // Extract text from all pages
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();
        const pageText = textContent.items.map(item => item.str).join(' ');
        fullText += pageText + '\n';
      }
      
      // Parse answers from text
      const answers = this.extractAnswers(fullText);
      return answers;
    } catch (error) {
      console.error('Error parsing answer file:', error);
      throw error;
    }
  }

  extractQuestions(text) {
    const questions = [];
    
    // Clean text
    text = text.replace(/\s+/g, ' ').trim();
    
    // Split by Question # pattern
    const questionBlocks = text.split(/(?=Question #\d+)/);
    
    let currentContext = '';
    
    for (const block of questionBlocks) {
      if (!block.trim()) continue;
      
      // Check for single question pattern
      const singleMatch = block.match(/Question #(\d+) of (\d+) Question ID: (\d+)\s*(.*)/s);
      
      // Check for group question pattern
      const groupMatch = block.match(/Question #(\d+) - (\d+) of (\d+) Question ID: (\d+)\s*(.*)/s);
      
      if (groupMatch) {
        const [, startQ, endQ, totalQ, questionId, content] = groupMatch;
        
        // Extract context before the question range
        const contextMatch = content.match(/^(.*?)(?=Question #\d+ - \d+)/s);
        if (contextMatch) {
          currentContext = contextMatch[1].trim();
        }
        
        // Extract individual questions in the group
        const groupQuestions = this.extractGroupQuestions(
          content, 
          parseInt(startQ), 
          parseInt(endQ), 
          parseInt(totalQ), 
          currentContext
        );
        
        questions.push(...groupQuestions);
        
      } else if (singleMatch) {
        const [, questionNum, totalQ, questionId, content] = singleMatch;
        
        const questionData = this.parseSingleQuestion(
          content, 
          parseInt(questionNum), 
          parseInt(totalQ), 
          questionId
        );
        
        if (questionData) {
          questions.push(questionData);
        }
        
        // Reset context for single questions
        currentContext = '';
      }
    }
    
    return questions;
  }

  extractGroupQuestions(content, startQ, endQ, totalQ, context) {
    const questions = [];
    
    // Split by individual question patterns within the group
    const subQuestionPattern = /Question #(\d+) - \d+ of \d+ Question ID: (\d+)\s*(.*?)(?=Question #\d+|$)/gs;
    let match;
    
    while ((match = subQuestionPattern.exec(content)) !== null) {
      const [, questionNum, questionId, questionContent] = match;
      
      const questionData = this.parseSingleQuestion(
        questionContent,
        parseInt(questionNum),
        totalQ,
        questionId,
        context
      );
      
      if (questionData) {
        questions.push(questionData);
      }
    }
    
    return questions;
  }

  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {
    if (!content.trim()) return null;
    
    // Split content into lines
    const lines = content.split('\n').map(line => line.trim()).filter(line => line);
    
    let questionText = '';
    const choices = {};
    let currentChoice = null;
    let choiceText = '';
    
    for (const line of lines) {
      // Check if line starts with choice pattern (A), B), C), etc.
      const choiceMatch = line.match(/^([A-E])\)\s*(.*)/);
      
      if (choiceMatch) {
        // Save previous choice
        if (currentChoice) {
          choices[currentChoice] = choiceText.trim();
        }
        
        currentChoice = choiceMatch[1];
        choiceText = choiceMatch[2];
      } else if (currentChoice) {
        // Continue choice text
        choiceText += ' ' + line;
      } else {
        // Question text
        questionText += ' ' + line;
      }
    }
    
    // Save last choice
    if (currentChoice) {
      choices[currentChoice] = choiceText.trim();
    }
    
    if (!questionText.trim() || Object.keys(choices).length === 0) {
      return null;
    }
    
    return {
      questionNumber: questionNum,
      totalQuestions: totalQ,
      questionId: questionId,
      context: context,
      questionText: questionText.trim(),
      choices: choices,
      isGroupQuestion: Boolean(context)
    };
  }

  extractAnswers(text) {
    const answers = {};
    
    // Clean text
    text = text.replace(/\s+/g, ' ').trim();
    
    // Split by Question # pattern
    const questionBlocks = text.split(/Question #(\d+)/);
    
    for (let i = 1; i < questionBlocks.length; i += 2) {
      if (i + 1 < questionBlocks.length) {
        const questionNum = parseInt(questionBlocks[i]);
        const content = questionBlocks[i + 1];
        
        // Find correct answer
        let correctAnswer = null;
        const answerPatterns = [
          /The correct answer is ([A-E])/i,
          /Correct answer:\s*([A-E])/i,
          /Answer:\s*([A-E])/i,
          /([A-E])\s*is correct/i
        ];
        
        for (const pattern of answerPatterns) {
          const match = content.match(pattern);
          if (match) {
            correctAnswer = match[1].toUpperCase();
            break;
          }
        }
        
        // Find explanation
        let explanation = '';
        const explanationPatterns = [
          /Explanation[:\s]+(.*?)(?=Question #|$)/is,
          /Explanation[:\s]+(.*?)(?=\(Module|$)/is
        ];
        
        for (const pattern of explanationPatterns) {
          const match = content.match(pattern);
          if (match) {
            explanation = match[1].trim();
            break;
          }
        }
        
        if (correctAnswer) {
          answers[questionNum] = {
            correctAnswer: correctAnswer,
            explanation: explanation
          };
        }
      }
    }
    
    return answers;
  }

  async parseFiles(questionFile, answerFile) {
    try {
      const [questions, answers] = await Promise.all([
        this.parseQuestionFile(questionFile),
        this.parseAnswerFile(answerFile)
      ]);
      
      return { questions, answers };
    } catch (error) {
      console.error('Error parsing files:', error);
      throw error;
    }
  }
}

export default CFAQuestionParser;
