// PDF Parser for CFA Questions and Answers
import * as pdfjsLib from 'pdfjs-dist';

// Set worker path - use the worker from the same version
pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;

class CFAQuestionParser {
  constructor() {
    this.questions = [];
    this.answers = {};
  }

  async parseQuestionFile(file) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;

      let fullText = '';

      // Extract text from all pages
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();
        const pageText = textContent.items.map(item => item.str).join(' ');
        fullText += pageText + '\n';
      }

      // Debug: Log extracted text
      console.log('Extracted text length:', fullText.length);
      console.log('First 1000 characters:', fullText.substring(0, 1000));

      // Parse questions from text
      const questions = this.extractQuestions(fullText);
      console.log('Extracted questions:', questions.length);

      return questions;
    } catch (error) {
      console.error('Error parsing question file:', error);
      throw error;
    }
  }

  async parseAnswerFile(file) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;
      
      let fullText = '';
      
      // Extract text from all pages
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();
        const pageText = textContent.items.map(item => item.str).join(' ');
        fullText += pageText + '\n';
      }
      
      // Parse answers from text
      const answers = this.extractAnswers(fullText);
      return answers;
    } catch (error) {
      console.error('Error parsing answer file:', error);
      throw error;
    }
  }

  extractQuestions(text) {
    const questions = [];

    // Clean text but preserve line breaks for better parsing
    text = text.replace(/\s+/g, ' ').trim();

    console.log('Looking for question patterns in text...');

    // Try multiple patterns for question detection
    const patterns = [
      /Question #(\d+) of (\d+) Question ID: (\d+)/g,
      /Question #(\d+) - (\d+) of (\d+) Question ID: (\d+)/g,
      /Question #(\d+) of (\d+)/g,
      /Question #(\d+)/g
    ];

    let foundPatterns = [];
    for (const pattern of patterns) {
      const matches = [...text.matchAll(pattern)];
      if (matches.length > 0) {
        console.log(`Found ${matches.length} matches for pattern:`, pattern.source);
        foundPatterns.push(...matches);
      }
    }

    if (foundPatterns.length === 0) {
      console.log('No question patterns found. Trying alternative extraction...');
      return this.extractQuestionsAlternative(text);
    }

    // Split by Question # pattern
    const questionBlocks = text.split(/(?=Question #\d+)/);
    console.log(`Split into ${questionBlocks.length} blocks`);

    let currentContext = '';

    for (const block of questionBlocks) {
      if (!block.trim()) continue;

      console.log('Processing block:', block.substring(0, 100) + '...');

      // Check for single question pattern
      const singleMatch = block.match(/Question #(\d+) of (\d+) Question ID: (\d+)\s*(.*)/s);

      // Check for group question pattern
      const groupMatch = block.match(/Question #(\d+) - (\d+) of (\d+) Question ID: (\d+)\s*(.*)/s);

      if (groupMatch) {
        const [, startQ, endQ, totalQ, , content] = groupMatch;
        console.log(`Found group question: ${startQ}-${endQ} of ${totalQ}`);

        // Extract context before the question range
        const contextMatch = content.match(/^(.*?)(?=Question #\d+ - \d+)/s);
        if (contextMatch) {
          currentContext = contextMatch[1].trim();
        }

        // Extract individual questions in the group
        const groupQuestions = this.extractGroupQuestions(
          content,
          parseInt(startQ),
          parseInt(endQ),
          parseInt(totalQ),
          currentContext
        );

        questions.push(...groupQuestions);

      } else if (singleMatch) {
        const [, questionNum, totalQ, questionId, content] = singleMatch;
        console.log(`Found single question: ${questionNum} of ${totalQ}`);

        const questionData = this.parseSingleQuestion(
          content,
          parseInt(questionNum),
          parseInt(totalQ),
          questionId
        );

        if (questionData) {
          questions.push(questionData);
        }

        // Reset context for single questions
        currentContext = '';
      } else {
        // Try simpler pattern
        const simpleMatch = block.match(/Question #(\d+)\s*(.*)/s);
        if (simpleMatch) {
          const [, questionNum, content] = simpleMatch;
          console.log(`Found simple question: ${questionNum}`);

          const questionData = this.parseSingleQuestion(
            content,
            parseInt(questionNum),
            100, // Default total
            'unknown'
          );

          if (questionData) {
            questions.push(questionData);
          }
        }
      }
    }

    console.log(`Extracted ${questions.length} questions total`);
    return questions;
  }

  extractQuestionsAlternative(text) {
    console.log('Using alternative extraction method...');
    const questions = [];

    // Look for any numbered patterns that might be questions
    const patterns = [
      /(\d+)\.\s+([^0-9]+?)(?=\d+\.|$)/gs,  // 1. Question text
      /Question\s+(\d+)[:\.]?\s*([^Q]+?)(?=Question\s+\d+|$)/gis,  // Question 1: text
    ];

    for (const pattern of patterns) {
      const matches = [...text.matchAll(pattern)];
      console.log(`Alternative pattern found ${matches.length} matches`);

      for (const match of matches) {
        const questionNum = parseInt(match[1]);
        const content = match[2].trim();

        if (content.length > 20) { // Filter out very short matches
          const questionData = this.parseSingleQuestion(
            content,
            questionNum,
            100, // Default total
            'alt-' + questionNum
          );

          if (questionData) {
            questions.push(questionData);
          }
        }
      }

      if (questions.length > 0) break; // Use first successful pattern
    }

    return questions;
  }

  extractGroupQuestions(content, startQ, endQ, totalQ, context) {
    const questions = [];
    
    // Split by individual question patterns within the group
    const subQuestionPattern = /Question #(\d+) - \d+ of \d+ Question ID: (\d+)\s*(.*?)(?=Question #\d+|$)/gs;
    let match;
    
    while ((match = subQuestionPattern.exec(content)) !== null) {
      const [, questionNum, questionId, questionContent] = match;
      
      const questionData = this.parseSingleQuestion(
        questionContent,
        parseInt(questionNum),
        totalQ,
        questionId,
        context
      );
      
      if (questionData) {
        questions.push(questionData);
      }
    }
    
    return questions;
  }

  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {
    if (!content.trim()) return null;

    console.log(`Parsing question ${questionNum}, content length: ${content.length}`);

    // Clean content and split into parts
    content = content.replace(/\s+/g, ' ').trim();

    let questionText = '';
    const choices = {};

    // Try to find choices first using multiple patterns
    const choicePatterns = [
      /([A-E])\)\s*([^A-E\)]+?)(?=[A-E]\)|$)/g,  // A) text B) text
      /([A-E])\s*\)\s*([^A-E]+?)(?=[A-E]\s*\)|$)/g,  // A ) text B ) text
      /([A-E])\.?\s*([^A-E\.]+?)(?=[A-E]\.?\s|$)/g,  // A. text B. text
    ];

    let foundChoices = false;
    for (const pattern of choicePatterns) {
      const matches = [...content.matchAll(pattern)];
      if (matches.length >= 2) { // At least 2 choices
        console.log(`Found ${matches.length} choices with pattern`);
        for (const match of matches) {
          const choice = match[1];
          const text = match[2].trim();
          if (text.length > 0) {
            choices[choice] = text;
          }
        }
        foundChoices = true;
        break;
      }
    }

    if (!foundChoices) {
      console.log('No choices found, trying line-by-line parsing...');

      // Fallback: split by lines and look for choices
      const lines = content.split(/[.\n]/).map(line => line.trim()).filter(line => line);

      let currentChoice = null;
      let choiceText = '';

      for (const line of lines) {
        // Check if line starts with choice pattern
        const choiceMatch = line.match(/^([A-E])\)?\s*(.*)/);

        if (choiceMatch && choiceMatch[2].length > 0) {
          // Save previous choice
          if (currentChoice) {
            choices[currentChoice] = choiceText.trim();
          }

          currentChoice = choiceMatch[1];
          choiceText = choiceMatch[2];
        } else if (currentChoice && line.length > 0) {
          // Continue choice text
          choiceText += ' ' + line;
        } else if (!currentChoice && line.length > 0) {
          // Question text
          questionText += ' ' + line;
        }
      }

      // Save last choice
      if (currentChoice) {
        choices[currentChoice] = choiceText.trim();
      }
    } else {
      // Extract question text (everything before first choice)
      const firstChoiceIndex = content.search(/[A-E]\)/);
      if (firstChoiceIndex > 0) {
        questionText = content.substring(0, firstChoiceIndex).trim();
      } else {
        questionText = content;
      }
    }

    console.log(`Question ${questionNum}: text length=${questionText.length}, choices=${Object.keys(choices).length}`);

    // Validate we have minimum required content
    if (!questionText.trim() && Object.keys(choices).length === 0) {
      console.log(`Question ${questionNum}: No content found`);
      return null;
    }

    // If no question text but have choices, use first part of content
    if (!questionText.trim() && Object.keys(choices).length > 0) {
      questionText = "Question content not clearly separated from choices.";
    }

    // If no choices but have question text, create dummy choices
    if (questionText.trim() && Object.keys(choices).length === 0) {
      choices['A'] = 'Choice A (not parsed)';
      choices['B'] = 'Choice B (not parsed)';
      choices['C'] = 'Choice C (not parsed)';
    }

    return {
      questionNumber: questionNum,
      totalQuestions: totalQ,
      questionId: questionId,
      context: context,
      questionText: questionText.trim(),
      choices: choices,
      isGroupQuestion: Boolean(context)
    };
  }

  extractAnswers(text) {
    const answers = {};
    
    // Clean text
    text = text.replace(/\s+/g, ' ').trim();
    
    // Split by Question # pattern
    const questionBlocks = text.split(/Question #(\d+)/);
    
    for (let i = 1; i < questionBlocks.length; i += 2) {
      if (i + 1 < questionBlocks.length) {
        const questionNum = parseInt(questionBlocks[i]);
        const content = questionBlocks[i + 1];
        
        // Find correct answer
        let correctAnswer = null;
        const answerPatterns = [
          /The correct answer is ([A-E])/i,
          /Correct answer:\s*([A-E])/i,
          /Answer:\s*([A-E])/i,
          /([A-E])\s*is correct/i
        ];
        
        for (const pattern of answerPatterns) {
          const match = content.match(pattern);
          if (match) {
            correctAnswer = match[1].toUpperCase();
            break;
          }
        }
        
        // Find explanation
        let explanation = '';
        const explanationPatterns = [
          /Explanation[:\s]+(.*?)(?=Question #|$)/is,
          /Explanation[:\s]+(.*?)(?=\(Module|$)/is
        ];
        
        for (const pattern of explanationPatterns) {
          const match = content.match(pattern);
          if (match) {
            explanation = match[1].trim();
            break;
          }
        }
        
        if (correctAnswer) {
          answers[questionNum] = {
            correctAnswer: correctAnswer,
            explanation: explanation
          };
        }
      }
    }
    
    return answers;
  }

  async parseFiles(questionFile, answerFile) {
    try {
      const [questions, answers] = await Promise.all([
        this.parseQuestionFile(questionFile),
        this.parseAnswerFile(answerFile)
      ]);
      
      return { questions, answers };
    } catch (error) {
      console.error('Error parsing files:', error);
      throw error;
    }
  }
}

export default CFAQuestionParser;
