// PDF Parser for CFA Questions and Answers
import * as pdfjsLib from 'pdfjs-dist';

// Set worker path - use the worker from the same version
pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;

class CFAQuestionParser {
  constructor() {
    this.questions = [];
    this.answers = {};
  }

  async parseQuestionFile(file) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;

      let fullText = '';

      // Extract text from all pages with better formatting
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();

        // Better text extraction preserving structure
        let pageText = '';
        let lastY = null;

        for (const item of textContent.items) {
          // Add line break if Y position changed significantly (new line)
          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {
            pageText += '\n';
          }

          pageText += item.str + ' ';
          lastY = item.transform[5];
        }

        fullText += pageText + '\n\n';
      }

      // Debug: Log extracted text
      console.log('Extracted text length:', fullText.length);
      console.log('First 2000 characters:', fullText.substring(0, 2000));

      // Parse questions from text
      const questions = this.extractQuestions(fullText);
      console.log('Extracted questions:', questions.length);

      return questions;
    } catch (error) {
      console.error('Error parsing question file:', error);
      throw error;
    }
  }

  async parseAnswerFile(file) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;

      let fullText = '';

      // Extract text from all pages with better formatting
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();

        // Better text extraction preserving structure
        let pageText = '';
        let lastY = null;

        for (const item of textContent.items) {
          // Add line break if Y position changed significantly (new line)
          if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {
            pageText += '\n';
          }

          pageText += item.str + ' ';
          lastY = item.transform[5];
        }

        fullText += pageText + '\n\n';
      }

      console.log('Answer file text length:', fullText.length);
      console.log('Answer file preview:', fullText.substring(0, 1000));

      // Parse answers from text
      const answers = this.extractAnswers(fullText);
      console.log('Extracted answers:', Object.keys(answers).length);

      return answers;
    } catch (error) {
      console.error('Error parsing answer file:', error);
      throw error;
    }
  }

  extractQuestions(text) {
    const questions = [];

    console.log('=== STARTING QUESTION EXTRACTION ===');
    console.log('Text length:', text.length);

    // First, let's find all question markers and their positions
    const questionMarkers = [];
    const questionPattern = /Question #(\d+)(?:\s*-\s*(\d+))?\s+of\s+(\d+)(?:\s+Question\s+ID:\s*(\d+))?/g;

    let match;
    while ((match = questionPattern.exec(text)) !== null) {
      const startQ = parseInt(match[1]);
      const endQ = match[2] ? parseInt(match[2]) : null;
      const totalQ = parseInt(match[3]);
      const questionId = match[4] || `q${startQ}`;

      questionMarkers.push({
        startQ,
        endQ,
        totalQ,
        questionId,
        isGroup: endQ !== null,
        index: match.index,
        fullMatch: match[0]
      });
    }

    console.log(`Found ${questionMarkers.length} question markers:`, questionMarkers);

    // Process each marker
    for (let i = 0; i < questionMarkers.length; i++) {
      const marker = questionMarkers[i];
      const nextMarker = questionMarkers[i + 1];

      // Extract content from this marker to the next (or end of text)
      const startIndex = marker.index + marker.fullMatch.length;
      const endIndex = nextMarker ? nextMarker.index : text.length;
      const content = text.substring(startIndex, endIndex).trim();

      console.log(`\n--- Processing marker ${i + 1}: Q${marker.startQ}${marker.endQ ? `-${marker.endQ}` : ''} ---`);
      console.log(`Content length: ${content.length}`);
      console.log(`Content preview: ${content.substring(0, 200)}...`);

      if (marker.isGroup) {
        // This is a group of questions with shared context
        console.log(`Processing question group ${marker.startQ}-${marker.endQ}`);

        // Extract shared context and individual questions
        const groupQuestions = this.extractGroupQuestions(
          content,
          marker.startQ,
          marker.endQ,
          marker.totalQ,
          marker.questionId
        );

        questions.push(...groupQuestions);
      } else {
        // Single question
        console.log(`Processing single question ${marker.startQ}`);

        const questionData = this.parseSingleQuestion(
          content,
          marker.startQ,
          marker.totalQ,
          marker.questionId
        );

        if (questionData) {
          questions.push(questionData);
        }
      }
    }

    // Sort questions by number
    questions.sort((a, b) => a.questionNumber - b.questionNumber);

    console.log(`\n=== EXTRACTION COMPLETE ===`);
    console.log(`Total questions extracted: ${questions.length}`);
    console.log('Question numbers:', questions.map(q => q.questionNumber));

    return questions;
  }

  extractQuestionsAlternative(text) {
    console.log('Using alternative extraction method...');
    const questions = [];

    // Look for any numbered patterns that might be questions
    const patterns = [
      /(\d+)\.\s+([^0-9]+?)(?=\d+\.|$)/gs,  // 1. Question text
      /Question\s+(\d+)[:.?]?\s*([^Q]+?)(?=Question\s+\d+|$)/gis,  // Question 1: text
    ];

    for (const pattern of patterns) {
      const matches = [...text.matchAll(pattern)];
      console.log(`Alternative pattern found ${matches.length} matches`);

      for (const match of matches) {
        const questionNum = parseInt(match[1]);
        const content = match[2].trim();

        if (content.length > 20) { // Filter out very short matches
          const questionData = this.parseSingleQuestion(
            content,
            questionNum,
            100, // Default total
            'alt-' + questionNum
          );

          if (questionData) {
            questions.push(questionData);
          }
        }
      }

      if (questions.length > 0) break; // Use first successful pattern
    }

    return questions;
  }

  extractGroupQuestions(content, startQ, endQ, totalQ, questionId) {
    const questions = [];

    console.log(`\n=== EXTRACTING GROUP QUESTIONS ${startQ}-${endQ} ===`);
    console.log('Group content length:', content.length);
    console.log('Group content preview:', content.substring(0, 300));

    // Step 1: Extract shared reading context
    // The context is everything before the first individual question
    let sharedContext = '';
    let remainingContent = content;

    // Look for the first individual question marker (like "6." or "Question 6")
    const firstQuestionPatterns = [
      new RegExp(`^(.*?)(?=\\b${startQ}\\.)`, 's'),  // Everything before "6."
      new RegExp(`^(.*?)(?=Question\\s+${startQ}\\b)`, 'is'),  // Everything before "Question 6"
      new RegExp(`^(.*?)(?=\\b${startQ}\\s*\\))`, 's'),  // Everything before "6)"
    ];

    for (const pattern of firstQuestionPatterns) {
      const match = content.match(pattern);
      if (match && match[1].trim().length > 100) {  // Ensure substantial context
        sharedContext = match[1].trim();
        remainingContent = content.substring(match[1].length);
        console.log(`Found shared context (${sharedContext.length} chars):`, sharedContext.substring(0, 150) + '...');
        break;
      }
    }

    // If no clear context separation, try to identify context by looking for reading material
    if (!sharedContext && content.length > 500) {
      // Look for typical reading indicators
      const contextIndicators = [
        /^(.*?(?:company|firm|portfolio|investment|analysis|data|information|table|exhibit).*?)(?=\d+\.)/is,
        /^(.*?(?:following|given|based on|consider|assume).*?)(?=\d+\.)/is
      ];

      for (const pattern of contextIndicators) {
        const match = content.match(pattern);
        if (match && match[1].trim().length > 100) {
          sharedContext = match[1].trim();
          remainingContent = content.substring(match[1].length);
          console.log(`Found context via indicators (${sharedContext.length} chars):`, sharedContext.substring(0, 150) + '...');
          break;
        }
      }
    }

    // Step 2: Extract individual questions
    console.log(`Looking for individual questions ${startQ} to ${endQ}`);

    for (let qNum = startQ; qNum <= endQ; qNum++) {
      console.log(`\n--- Extracting Question ${qNum} ---`);

      // Multiple patterns to find individual questions
      const questionPatterns = [
        new RegExp(`\\b${qNum}\\.\\s*(.*?)(?=\\b${qNum + 1}\\.|$)`, 's'),  // "6. content"
        new RegExp(`Question\\s+${qNum}[:\\.]?\\s*(.*?)(?=Question\\s+${qNum + 1}|$)`, 'is'),  // "Question 6: content"
        new RegExp(`${qNum}\\)\\s*(.*?)(?=${qNum + 1}\\)|$)`, 's'),  // "6) content"
        new RegExp(`^.*?${qNum}[^\\d]*?(.*?)(?=${qNum + 1}[^\\d]|$)`, 's')  // Flexible pattern
      ];

      let questionContent = '';

      for (let i = 0; i < questionPatterns.length; i++) {
        const pattern = questionPatterns[i];
        const match = remainingContent.match(pattern);
        if (match && match[1].trim().length > 20) {
          questionContent = match[1].trim();
          console.log(`Found Q${qNum} using pattern ${i}: ${questionContent.substring(0, 100)}...`);
          break;
        }
      }

      // If still no content, try searching in the full content
      if (!questionContent) {
        console.log(`Trying full content search for Q${qNum}`);
        for (let i = 0; i < questionPatterns.length; i++) {
          const pattern = questionPatterns[i];
          const match = content.match(pattern);
          if (match && match[1].trim().length > 20) {
            questionContent = match[1].trim();
            console.log(`Found Q${qNum} in full content using pattern ${i}`);
            break;
          }
        }
      }

      if (questionContent) {
        const questionData = this.parseSingleQuestion(
          questionContent,
          qNum,
          totalQ,
          `${questionId}-${qNum}`,
          sharedContext
        );

        if (questionData) {
          questions.push(questionData);
          console.log(`Successfully extracted Q${qNum}`);
        } else {
          console.log(`Failed to parse Q${qNum} content`);
        }
      } else {
        console.log(`No content found for Q${qNum}`);
      }
    }

    console.log(`\n=== GROUP EXTRACTION COMPLETE ===`);
    console.log(`Extracted ${questions.length} questions from group ${startQ}-${endQ}`);
    console.log(`Shared context length: ${sharedContext.length}`);

    return questions;
  }

  parseSingleQuestion(content, questionNum, totalQ, questionId, context = '') {
    if (!content.trim()) return null;

    console.log(`\n=== Parsing Question ${questionNum} ===`);
    console.log(`Content length: ${content.length}`);
    console.log(`Content preview: ${content.substring(0, 300)}...`);

    let questionText = '';
    const choices = {};

    // Method 1: Look for choice patterns A) B) C) D) E)
    const choicePattern = /([A-E])\)\s*([^A-E]*?)(?=\s*[A-E]\)|$)/g;
    const choiceMatches = [...content.matchAll(choicePattern)];

    console.log(`Found ${choiceMatches.length} choice matches`);

    if (choiceMatches.length >= 3) {
      // Extract question text (everything before first choice)
      const firstChoiceIndex = choiceMatches[0].index;
      questionText = content.substring(0, firstChoiceIndex).trim();

      // Extract choices
      choiceMatches.forEach(match => {
        const choice = match[1];
        const text = match[2].trim();
        if (text.length > 0) {
          choices[choice] = text;
        }
      });

      console.log(`Method 1 success: ${Object.keys(choices).length} choices extracted`);
    } else {
      // Method 2: Line-by-line parsing with better logic
      console.log('Using Method 2: Line-by-line parsing');

      const lines = content.split(/\n/).map(line => line.trim()).filter(line => line.length > 0);

      let currentChoice = null;
      let choiceText = '';
      let questionLines = [];
      let foundFirstChoice = false;

      for (const line of lines) {
        // Check for choice pattern at start of line
        const choiceMatch = line.match(/^([A-E])\)\s*(.*)/);

        if (choiceMatch) {
          // Save previous choice if exists
          if (currentChoice && choiceText.trim()) {
            choices[currentChoice] = choiceText.trim();
          }

          currentChoice = choiceMatch[1];
          choiceText = choiceMatch[2];
          foundFirstChoice = true;

        } else if (currentChoice && foundFirstChoice) {
          // Continue current choice text
          choiceText += ' ' + line;

        } else if (!foundFirstChoice) {
          // Still in question text area
          questionLines.push(line);
        }
      }

      // Save last choice
      if (currentChoice && choiceText.trim()) {
        choices[currentChoice] = choiceText.trim();
      }

      questionText = questionLines.join(' ').trim();
      console.log(`Method 2 result: ${Object.keys(choices).length} choices, question length: ${questionText.length}`);
    }

    // Method 3: If still no good results, try more aggressive parsing
    if (Object.keys(choices).length < 3 && content.length > 100) {
      console.log('Using Method 3: Aggressive parsing');

      // Look for any A) B) C) patterns anywhere in text
      const aggressivePattern = /([A-E])\)\s*([^A-E\n]{10,200}?)(?=\s*[A-E]\)|$)/g;
      const aggressiveMatches = [...content.matchAll(aggressivePattern)];

      if (aggressiveMatches.length >= 3) {
        // Clear previous results
        Object.keys(choices).forEach(key => delete choices[key]);

        // Extract question text (everything before first choice)
        const firstChoiceIndex = aggressiveMatches[0].index;
        questionText = content.substring(0, firstChoiceIndex).trim();

        // Extract choices
        aggressiveMatches.forEach(match => {
          const choice = match[1];
          const text = match[2].trim();
          choices[choice] = text;
        });

        console.log(`Method 3 success: ${Object.keys(choices).length} choices extracted`);
      }
    }

    // Clean up and validate
    questionText = questionText.replace(/\s+/g, ' ').trim();
    Object.keys(choices).forEach(key => {
      choices[key] = choices[key].replace(/\s+/g, ' ').trim();
      // Remove empty choices
      if (!choices[key]) {
        delete choices[key];
      }
    });

    console.log(`Final result for Question ${questionNum}:`);
    console.log(`- Question text: ${questionText.length} chars`);
    console.log(`- Choices: ${Object.keys(choices).join(', ')}`);
    console.log(`- Context: ${context ? 'Yes' : 'No'}`);

    // Validation and fallbacks
    if (!questionText && Object.keys(choices).length === 0) {
      console.log(`Question ${questionNum}: Complete parsing failure`);
      return null;
    }

    // Ensure minimum choices
    const expectedChoices = ['A', 'B', 'C', 'D', 'E'];
    const missingChoices = expectedChoices.filter(c => !choices[c]);

    if (missingChoices.length > 2) {
      console.log(`Question ${questionNum}: Missing too many choices, adding placeholders`);
      missingChoices.forEach(choice => {
        choices[choice] = `Choice ${choice} (not found in PDF)`;
      });
    }

    // Ensure question text
    if (!questionText.trim()) {
      questionText = `Question ${questionNum} content (parsing incomplete - check PDF)`;
    }

    return {
      questionNumber: questionNum,
      totalQuestions: totalQ,
      questionId: questionId,
      context: context,
      questionText: questionText,
      choices: choices,
      isGroupQuestion: Boolean(context)
    };
  }

  extractAnswers(text) {
    const answers = {};

    console.log('\n=== STARTING ANSWER EXTRACTION ===');
    console.log('Answer file text length:', text.length);
    console.log('Answer file preview:', text.substring(0, 500));

    // Step 1: Find all question markers in answer file
    const questionMarkers = [];

    // More comprehensive patterns for answer file
    const markerPatterns = [
      /Question #(\d+) of (\d+) Question ID: (\d+)/g,
      /Question #(\d+) of (\d+)/g,
      /Question #(\d+)/g,
      /Question\s+(\d+)/g
    ];

    for (const pattern of markerPatterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const questionNum = parseInt(match[1]);
        if (!questionMarkers.find(m => m.questionNum === questionNum)) {
          questionMarkers.push({
            index: match.index,
            fullMatch: match[0],
            questionNum: questionNum
          });
        }
      }
    }

    console.log(`Found ${questionMarkers.length} question markers in answer file:`,
                questionMarkers.map(m => m.questionNum).sort((a, b) => a - b));

    // Sort by position in text
    questionMarkers.sort((a, b) => a.index - b.index);

    // Step 2: Extract answer content for each question
    for (let i = 0; i < questionMarkers.length; i++) {
      const marker = questionMarkers[i];
      const nextMarker = questionMarkers[i + 1];

      // Get content from current marker to next marker (or end of text)
      const startIndex = marker.index + marker.fullMatch.length;
      const endIndex = nextMarker ? nextMarker.index : text.length;
      const content = text.substring(startIndex, endIndex).trim();

      console.log(`\n--- Processing Answer for Question ${marker.questionNum} ---`);
      console.log(`Content length: ${content.length}`);
      console.log(`Content preview: ${content.substring(0, 300)}...`);

      // Step 3: Find correct answer with multiple patterns
      let correctAnswer = null;
      const answerPatterns = [
        /The correct answer is\s*([A-E])\.?/i,
        /The correct answer is\s*([A-E])\s/i,
        /Correct answer:\s*([A-E])\.?/i,
        /Correct answer\s*is\s*([A-E])\.?/i,
        /Answer:\s*([A-E])\.?/i,
        /Answer\s*is\s*([A-E])\.?/i,
        /([A-E])\s*is\s*correct/i,
        /([A-E])\s*is\s*the\s*correct/i,
        /Choice\s*([A-E])\s*is\s*correct/i,
        /Option\s*([A-E])\s*is\s*correct/i,
        /\b([A-E])\s*\.\s*$/m,  // Single letter at end of line
        /^([A-E])$/m  // Single letter on its own line
      ];

      for (let j = 0; j < answerPatterns.length; j++) {
        const pattern = answerPatterns[j];
        const match = content.match(pattern);
        if (match) {
          correctAnswer = match[1].toUpperCase();
          console.log(`Found correct answer "${correctAnswer}" for Q${marker.questionNum} using pattern ${j}: ${pattern.source}`);
          break;
        }
      }

      // If still no answer, try more aggressive search
      if (!correctAnswer) {
        console.log(`No answer found with standard patterns, trying aggressive search for Q${marker.questionNum}`);

        // Look for any single letter A-E that appears prominently
        const aggressivePatterns = [
          /\b([A-E])\b/g  // Any single letter A-E
        ];

        for (const pattern of aggressivePatterns) {
          const matches = [...content.matchAll(pattern)];
          if (matches.length > 0) {
            // Take the first occurrence
            correctAnswer = matches[0][1].toUpperCase();
            console.log(`Found answer "${correctAnswer}" for Q${marker.questionNum} via aggressive search`);
            break;
          }
        }
      }

      // Step 4: Extract explanation
      let explanation = '';

      // Multiple patterns to find explanation
      const explanationPatterns = [
        /Explanation[:\s]*\n?(.*?)(?=Question #|$)/is,
        /Explanation[:\s]+(.*?)(?=\(Module|$)/is,
        /Explanation[:\s]+(.*?)(?=\n\s*\n|$)/is,
        /Explanation[:\s]+(.*)/is
      ];

      for (let j = 0; j < explanationPatterns.length; j++) {
        const pattern = explanationPatterns[j];
        const match = content.match(pattern);
        if (match && match[1].trim().length > 10) {
          explanation = match[1].trim();
          console.log(`Found explanation for Q${marker.questionNum} using pattern ${j}`);
          break;
        }
      }

      // Manual explanation extraction if patterns fail
      if (!explanation) {
        const explKeywords = ['explanation', 'Explanation', 'EXPLANATION'];
        for (const keyword of explKeywords) {
          const explIndex = content.toLowerCase().indexOf(keyword.toLowerCase());
          if (explIndex !== -1) {
            let afterExpl = content.substring(explIndex + keyword.length).trim();
            afterExpl = afterExpl.replace(/^[:\s\n]+/, '');

            const nextQuestionIndex = afterExpl.search(/Question #\d+/i);
            if (nextQuestionIndex !== -1) {
              afterExpl = afterExpl.substring(0, nextQuestionIndex);
            }

            if (afterExpl.length > 20) {
              explanation = afterExpl.trim();
              console.log(`Manual explanation extraction for Q${marker.questionNum}`);
              break;
            }
          }
        }
      }

      // Clean up explanation
      if (explanation) {
        explanation = explanation.replace(/\s+/g, ' ');
        explanation = explanation.replace(/^\s*[:\-\s]+/, '');
        explanation = explanation.replace(/\(Module.*?\)$/i, '');
        explanation = explanation.replace(/\(LOS.*?\)$/i, '');
        explanation = explanation.replace(/\(Reading.*?\)$/i, '');
        explanation = explanation.replace(/\s+\d+\s*$/, '');
        explanation = explanation.trim();
      }

      // Step 5: Store the answer data
      if (correctAnswer) {
        answers[marker.questionNum] = {
          correctAnswer: correctAnswer,
          explanation: explanation || 'No explanation available'
        };
        console.log(`✓ Successfully extracted answer for Q${marker.questionNum}: ${correctAnswer}`);
      } else {
        console.log(`✗ Failed to find correct answer for Q${marker.questionNum}`);
        console.log(`Raw content for debugging: ${content.substring(0, 200)}...`);
      }
    }

    console.log(`\n=== ANSWER EXTRACTION COMPLETE ===`);
    console.log(`Successfully extracted answers for ${Object.keys(answers).length} questions`);
    console.log(`Answer summary:`, Object.keys(answers).sort((a, b) => a - b).map(q => `Q${q}:${answers[q].correctAnswer}`));

    return answers;
  }

  async parseFiles(questionFile, answerFile) {
    try {
      const [questions, answers] = await Promise.all([
        this.parseQuestionFile(questionFile),
        this.parseAnswerFile(answerFile)
      ]);
      
      return { questions, answers };
    } catch (error) {
      console.error('Error parsing files:', error);
      throw error;
    }
  }
}

export default CFAQuestionParser;
